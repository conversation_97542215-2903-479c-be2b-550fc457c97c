<template>
    <view class='save_doc_list'>
        <NavBar :title="_getDocsParams?.title || ''" :clickLeft="clickLeft" />
        <div class="docs_list">
            <div class="docs_item" v-for="(item, index) in state.docsList" :key="index" @click="handleSaveDoc(item)">
                <image class="docs_item_icon" src="https://file.1d1j.cn/cloud-mobile/officialDocs/docs.png"
                    mode="aspectFit" />
                <div class="docs_item_title">{{ item.formName }}</div>
            </div>
        </div>
        <yd-empty class="empty" v-if="!state.loading && !state.docsList.length">暂无数据</yd-empty>
    </view>
</template>

<script setup>
import NavBar from "../../components/navBar.vue"
import useStore from "@/store"
const { officialDocs } = useStore()
const _getDocsParams = computed(() => officialDocs.getDocsParams)

const state = reactive({
    loading: false,
    docsList: []
})

function clickLeft() {
    navigateTo({
        url: "/apps/officialDocs/home/<USER>",
    })
}
const initPage = async () => {
    const { type } = _getDocsParams.value
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    try {
        state.loading = true
        const { data } = await http.post("/cloud/official-doc/process-template/list", { type })
        uni.hideToast()
        state.loading = false
        state.docsList = data

    } catch (error) {
        uni.hideToast()
        state.loading = false
    } finally {
        uni.hideToast()
        state.loading = false
    }
}
// 发布
const handleSaveDoc = (item) => {
    const { id } = item
    officialDocs.setDocsParams({ ..._getDocsParams.value, id, status: '', documentId: '' })
    officialDocs.setSubmitFormClearKey()
    navigateTo({
        url: '/apps/officialDocs/manage/saveDocList/createRelea',
    })
}
onMounted(() => {
    initPage()
})

</script>

<style lang='scss' scoped>
.save_doc_list {
    height: 100vh;
    background: #F6F6F6;

    .docs_list {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 24rpx 60rpx;
        padding: 20rpx 30rpx;
        text-align: center;

        .docs_item {
            background-color: #fff;
            border: 1rpx solid rgb(246, 243, 243);
            cursor: pointer;
            border-radius: 10rpx;
            overflow: hidden;
            text-align: center;
            padding: 20rpx;
            box-sizing: border-box;
            user-select: none;
            transition: 0.3s ease-in-out;
            position: relative;

            .docs_item_icon {
                width: 140rpx;
                height: 160rpx;
            }

            .docs_item_title {
                width: 140rpx;
                margin-top: 8rpx;
                font-size: 26rpx;
                font-weight: 500;
                color: #000000;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }
        }
    }

    .empty {
        margin-top: 50% !important;
    }
}
</style>