<template>
    <view>
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="选择评分项" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="participants">
            <scroll-view :scroll-y="true" enable-back-to-top class="evaluate">
                <evaluate-rule :list="participantsList" @changeScore="indicatorScoreChange" @changeData="changeData" :ruleData="ruleData">
                    <template #info="{ data }">
                        <view class="score_label" v-if="data.indicatorScore.evalScoreTypeList.includes('score')">
                            <text>他人评分：</text>
                            <text class="value">{{ data.indicatorScore.othersTotalScore || 0 }}分</text>
                        </view>
                    </template>
                </evaluate-rule>
            </scroll-view>
            <view class="evaluate_footer">
                <text class="aggregate_score">
                    合计得分：
                    <text class="score">{{ totalScore || 0 }}</text>
                </text>
                <view class="footer_btn">
                    <button class="footer_btn" :loading="loading" type="primary" @click="handlerSave">确定</button>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import EvaluateRule from "../components/evaluateRule.vue"
import { computed } from "vue"

const queryParams = ref({})
const participantsList = ref([])
const loading = ref(false)
const myTotalScore = ref(0)
const averageTotalScore = ref(0)
const scoringList = ref([])
const ruleData = ref({})

// 总分判断是立即评价还是平均评价
const totalScore = computed(() => {
    //  settlementType: 结算方式【1.立即结算、2.活动结束后结算】
    // 立即结算使用正常评分加起来，活动后结算需要算平均分
    return ruleData.value.settlementType == 1 ? myTotalScore.value?.toFixed(2) : averageTotalScore.value?.toFixed(2)
})

// 计算总分值
function totalScoreCompt() {
    myTotalScore.value = 0
    averageTotalScore.value = 0
    participantsList.value.forEach((i) => {
        i.secondIndicators.forEach((j) => {
            const { score, evaluatePersonNum, thisIndicatorScore, othersIndicatorScore, scoreRecordList, totalIndicatorScore } = j.indicatorScore
            const newScore = score || 0
            myTotalScore.value += newScore
            if (evaluatePersonNum && evaluatePersonNum > 0) {
                let finalScore = 0
                if (Array.isArray(scoreRecordList) && scoreRecordList.length > 0) {
                    //  如果自己已经有过打分
                    //  别人的所有总分 + 自己的所有总分  / 总人数
                    finalScore = (othersIndicatorScore * (evaluatePersonNum - 1) + (newScore + thisIndicatorScore * scoreRecordList.length)) / (scoreRecordList.length + 1) / evaluatePersonNum
                } else {
                    // 如果自己从来没有打过分 是第一次来打分
                    finalScore = (othersIndicatorScore * 0 + newScore) / 1
                }
                averageTotalScore.value += finalScore
            } else {
                averageTotalScore.value += newScore
            }
        })
    })
}

function handlerSubmit() {
    loading.value = true
    const params = {
        scoringList: scoringList.value
    }
    http.post("/app/evalDayRulePerson/evalAddScoring", params)
        .then(({ message }) => {
            uni.showToast({
                title: message
            })
            uni.navigateBack()
        })
        .finally(() => {
            loading.value = false
        })
}

// 提交评价活动评分
function handlerSave() {
    const isVerify = scoringList.value.some((i) => i.score === "")
    if (isVerify) {
        uni.showToast({
            icon: "none",
            title: "还有未评分项，请评分!"
        })
        return
    } else {
        uni.showModal({
            title: "提示",
            content: "请检查评分是否准确，提交后不可更改！",
            confirmColor: "#00b781",
            cancelColor: "#999",
            confirmText: "确定",
            success: function (res) {
                if (res.confirm) {
                    handlerSubmit()
                } else if (res.cancel) {
                    uni.showToast({
                        icon: "none",
                        title: "已取消提交"
                    })
                }
            }
        })
    }
}

// 获取规则评价
function getRulePersonScoreListInfo() {
    const { activityId, toPersonId, id } = queryParams.value
    const params = {
        activityId,
        toPersonId,
        rulePersonId: id,
        queryThisFrom: true
    }
    http.post("/app/evalDayRulePerson/getRulePersonScoreList", params).then(({ data }) => {
        scoringList.value = []
        participantsList.value = data?.map((i) => {
            return {
                ...i,
                secondIndicators: i.secondIndicators.map((j) => {
                    myTotalScore.value = 0
                    averageTotalScore.value = 0
                    scoringList.value.push({
                        id: j.id,
                        score: j.indicatorScore?.evalScoreTypeList?.includes("score") ? "" : 0,
                        othersTotalScore: j.indicatorScore.othersTotalScore,
                        evaluatePersonNum: j.indicatorScore.evaluatePersonNum,
                        // imgPaths: j.indicatorScore.imgPaths,
                        // videoPaths: j.indicatorScore.videoPaths,
                        // comment: j.indicatorScore.comment
                        imgPaths: "",
                        videoPaths: "",
                        comment: ""
                    })
                    return {
                        ...j,
                        indicatorScore: {
                            ...j.indicatorScore,
                            score: j.indicatorScore?.evalScoreTypeList?.includes("score") ? "" : 0,
                            imgPathsList: [],
                            imgPaths: "",
                            videoPaths: "",
                            comment: ""
                        }
                    }
                })
            }
        })
    })
}

function changeData(j, obj) {
    scoringList.value = scoringList.value.map((i) => {
        const newObj = j.id == i.id ? obj : i
        return {
            ...i,
            ...newObj
        }
    })
}

// 加减分数
function indicatorScoreChange(value, j) {
    j.indicatorScore.score = value
    scoringList.value = scoringList.value.map((i) => {
        return {
            ...i,
            score: j.id == i.id ? value : i.score
        }
    })
    totalScoreCompt()
}

// 返回
function clickLeft() {
    uni.navigateBack()
}

async function getRuleData() {
    await http.post("/app/evalDayRulePerson/getEvalRulePersonDetails", { rulePersonId: queryParams.value.id }).then((res) => {
        ruleData.value = res.data
    })
}

onLoad(async (options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    queryParams.value = options
    await getRuleData()
    getRulePersonScoreListInfo()
})
</script>

<style lang="scss" scoped>
.participants {
    min-height: calc(100vh - 148rpx);
    background: $uni-bg-color-grey;
    padding: 30rpx;
    padding-bottom: 200rpx;
    .evaluate {
        min-height: 80rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        padding: 32rpx;
        width: calc(100% - 64rpx);
        .score_label {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;
            line-height: 40rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .value {
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
            }
            .this_score {
                color: var(--primary-color);
            }
        }
    }

    .evaluate_footer {
        z-index: 999;
        padding: 0rpx 30rpx;
        padding-bottom: 30rpx;
        height: 126rpx;
        background: $uni-bg-color;
        position: fixed;
        bottom: 0;
        left: 0;
        width: calc(100vw - 60rpx);
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 101;

        .aggregate_score {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;

            .score {
                font-weight: 600;
                font-size: 36rpx;
                color: var(--primary-color);
                line-height: 50rpx;
            }
        }

        .footer_btn {
            width: 156rpx;
            height: 80rpx;
            background: var(--primary-color);
            text-align: center;
            border-radius: 10rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-bg-color;
            line-height: 80rpx;
        }
    }
}
</style>
