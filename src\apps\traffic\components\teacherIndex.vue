<template>
    <view class="traffic">
        <!-- 列表 -->
        <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false" use-virtual-list>
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="通行数据" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>

                <uv-tabs :current="0" :scrollable="false" lineColor="var(--primary-color)" :activeStyle="{ color: 'var(--primary-color)' }" :inactiveStyle="{ color: '#999999' }" :customStyle="{ background: '#fff' }" :list="tabsList" @click="tabsClick"></uv-tabs>

                <!-- 筛选条件 -->
                <view class="select_box">
                    <view class="reset_picker">
                        <uni-datetime-picker :border="false" :clearIcon="false" type="date" placeholder="选择日期" v-model="datetime" @change="getData" />
                    </view>
                    <view class="select_site" @click="selectPopupRef.open()">
                        <text> {{ siteName || "选择场地" }} </text>
                    </view>
                </view>

                <!-- 统计卡片 -->
                <statistics-card :data="wholeCount[tabsCode]" :showTip="true" @goDetail="cardGoDetail" />

                <!-- 分类 -->
                <box-tab v-if="tabsCode != 'teacher'" :tabId="tabId" @handelClick="changeTab" :titleArr="boxTabList" />
            </template>
            <view class="list_box">
                <view class="list_item" v-for="item in dataList" :key="item.businessId">
                    <view class="title_box">
                        <text class="title">{{ item.businessName }}</text>
                        <uni-icons @click="gotoDetails(item)" type="right" size="20" color="#999999"></uni-icons>
                    </view>
                    <view class="statistics_num">
                        <view class="num_item" :class="j.code == 'outPeopleNum' ? 'border_class' : ''" v-for="j in column" :key="item.businessId + j.code">
                            <text class="num">{{ item[j.code] || 0 }}</text>
                            <text class="label">{{ j.label }}</text>
                        </view>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <view class="release_entrance" @click.stop="releaseFn" v-if="isRelease">
            <image src="@nginx/workbench/traffic/release_entrance.png" mode="scaleToFill" class="pass_image" />
            <text class="text">放 行</text>
        </view>
        <!-- 选择场地弹框 -->
        <yd-select-popup title="请选择场地" ref="selectPopupRef" :list="siteList" :fieldNames="{ value: 'id', label: 'name' }" @closePopup="closePopup" :selectId="[siteId]" />
        <!-- 引导组件 -->
        <yd-guidance ref="guide" :step="steps"></yd-guidance>
    </view>
</template>

<script setup>
import boxTab from "./boxTab.vue"
import StatisticsCard from "./statisticsCard.vue"
import dayjs from "dayjs"

const props = defineProps({
    appCode: {
        type: String,
        default: ""
    }
})

const guide = ref(null)
const isRelease = ref(false)
const steps = {
    name: "trafficGuide",
    guideList: [
        {
            el: ".reset_picker",
            tips: "在这里可切换通行日期",
            next: "下一步"
        },
        {
            el: ".statistics_card",
            tips: "点击这里可查看通行详情",
            next: "我知道了"
        }
    ]
}
const tabsList = ref([
    {
        name: "学生",
        code: "student"
    },
    {
        name: "教职工",
        code: "teacher"
    }
])
const tabsCode = ref("student")
const paging = ref(null)
const dataList = ref([])
const selectPopupRef = ref(false) // 选择弹框dom
const siteList = ref([])
const datetime = ref(dayjs().format("YYYY-MM-DD"))
const siteName = ref("")
const siteId = ref(null)
const wholeCount = ref({})
const learningPeriod = ref({})
const tabId = ref("")
const boxTabList = ref([])

const column = ref([
    {
        code: "totalPeopleNum",
        label: "通行人数"
    },
    {
        code: "outPeopleNum",
        label: "出校"
    },
    {
        code: "inPeopleNum",
        label: "入校"
    }
])
async function getData() {
    try {
        await getCardData()
        await queryList()
    } catch (error) {
        console.log(error, "报错了")
    }
}

function closePopup(val) {
    if (val && siteId.value != val.id) {
        siteId.value = val.id
        siteName.value = val.name
    }
    getData()
}
function tabsClick(item) {
    tabsCode.value = item.code
    paging.value.reload()
}

const changeTab = (val) => {
    tabId.value = val.sectionId
}

// 调用List数据
async function queryList() {
    uni.showLoading({
        title: "加载中..."
    })
    const url = tabsCode.value == "teacher" ? "/app/traffic/statistics/whole/eachDept" : "/app/traffic/statistics/whole/eachGrade"
    const params = {
        passId: siteId.value,
        queryDate: datetime.value
    }
    await http
        .post(url, params)
        .then((res) => {
            paging.value.complete(res.data)
            if (tabsCode.value !== "teacher") {
                boxTabList.value = []
                learningPeriod.value = {}
                tabId.value = ""
                res.data?.forEach((item, idx) => {
                    if (item.sectionId) {
                        !idx && (tabId.value = item.sectionId)
                        if (!learningPeriod.value[item.sectionId]) {
                            learningPeriod.value[item.sectionId] = [item]
                            boxTabList.value.push(item)
                        } else {
                            learningPeriod.value[item.sectionId].push(item)
                        }
                    }
                })
            }
        })
        .finally(() => {
            uni.hideLoading()
        })
}

async function getCardData() {
    const params = {
        passId: siteId.value,
        queryDate: datetime.value
    }
    await http.post("/app/traffic/statistics/whole/count", params).then((res) => {
        wholeCount.value = res.data
    })
}

function gotoDetails(item) {
    const params = {
        pageTitle: item.businessName,
        businessId: item.businessId,
        passId: siteId.value,
        queryDate: datetime.value,
        userType: tabsCode.value == "teacher" ? 1 : 0 // 用户类型 0：学生 1：老师
    }
    const url = tabsCode.value == "teacher" ? "/apps/traffic/details" : "/apps/traffic/gradeDetails"
    navigateTo({
        url,
        query: params
    })
}

async function init() {
    await http.post("/cloud/quick/passSetting/getSchoolQuickPassSettingByType", { type: 1 }).then((res) => {
        isRelease.value = res.data.isEnabled
    })
    try {
        // 获取场地列表
        await http.get("/cloud/v3/rule/schoolPassscene/listPassSite").then((res) => {
            siteList.value = res.data
            if (res.data && res.data.length > 0) {
                const appCodeObj = res.data.find((i) => i.id == props.appCode)
                siteId.value = appCodeObj ? appCodeObj?.id : res.data[0]?.id
                siteName.value = appCodeObj ? appCodeObj?.name : res.data[0]?.name
            }
        })
        getData()
    } catch (error) {
        console.log(error, "报错了")
    }
}

function cardGoDetail(data) {
    const businessId = dataList.value && dataList.value.length > 0 ? dataList.value[0].businessId : null
    const params = {
        isAll: true,
        pageTitle: "通行数据",
        businessId,
        passId: siteId.value,
        queryDate: datetime.value,
        userType: tabsCode.value == "teacher" ? 1 : 0 // 用户类型 0：学生 1：老师
    }
    navigateTo({
        url: "/apps/traffic/details",
        query: params
    })
}

function releaseFn() {
    navigateTo({
        url: "/apps/traffic/release/index"
    })
}

function showMyGuide() {
    // 打开引导页之前把缓存去掉
    uni.removeStorage({
        key: "trafficGuide",
        success: function (res) {
            guide.value.showGuide = true
        }
    })
}

onMounted(async () => {
    guide.value.showGuide = false
    await init()
})

defineExpose({ showMyGuide })
</script>

<style lang="scss" scoped>
.traffic {
    min-height: 100vh;
    background: $uni-bg-color-grey;
    .select_box {
        display: flex;
        justify-content: space-between;
        background: $uni-bg-color;
        align-items: center;
        padding: 10rpx 30rpx;
        .reset_picker,
        .select_site {
            &::after {
                content: "";
                display: block;
                border: 10rpx solid transparent;
                border-top: 10rpx solid var(--primary-color);
                border-bottom-width: 1px;
                margin-left: 10rpx;
            }
        }
        .reset_picker {
            display: flex;
            align-items: center;

            :deep(.uni-icons) {
                display: none;
            }
        }
        .select_site {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            line-height: 40rpx;
        }
    }
    .list_box {
        padding: 30rpx;
        .list_item {
            height: 224rpx;
            background: #ffffff;
            box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
            border-radius: 20rpx;
            flex-direction: column;
            display: flex;
            margin-bottom: 20rpx;

            .title_box {
                padding: 28rpx 30rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1rpx solid $uni-border-color;
                .title {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: $uni-text-color;
                    line-height: 42rpx;
                }
            }
            .statistics_num {
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex: 1;
                .num_item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    flex: 1;
                    .num {
                        font-weight: 500;
                        font-size: 34rpx;
                        color: #666666;
                        line-height: 36rpx;
                        margin-bottom: 20rpx;
                    }
                    .label {
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #666666;
                        line-height: 26rpx;
                    }
                }
                .border_class {
                    border-left: 1rpx solid $uni-border-color;
                    border-right: 1rpx solid $uni-border-color;
                }
            }
        }
    }
    .release_entrance {
        position: fixed;
        bottom: 60rpx;
        right: 30rpx;
        width: 112rpx;
        height: 112rpx;
        background: var(--primary-color);
        border-radius: 50%;
        box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(17, 198, 133, 0.2);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #ffffff;

        .pass_image {
            width: 44rpx;
            height: 44rpx;
        }

        .text {
            font-size: 24rpx;
            font-weight: 400;
            color: #ffffff;
            line-height: 34rpx;
        }
    }
    :deep(.guide-step-tips) {
        background: #fff !important;
        .text {
            font-weight: 500;
            font-size: 34rpx;
            color: #333333;
            line-height: 48rpx;
        }
        .tool-btn {
            font-weight: 400;
            font-size: 28rpx;
            color: #999999;
            line-height: 40rpx;
            .next {
                color: var(--primary-color) !important;
            }
        }
    }
    :deep(.arrow) {
        background: #fff !important;
    }
    :deep(.guide-box) {
        padding: 0 4rpx;
    }
    :deep(.guide-box::before) {
        border: none !important;
    }
}
</style>
