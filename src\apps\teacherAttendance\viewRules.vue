<template>
    <div>
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" title="查看规则" :border="false" @clickLeft="clickLeft" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <div class="attendance_rule">
            <div class="teacher_info">
                <img class="avatar_teacher" src="@nginx/workbench/teacherAttendance/avatar_teacher.png" alt="" />
                <div class="info">
                    <span class="name">{{ data.personName }}</span>
                    <div class="text">
                        <span class="attendance_group">考勤组：{{ data.groupName }}</span>
                    </div>
                </div>
            </div>
            <div class="attendance_time">
                <div class="time_title">
                    <span>考勤时间</span>
                    <uni-icons @click="timeUp = !timeUp" :type="timeUp ? 'up' : 'down'" size="18" color="#909399"></uni-icons>
                </div>
                <div class="rule_time" :class="{ minH: timeUp }">
                    <span v-for="(i, index) in data.workDay" :key="index"> <span v-if="index != 0">、</span>{{ i }}</span>
                    <span style="padding-left: 16rpx">{{ data.timeRange }}</span>
                </div>
                <div class="rule_time" :class="{ minH: timeUp }">
                    <span v-for="(i, index) in data.restDay" :key="index"> <span v-if="index != 0">、</span>{{ i }} </span>休息
                </div>
            </div>
            <div class="punch_range">
                <div class="time_title">
                    <span>打卡范围</span>
                    <uni-icons @click="rangeUp = !rangeUp" :type="rangeUp ? 'up' : 'down'" size="18" color="#909399"></uni-icons>
                </div>
                <div class="range_content" :class="{ minH: rangeUp }">
                    <div class="item" v-for="(i, index) in data.addressList" :key="index">
                        <span class="title">打卡地址 {{ index + 1 }}</span>
                        <span class="text">{{ i.addressName }}</span>
                    </div>
                    <div class="item" v-for="(i, index) in data.wifiList" :key="index">
                        <span class="title">WIFI名称 {{ index + 1 }}</span>
                        <span class="text">{{ i.wifiName }}</span>
                    </div>
                    <div class="item" v-for="(i, index) in data.deviceList" :key="index">
                        <span class="title">设备蓝牙名称{{ index + 1 }}</span>
                        <span class="text">{{ i.deviceId }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const timeUp = ref(false)
const rangeUp = ref(false)
const data = ref({})
function getRule() {
    http.get("/attweb/attendanceWorkClock/getClockRule").then((res) => {
        data.value = res.data
    })
}

function clickLeft() {
    uni.navigateBack()
}

onLoad(() => {
    getRule()
})
</script>

<style lang="scss" scoped>
.attendance_rule {
    background: $uni-bg-color-grey;
    min-height: calc(100vh - 88rpx);
    .teacher_info {
        height: 100rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        padding: 30rpx;
        display: flex;
        align-items: center;
        position: relative;
        .avatar_teacher {
            width: 100rpx;
            height: 100rpx;
        }
        .info {
            display: flex;
            margin-left: 20rpx;
            height: 100rpx;
            flex-direction: column;
            justify-content: space-around;
            .name {
                font-weight: 500;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
            }
            .text {
                .attendance_group {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: $uni-text-color;
                    line-height: 34rpx;
                }
            }
        }
    }
    .attendance_time,
    .punch_range {
        margin-top: 20rpx;
        min-height: 20rpx;
        background: $uni-bg-color;
        padding: 30rpx;
    }
    .range_content {
        .item {
            display: flex;
            flex-direction: column;
            margin-bottom: 40rpx;
            .title {
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
            }
            .text {
                font-weight: 400;
                font-size: 24rpx;
                color: $uni-text-color;
                line-height: 34rpx;
                padding-top: 10rpx;
            }
        }
    }
    .time_title {
        display: flex;
        justify-content: space-between;
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
        line-height: 34rpx;
        margin-bottom: 10rpx;
    }
    .rule_time {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 40rpx;
    }
    .minH {
        display: none;
    }
}
</style>
