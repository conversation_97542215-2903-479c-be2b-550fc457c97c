<template>
    <div class="video_page">
        <z-paging ref="paging" :auto="false" @query="getVideoList" v-model="videoList">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="校园视频" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <!-- tabs切换 -->
                <uv-tabs lineWidth="20" lineColor="var(--primary-color)" :current="tabsCurrent" :scrollable="false" :activeStyle="{ color: 'var(--primary-color)' }" :inactiveStyle="{ color: '#999999' }" :customStyle="{ background: '#fff' }" :list="tabsList" @click="tabsClick"></uv-tabs>

                <!-- 选择班级 -->
                <div v-if="tabsCurrent == 0" class="classes" @click="selectClassesRef?.open()">
                    {{ selectClasses.classesName || "选择班级" }}
                    <image class="sift_icon" src="@nginx/components/siftIcon.png" alt="" />
                </div>

                <!-- 提示 -->
                <div class="prompt" v-if="isTeacher">
                    <div class="prompt_left">
                        <image src="@nginx/workbench/videoAlbum/promptIcon.png" alt="" class="prompt_play" />
                        <span class="prompt_text">提示</span>
                    </div>
                    <uni-icons @click="promptRef?.open()" type="right" size="18" color="#faad14"></uni-icons>
                </div>
            </template>
            <div v-if="listType == 'operate'" class="operate_list">
                <uv-checkbox-group activeColor="var(--primary-color)" shape="circle" v-model="checkboxValue">
                    <div class="video_list">
                        <uv-checkbox v-for="(item, index) in videoList" :key="index" :label="item.id" :name="item.id">
                            <video :enable-progress-gesture="false" :show-center-play-btn="false" :controls="false" class="video_item" :src="item.url"></video>

                            <div class="shadow_mask"></div>
                        </uv-checkbox>
                    </div>
                </uv-checkbox-group>
            </div>
            <!-- 视频列表 -->
            <div class="video_list" v-else>
                <div class="video_item" v-for="(item, index) in videoList" :key="item.id">
                    <div class="video_box">
                        <video :enable-progress-gesture="false" :show-center-play-btn="false" :controls="false" class="y_video" :src="item.url"></video>
                        <div class="video_mask" @touchstart="gtouchstart(item)" @touchmove="gtouchmove()" @touchend="showDeleteButton(index)">
                            <image src="@nginx/workbench/videoAlbum/videoPlay.png" alt="" class="video_play" />
                        </div>
                        <div class="video_home_show" v-if="item.isHome == true">首页视频</div>
                    </div>
                    <div class="video_operate" v-if="isTeacher">
                        <div class="video_show">
                            <div>{{ item.isShow ? "展示" : "不展示" }}</div>
                            <switch color="var(--primary-color)" @change="changeShow(item)" class="video_switch" :checked="item.isShow" />
                        </div>
                        <div class="more_icon" @click="homeChange(item)">
                            <uni-icons type="more-filled" size="18" color="#333"></uni-icons>
                        </div>
                    </div>
                </div>
            </div>
            <div class="uploadImage" @click="handleUpload" v-if="listType != 'operate' && isTeacher">
                <uni-icons type="plusempty" size="30" color="#fff"></uni-icons>
            </div>

            <template #empty>
                <slot name="empty">
                    <yd-empty text="暂无数据" />
                </slot>
            </template>
            <template #bottom>
                <div class="operate_btn" v-if="listType == 'operate'">
                    <uv-checkbox-group activeColor="var(--primary-color)" shape="circle" v-model="isAll">
                        <uv-checkbox :name="0" @change="changeAll">
                            <div class="select_class">
                                <div class="all_class">全选</div>
                                <div class="select_text">
                                    已选 <span class="num">{{ checkboxValue.length || 0 }}</span> 张
                                </div>
                            </div>
                        </uv-checkbox>
                    </uv-checkbox-group>
                    <div class="btn_box">
                        <button class="btn_class delete_btn" @click="handleDelete" :loading="deleteLoading" :disabled="deleteLoading">删除</button>
                        <button class="btn_class default_btn" @click="cancelOperate">取消</button>
                    </div>
                </div>
            </template>
        </z-paging>

        <!-- 选择班级弹框 -->
        <yd-select-popup title="请选择班级" ref="selectClassesRef" :list="classList" :fieldNames="{ value: 'classesId', label: 'classesName' }" @closePopup="closeClasses" :selectId="[selectClasses.classesId]" />

        <!-- 选择首页展示弹框 -->
        <yd-select-popup :showRight="false" title="提示" ref="homeShowRef" :list="homeShowList">
            <template #right="{ data }">
                <switch color="var(--primary-color)" @change="changeHomeShow(data)" style="transform: scale(0.6)" :checked="isHomeShow" />
            </template>
        </yd-select-popup>

        <!-- 视频预览 -->
        <preview-video :list="videoList" ref="previewVideoRef" />

        <!-- 提示弹框 -->
        <yd-popup type="bottom" ref="promptRef" :titleflag="false" :btnsflag="false" borderRadius="20rpx 20rpx 0rpx 0rpx" popupWidth="100%">
            <view class="prompt_content">
                <span class="prompt_title">提示</span>
                <uni-icons @click="promptRef.popup?.close()" type="closeempty" class="delect_icon" size="20"></uni-icons>
                <div class="content">
                    <div class="text">
                        <div class="index_class">1</div>
                        <span>视频大小不能超过500MB；</span>
                    </div>
                    <div class="text">
                        <div class="index_class">2</div>
                        <span>视频格式，目前支持MP4、FLV、HLS、MPEG-DASH；</span>
                    </div>
                    <div class="text">
                        <div class="index_class">3</div>
                        <span>首页模块只能展示一个视频。</span>
                    </div>
                </div>
            </view>
        </yd-popup>

        <!-- 确认删除框 -->
        <yd-popup ref="confirmRef" :titleflag="false" @confirm="dialogConfirm">
            <view style="padding: 33px 0px 10px 0px">确定删除已选的{{ checkboxValue.length || 0 }}个视频吗？</view>
        </yd-popup>
    </div>
</template>

<script setup>
import PreviewVideo from "./components/previewVideo.vue"

import useStore from "@/store"

const { user } = useStore()
const listType = ref("read") // read只读模式 / operate编辑模式
const appCode = ref(null) // 应用标识
const paging = ref(null)
const promptRef = ref(null) // 提示弹框
const tabsId = ref(1) // tabs当前id
const selectClassesRef = ref(null) // 选择弹框dom
const previewVideoRef = ref(null) // 视频预览弹框
const homeShowRef = ref(null) // 首页弹框
const tabsCurrent = ref(0) // tabs当前索引
const classList = ref([]) // 班级列表
const videoList = ref([])
const selectClasses = ref({}) // 当前选中班级
const timeOutEvent = ref(null) // 节流事件
const isHomeShow = ref(false)
const type = ref(2) // 1相册 2视频
const homeShowList = ref([{ label: "设为首页视频", value: null }])
const tabsList = [
    { name: "班级", id: 1 },
    { name: "校级", id: 2 }
]

const isAll = ref([]) // 全选值
const deleteLoading = ref(false) // 删除按钮loading
const confirmRef = ref(null) // 删除前二次弹框
const checkboxValue = ref([]) // 编辑时多选选中项

// 是否为教师端（教师端有操作功能）
const isTeacher = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    return !["eltern", "student"].includes(roleCode)
})

async function getVideoList(pageNo, pageSize) {
    await http
        .post("/brand/album/page", {
            type: type.value,
            pageNo,
            pageSize,
            kind: tabsId.value, // 1-班级/2-校级
            classesId: tabsId.value == 1 ? selectClasses.value.classesId : null // 如果是班级传入班级ID如果是校级直接为null
        })
        .then(({ data }) => {
            paging.value.complete(data.list)
        })
        .catch(() => {
            paging.value.complete(false)
        })
}

// 初始化获取班级
async function init() {
    try {
        const res = await http.get("/app/roll/getClassesInfo", { code: appCode.value })
        classList.value = res.data
        // 有班级才可以获取相册
        if (res.data?.length > 0) {
            selectClasses.value = res.data[0] || {}
            paging.value.reload()
        } else {
            paging.value.setLocalPaging([])
            uni.showToast({
                title: "暂无班级",
                icon: "none"
            })
        }
    } catch (error) {
        console.log(error, "error")
    }
}

// 取消选择班级弹框
function closeClasses(item) {
    if (item && item.classesId != selectClasses.value.classesId) {
        selectClasses.value = item
        paging.value.reload()
    }
}

function previewVideoFn(index) {
    previewVideoRef.value.open(index)
}

// tabs切换点击
function tabsClick(item) {
    tabsCurrent.value = item.index
    tabsId.value = item.id
    paging.value.reload()
}

function homeChange(item) {
    homeShowList.value[0].value = item.id
    isHomeShow.value = item.isHome
    homeShowRef.value.open()
}

function changeHomeShow(data) {
    isHomeShow.value = !isHomeShow.value
    http.post("/brand/album/updateIsHome", { id: data.value }).then((res) => {
        paging.value.reload()
        homeShowRef.value.close()
        setTimeout(() => {
            uni.showToast({ title: res.message, icon: "none" })
        }, 500)
    })
}

function changeShow(item) {
    console.log(item)
    item.isShow = !item.isShow
    http.post("/brand/album/updateIsShow", { id: item.id }).then((res) => {
        paging.value.reload()
        setTimeout(() => {
            uni.showToast({ title: res.message, icon: "none" })
        }, 500)
    })
}

//真正长按后应该执行的内容
function longPress(val) {
    timeOutEvent.value = 0
    //执行长按要执行的内容，如弹出菜单
    listType.value = "operate"
}

//手释放，如果在500毫秒内就释放，则取消长按事件，此时可以执行onclick应该执行的事件
function showDeleteButton(index) {
    clearTimeout(timeOutEvent.value) //清除定时器
    if (timeOutEvent.value != 0) {
        //这里写要执行的内容（如onclick事件）
        previewVideoFn(index)
    }
    return false
}

//如果手指有移动，则取消所有事件，此时说明用户只是要移动而不是长按
function gtouchmove() {
    clearTimeout(timeOutEvent.value) //清除定时器
    timeOutEvent.value = 0
}

//长按事件（起始）
function gtouchstart(item) {
    if (!isTeacher.value) return
    timeOutEvent.value = setTimeout(function () {
        longPress(item)
    }, 500) //这里设置定时器，定义长按500毫秒触发长按事件
    return false
}

// 编辑全选
function changeAll(item) {
    if (item) {
        checkboxValue.value = videoList.value.map((item) => item.id)
    } else {
        checkboxValue.value = []
    }
}

// 删除按钮
function handleDelete() {
    if (checkboxValue.value && checkboxValue.value.length > 0) {
        confirmRef.value.open()
    } else {
        uni.showToast({
            title: "请选择要删除的图片",
            icon: "none"
        })
    }
}

// 取消
async function cancelOperate() {
    listType.value = "read"
    checkboxValue.value = []
    isAll.value = []
}

// 删除确认弹框
function dialogConfirm() {
    deleteLoading.value = true
    http.post("/brand/album/delete", { ids: checkboxValue.value || [] })
        .then(async (res) => {
            await cancelOperate()
            paging.value.reload()
            setTimeout(() => {
                uni.showToast({ title: res.message, icon: "none" })
            }, 500)
        })
        .finally(() => {
            deleteLoading.value = false
        })
}

// 上传图片
function handleUpload() {
    uni.chooseVideo({
        count: 1,
        sourceType: ["camera", "album"],
        success: (res) => {
            console.log(res, res.tempFilePath)
            http.uploadFile("/app/file/upload", res.tempFilePath, { folderType: "app" }).then((url) => {
                if (url) {
                    const obj = {
                        classifyId: null, // 视频无分类
                        kind: tabsId.value, // 1-班级/2-校级
                        classesId: tabsId.value == 1 ? selectClasses.value.classesId : null,
                        type: type.value,
                        urls: [url]
                    }
                    http.post("/brand/album/create", obj).then((res) => {
                        uni.showToast({ title: res.message, icon: "none" })
                        paging.value.reload()
                    })
                }
            })
        }
    })
}

watch(
    () => checkboxValue.value,
    (val) => {
        if (val.length == videoList.value.length) {
            isAll.value = [0]
        } else {
            isAll.value = []
        }
    }
)

onMounted(() => {
    init()
})

onLoad((option) => {
    appCode.value = option.code
})
</script>

<style lang="scss" scoped>
.video_page {
    background: $uni-bg-color-grey;
    min-height: 100vh;
}
.classes {
    padding: 30rpx;
    background: $uni-bg-color;
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 28rpx;
    color: $uni-text-color;
    line-height: 40rpx;
    .sift_icon {
        width: 28rpx;
        height: 28rpx;
    }
}
.prompt {
    height: 80rpx;
    background: #fff7e1;
    border-radius: 10rpx;
    margin: 20rpx 30rpx 0rpx 30rpx;
    display: flex;
    padding: 0rpx 20rpx;
    align-items: center;
    justify-content: space-between;
    .prompt_left {
        display: flex;
        align-items: center;
        .prompt_play {
            width: 32rpx;
            height: 32rpx;
        }
        .prompt_text {
            font-weight: 400;
            font-size: 28rpx;
            padding-left: 10rpx;
            color: #faad14;
        }
    }
}
.video_list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10rpx; /* 单元格之间的间距 */
    padding: 30rpx;
    margin: auto;
    .video_item {
        width: 220rpx;
        min-height: 220rpx;
        .video_box {
            width: 220rpx;
            height: 220rpx;
            position: relative;
            .y_video {
                width: 220rpx;
                height: 220rpx;
            }
            .video_mask {
                position: absolute;
                width: 220rpx;
                height: 220rpx;
                top: 0;
                left: 0;
                background: #00000060;
                display: flex;
                align-items: center;
                justify-content: center;
                .video_play {
                    width: 80rpx;
                    height: 80rpx;
                }
            }
            .video_home_show {
                position: absolute;
                width: 220rpx;
                height: 52rpx;
                bottom: 0;
                background: var(--primary-color);
                font-size: 24rpx;
                color: $uni-text-color-inverse;
                line-height: 52rpx;
                text-align: center;
                left: 0;
            }
        }
        .video_operate {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .video_show {
                flex: 1;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
                display: flex;
                align-items: center;
                .video_switch {
                    max-width: 62rpx;
                    transform: scale(0.6);
                }
            }
            .more_icon {
                height: 36rpx;
                width: 36rpx;
                transform: rotate(90deg);
            }
        }
    }
}
:deep(.confirm_popup_content) {
    padding: 0rpx !important;
}
.prompt_content {
    padding: 30rpx;
    position: relative;
    .delect_icon {
        position: absolute;
        top: 6rpx;
        right: 10rpx;
        padding: 30rpx;
    }
    .prompt_title {
        font-weight: 500;
        font-size: 34rpx;
        color: #333333;
        line-height: 48rpx;
        text-align: center;
    }
    .content {
        display: flex;
        flex-direction: column;
        margin-top: 64rpx;
        .text {
            font-weight: 400;
            margin-bottom: 40rpx;
            font-size: 32rpx;
            color: #333333;
            line-height: 52rpx;
            display: flex;
            text-align: left;

            .index_class {
                width: 52rpx;
                background: #fff7e1;
                height: 52rpx;
                border-radius: 50%;
                color: #faad14;
                font-size: 30rpx;
                text-align: center;
                margin-right: 20rpx;
            }
        }
    }
}
.operate_list {
    :deep(.uv-checkbox__icon-wrap) {
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        z-index: 99;
    }
    :deep(.uv-checkbox__label-wrap) {
        padding: 0rpx;
    }
    :deep(.uv-checkbox) {
        border: 1rpx solid $uni-border-color;
        width: 210rpx;
        height: 210rpx;
        position: relative;
    }
    .shadow_mask {
        height: 210rpx;
        width: 210rpx;
        position: absolute;
        top: 0rpx;
        left: 0rpx;
        z-index: 98;
        background: #00000050;
    }
}
.operate_btn {
    padding: 30rpx 30rpx 60rpx 30rpx;
    height: 50rpx;
    display: flex;
    background: $uni-bg-color;
    align-items: center;
    justify-content: space-between;
    .select_class {
        display: flex;
        align-items: center;
        .all_class {
            font-weight: 500;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }
        .select_text {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            line-height: 34rpx;
            padding-left: 30rpx;
            .num {
                color: var(--primary-color);
            }
        }
    }

    .btn_box {
        display: flex;
        justify-content: flex-end;
        .btn_class {
            min-width: 112rpx;
            height: 60rpx;
            font-weight: 400;
            font-size: 26rpx;
            line-height: 60rpx;
            text-align: center;
            background: $uni-bg-color;
        }
        .default_btn {
            color: $uni-text-color;
            border: 1rpx solid $uni-border-color;
            margin-left: 20rpx;
        }
        .delete_btn {
            color: $uni-color-error;
            border: 1rpx solid $uni-color-error;
        }
    }
}
.uploadImage {
    position: fixed;
    bottom: 226rpx;
    right: 30rpx;
    width: 112rpx;
    height: 112rpx;
    background: var(--primary-color);
    box-shadow: 0rpx 8rpx 8rpx 0rpx #11c68533;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}
</style>
