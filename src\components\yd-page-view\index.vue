<template>
    <view
        class="page_view"
        :style="{
            backgroundColor: pageBackground
        }"
    >
        <z-paging ref="paging" v-model="dataList" :auto="auto" @query="query" :refresher-only="refresherOnly" @onRefresh="onRefresh" :auto-show-back-to-top="true">
            <!--  -->
            <template #top v-if="!hideTop">
                <slot name="top">
                    <uni-nav-bar :showMenuButtonWidth="true" :left-icon="leftIcon" :color="color" :background-color="backgroundColor" :statusBar="true" :fixed="true" :title="changeTitle(title)" @clickLeft="back" :border="border" :leftWidth="leftWidth" :rightWidth="rightWidth">
                        <template #left>
                            <slot name="left" v-if="!hideLeft">
                                <uni-icons v-if="!hideLeft" type="left" size="18"></uni-icons>
                            </slot>
                        </template>
                        <template #default>
                            <view class="page_title_box">
                                <slot name="title">
                                    <text class="page_title" :style="{ color: color }">
                                        {{ changeTitle(title) }}
                                    </text>
                                </slot>
                            </view>
                        </template>
                        <template #right>
                            <slot name="right"></slot>
                        </template>
                    </uni-nav-bar>
                </slot>
            </template>
            <!--  -->
            <slot></slot>
            <!--  -->
            <template #bottom v-if="!hideBottom">
                <slot name="bottom">
                    <yd-tabBar :data="tabBarData.length ? tabBarData : tabBarList" :current="tabBarCurrent ?? current" @yClick="checkItem">
                        <template #qrCode="{ item }">
                            <div class="qrcode_box">
                                <div class="qrcode_logo">
                                    <image class="image" src="@nginx/tabBar/qrcodeLogo.png" mode="aspectFit"></image>
                                </div>
                                <text class="bar_text" :class="item.item.active ? 'active_text' : ''">{{ item.text }}</text>
                            </div>
                        </template>
                    </yd-tabBar>
                </slot>
            </template>
            <!--  -->
            <template #empty>
                <slot name="empty">
                    <yd-empty :text="emptyText" />
                </slot>
            </template>
            <template #backToTop>
                <slot name="backToTop"></slot>
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import useStore from "@/store"
import { checkPlatform } from "@/utils/sendAppEvent.js"
// #ifdef H5 || H5-WEIXIN
import * as dd from "dingtalk-jsapi" // 钉钉
// #endif

const paging = ref(null)

const emit = defineEmits(["clickTabBar", "query", "onRefresh", "update:modelValue"])
const { system } = useStore()
const tabBarList = computed(() => system.tabBarList)
const current = computed(() => system.currentTabBar)

const props = defineProps({
    pageBackground: {
        type: String,
        default: "#F6F6F6"
    },
    title: {
        type: String,
        default: ""
    },
    leftWidth: {
        type: Number,
        //  #ifdef APP-PLUS || H5
        default: 40,
        //  #endif
        // #ifdef  MP-WEIXIN
        default: 86
        //  #endif
    },
    rightWidth: {
        type: Number,
        //  #ifdef APP-PLUS || H5
        default: 40,
        //  #endif
        // #ifdef  MP-WEIXIN
        default: 86
        //  #endif
    },
    border: {
        type: Boolean,
        default: false
    },
    leftIcon: {
        type: String,
        default: "" //left
    },
    // 返回事件
    clickLeft: Function,
    clickTabBar: Function,
    backgroundColor: {
        type: String,
        default: "#fff"
    },
    color: {
        type: String,
        default: "#000"
    },
    hideTop: {
        type: Boolean,
        default: false
    },
    hideBottom: {
        type: Boolean,
        default: true
    },
    hideLeft: {
        type: Boolean,
        default: false // true-隐藏 false-显示
    },
    tabBarData: {
        type: Array, // tabBar data
        default: []
    },
    tabBarCurrent: {
        type: [Number, String]
    },
    emptyText: {
        type: String,
        default: "暂无数据"
    },
    modelValue: {
        type: Array,
        default: []
    },
    auto: {
        type: Boolean,
        default: true
    },
    refresherOnly: {
        type: Boolean,
        default: false
    }
})

const tabBarCurrent = computed(() => props.tabBarCurrent)

const dataList = computed({
    get: () => props.modelValue,

    set: (val) => {
        emit("update:modelValue", val)
    }
})

const query = (n, v) => {
    emit("query", n, v)
}

const onRefresh = (p) => {
    emit("onRefresh", p)
}

const changeTitle = (val) => {
    if (!val) return ""
    return decodeURIComponent(val)
}

const back = () => {
    if (props.clickLeft) {
        return props.clickLeft()
    }
    if (!props.clickLeft && props.hideLeft) {
        return true
    }
    uni.navigateBack({
        delta: 1
    })
}

const checkItem = (item, index) => {
    if (props.clickTabBar) {
        return props.clickTabBar(item, index)
    }
    // 避免重复点击跳转
    if (current.value === item.id) {
        // TODO: 双击回到顶部
        return
    }
    !props.clickTabBar &&
        system.switchTab({
            id: item.id
        })
    uni.switchTab({
        url: item.pagePath,
        fail: () => {
            uni.navigateTo({
                url: item.pagePath
            })
        },
        complete: () => {
            // #ifdef H5 || H5-WEIXIN
            if (checkPlatform() === "dingding") {
                dd.biz.navigation.setTitle({
                    title: item.text
                })
            }
            // #endif
        }
    })
}

defineExpose({
    paging: paging
})
</script>

<style lang="scss" scoped>
.page_view {
    position: relative;
    min-height: 100vh;
    z-index: 1;
    :deep(.zp-empty-view-center) {
        background-color: v-bind("props.backgroundColor");
    }
    :deep(.uni-scroll-view) {
        scrollbar-width: none;
        scrollbar-color: transparent transparent;
    }

    .page_title_box {
        display: flex;
        flex: 1;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        .page_title {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-weight: 500;
            font-size: 32rpx;
            color: $uni-text-color;
            line-height: 48rpx;
            text-align: center;
        }
    }
}
.qrcode_box {
    position: relative;
    bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .qrcode_logo {
        width: 80rpx;
        height: 80rpx;
        background: var(--primary-color);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        .image {
            width: 40rpx;
            height: 40rpx;
        }
    }
    .bar_text {
        font-weight: 400;
        font-size: 20rpx;
        color: $uni-text-color;
        line-height: 28rpx;
        padding-top: 6rpx;
    }
    .active_text {
        color: var(--primary-color);
        transition: all 0.3s;
    }
}
</style>
