<template>
    <div class="perfect_more">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="个人信息录入" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
        <div class="tip" v-if="roleCode != 'eltern'">
            <uni-icons type="info-filled" size="18" color="var(--primary-color)"></uni-icons>
            <text class="tip_text">请填写以下个人信息（可选填）</text>
        </div>
        <parent-data v-if="roleCode == 'eltern'" />
        <student-data v-else-if="roleCode == 'student'" />
        <teacher-data v-else />
    </div>
</template>

<script setup>
import useStore from "@/store"
import ParentData from "./components/parentData.vue"
import TeacherData from "./components/teacherData.vue"
import StudentData from "./components/studentData.vue"

const { user } = useStore()
const roleCode = computed(() => user?.identityInfo?.roleCode)
</script>

<style lang="scss" scoped>
.perfect_more {
    min-height: 100vh;
    background: $uni-bg-color-grey;
    .tip {
        padding: 30rpx;
        background: var(--primary-bg-color);
        display: flex;
        align-items: center;
        .tip_text {
            padding-left: 10rpx;
            color: var(--primary-color);
            font-weight: 400;
            font-size: 24rpx;
            line-height: 34rpx;
        }
    }
}
</style>
