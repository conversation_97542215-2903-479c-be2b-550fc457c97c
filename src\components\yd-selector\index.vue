<template>
    <uni-popup ref="selectorPopup" type="bottom" :is-mask-click="false" :safe-area="false" style="z-index: 999">
        <view class="selector_page">
            <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="pageBack" title="选择成员" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
            <!-- 搜索页面 -->
            <div v-if="state.isSearchPage" class="search_page">
                <view class="search_box">
                    <uv-search @change="searchSelectList" @clear="searchSelectList" :placeholder="`请输入你要搜索的${state.selectType && state.selectType.name ? state.selectType.name : ''}`" :showAction="false" v-model="state.searchKey"></uv-search>
                </view>
                <scroll-view class="scroll_search_y" :scroll-y="true" :scrollTop="state.scrollTop" :show-scrollbar="false" :lower-threshold="50" :scroll-with-animation="true">
                    <view class="search_list_box" v-if="state.searchreqListArr && state.searchreqListArr.length">
                        <view class="item_select" v-for="item in state.searchreqListArr" :key="item.id">
                            <view class="item_select_left" @click="searchselectItemThis(item)">
                                <view class="select_img_box">
                                    <image v-if="searchisSelected(item)" class="select_img" src="@nginx/workbench/registration/selectYes.png"></image>
                                    <image v-if="!searchisSelected(item)" class="select_img" src="@nginx/workbench/registration/selectNo.png"></image>
                                </view>
                                <view class="avatar_box">
                                    <uv-avatar :text="item.name?.charAt(0)" size="30" fontSize="14" bg-color="var(--primary-color)"></uv-avatar>
                                </view>
                                <view class="select_name">{{ item.name }}</view>
                            </view>
                        </view>
                    </view>
                    <yd-empty text="暂无数据" :isMargin="true" v-else />
                </scroll-view>
                <!-- 底部 -->
                <view class="foot_box_out">
                    <view class="foot_box">
                        <view class="sub_box">
                            <view class="sub_btn" @click="savasearch">确定</view>
                        </view>
                    </view>
                </view>
            </div>
            <!-- 选择页面 -->
            <div v-else>
                <!-- 类型选择 -->
                <view class="type_box">
                    <view class="type_class">
                        <view
                            @click="changeType(item)"
                            class="type_item"
                            :class="{
                                select_type: state.selectType.type == item.type
                            }"
                            v-for="item in state.typeList"
                            :key="item.type"
                            >{{ item.name }}</view
                        >
                    </view>
                </view>
                <!-- 选择的内容 -->
                <view class="content">
                    <!-- 搜索区域 -->
                    <view class="search_box">
                        <uv-search @click="showsearch" :disabled="true" :placeholder="`请输入你要搜索的${state.selectType && state.selectType.name ? state.selectType.name : ''}`" :showAction="false"></uv-search>
                    </view>
                    <!-- 面包屑 可以滑动 -->
                    <view class="breadcrumb_box">
                        <view class="breadcrumb">
                            <view class="breadcrumb_item" @click="handleBreadAll">
                                <view class="item_slot">全部</view>
                                <uni-icons type="right" size="18" color="#ccc"></uni-icons>
                            </view>
                            <view class="breadcrumb_item" v-for="(item, index) in state.bread" :key="item.value" @click="handleBread(item, index)">
                                <view class="item_slot">{{ item.lable }}</view>
                                <view class="icon">
                                    <uni-icons type="right" size="18" color="#ccc"></uni-icons>
                                </view>
                            </view>
                        </view>
                    </view>
                    <scroll-view class="scroll_y" :scroll-y="true" :scrollTop="state.scrollTop" :show-scrollbar="false" :lower-threshold="50" :scroll-with-animation="true">
                        <!-- 全选 -->
                        <view @click="allSelectPeople" class="all_select_box" v-if="state.list && state.list.length && isSelectLevel(state.list[0], true)">
                            <view class="all_select">
                                <view class="select_img_box">
                                    <image v-show="state.selectPeople" class="select_img" src="@nginx/workbench/registration/selectYes.png"></image>
                                    <image v-show="!state.selectPeople" class="select_img" src="@nginx/workbench/registration/selectNo.png"></image>
                                </view>
                                <view class="select_name">全选</view>
                            </view>
                        </view>
                        <div v-if="state.list && state.list.length">
                            <view class="list_box" v-for="item in state.list" :key="item.id">
                                <view class="list_item">
                                    <view class="item_box">
                                        <view class="left_box">
                                            <view class="select_img_box" @click="selectItemThis(item)" v-if="isSelectLevel(item)">
                                                <image v-if="isSelected(item)" class="image_url" src="@nginx/workbench/registration/selectYes.png"></image>
                                                <image v-if="!isSelected(item)" class="image_url" src="@nginx/workbench/registration/selectNo.png"></image>
                                            </view>
                                            <view class="name_title">
                                                {{ item.name }}
                                            </view>
                                        </view>
                                        <view class="right_box" v-if="item.isSub" @click="handleNext(item)">
                                            <view class="next_img_box">
                                                <image class="next_img" src="@nginx/workbench/registration/nextIcon.png"> </image>
                                            </view>
                                            <view
                                                class="next_name"
                                                :class="{
                                                    next_name_active: !isSelected(item),
                                                    next_name_not_active: isSelected(item)
                                                }"
                                                >下级</view
                                            >
                                        </view>
                                    </view>
                                    <view class="elterns_list" v-if="item.elterns && item.elterns.length">
                                        <view class="elterns_item" v-for="(eItm, eIdx) in item.elterns" :key="eItm.id || eIdx">
                                            <view class="select_img_box" @click="selectItemThis(eItm, item)" v-if="isSelectLevel(eItm)">
                                                <image v-if="isSelected(eItm)" class="image_url" src="@nginx/workbench/registration/selectYes.png"></image>
                                                <image v-if="!isSelected(eItm)" class="image_url" src="@nginx/workbench/registration/selectNo.png"></image>
                                            </view>
                                            <view class="name_title">
                                                <text>{{ eItm.name }}</text>
                                                <view class="relations_tip">{{ relationsType[eItm.relations] }}</view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </div>
                        <yd-empty v-else :isMargin="true" />
                    </scroll-view>
                </view>
                <!-- 底部 -->
                <view class="foot_box_out">
                    <view class="foot_box">
                        <view @click="openhasSelectPopup" class="has_select ellipsis">{{ `已选：${state.selected.length}人，其中${getNum(state.selected)}有个部门(含子部门)/班级` }}</view>
                        <view class="sub_box">
                            <view class="up_img" @click="openhasSelectPopup">
                                <uv-icon size="20" color="var(--primary-color)" name="arrow-up"></uv-icon>
                            </view>
                            <view class="sub_btn" @click="outputPeople">确定</view>
                        </view>
                    </view>
                </view>
            </div>
            <!-- 选择的人员弹框 -->
            <uv-popup ref="hasSelectPopup" mode="bottom" round="20" custom-style="height: 1000rpx;" :closeable="true">
                <view class="has_select_popup">
                    <view class="titleBox ellipsis"> {{ `已选：${state.copySelected.length}人，其中${getNum(state.copySelected)}有个部门(含子部门)/班级` }} </view>
                    <view class="hasListBox" v-if="state.copySelected && state.copySelected.length">
                        <view class="hasListItem" v-for="item in state.copySelected" :key="item.id">
                            <view class="item_left">
                                <view class="item_left_avatar" v-if="item.typeValue === 'people_dept' || item.typeValue === 'student'">
                                    <uv-avatar :text="item.name?.charAt(0)" size="30" fontSize="14" bg-color="var(--primary-color)"></uv-avatar>
                                </view>
                                <view class="item_left_name">{{ item.name }}</view>
                            </view>

                            <view class="item_right" @click="removeItem(item)">
                                <view class="right_btn"> 移除 </view>
                            </view>
                        </view>
                    </view>
                    <yd-empty v-else :isMargin="true" />
                    <view class="submitFootBox" @click="confirmBtn">
                        <view class="submitFootBtn"> 确定 </view>
                    </view>
                </view>
            </uv-popup>
        </view>
    </uni-popup>
</template>

<script setup>
import { computed } from "vue"

const emit = defineEmits(["confirm"])
const selectorPopup = ref(null)
const hasSelectPopup = ref(null)

const state = reactive({
    // ——————————选择——————————
    selectPeople: false,
    scrollTop: 0,
    bread: [],
    selectType: null,
    typeList: [],
    list: [],
    selected: [],
    copySelected: [],
    // ——————————搜索——————————
    isSearchPage: false,
    searchreqListArr: [],
    searchselected: [],
    searchKey: ""
})

const isTypeMultiple = ref(false)

const relationsType = {
    1: "父亲",
    2: "母亲",
    3: "爷爷",
    4: "奶奶",
    5: "外公",
    6: "外婆",
    7: "其他"
}

const getNum = computed(() => {
    return (list) => {
        const pidList = []
        list.forEach((i) => {
            pidList.push(i.pid)
        })
        const num = [...new Set(pidList)]
        console.log(pidList, num, "nihao")
        return num.length
    }
})

// 接口参数 businessType 枚举
const getBusinessType = (obj) => {
    const business = {
        classes: 10, // 选班级
        student: 11, // 选学生
        parent: 12, // 选家长
        dept: 20, // 选部门
        people_dept: 21, // 选部门下的老师
        role: 30, // 选角色
        people_role: 31, // 选角色下的老师
        external: 40, // 选组
        people_external: 41 // 选组成员
    }
    return business[obj.type]
}

// 接口参数 treeType 枚举
const getTreeType = (obj) => {
    const business = {
        classes: 1, // 选班级
        student: 1, // 选学生
        parent: 1, // 选家长
        dept: 2, // 选部门
        people_dept: 2,
        role: 3, // 选角色
        people_role: 3, // 选角色下的老师
        external: 4, // 选组
        people_external: 4 // 选组成员
    }
    return business[obj.type]
}

// 搜索列表的判断：当前层级是否可以点击选择
const searchisSelected = computed(() => {
    return (item) => !!~state.selected.findIndex((i) => i.id === item.id) || !!~state.searchselected.findIndex((i) => i.id === item.id)
})

// 选择按钮的判断：当前层级是否可以点击选择
const isSelectLevel = computed(() => {
    return (item, isAll) => {
        // tip： 除了学籍的类型层级多一些不一样需要传参，其他的可以直接用type不需要传selectLevel，但是其他的你想传selectLevel也行，随便你咯
        // 选择的层级 不选的话默认每个层级都可以选择
        if (state.selectType.selectLevel == "parent" && (!!item.relations || (!!item.elterns && isAll))) return true
        if (!state.selectType && !state.selectType.selectLevel) return true
        const isClasses = getTreeType(state.selectType) // 判断学籍
        const { selectLevel, type } = state.selectType
        return isClasses == 1 ? selectLevel == "all" || selectLevel == item.typeValue : type == item.typeValue || selectLevel == item.typeValue
    }
})

// 下一级按钮判断：当前层级是否已经选择了，选择了的话不可以点击进入下一级
const isSelected = computed(() => {
    return (item) => !!~state.selected.findIndex((i) => i.id === item.id)
})

// 获取列表
async function getList(options = {}) {
    const params = {
        treeType: getTreeType(state.selectType),
        pid: 0,
        businessType: getBusinessType(state.selectType),
        code: null,
        isRule: false, // 是否权限校验
        typeValue: null,
        ...options
    }
    let url = "/cloud/v3/tree/selectTree"
    if (params.businessType == 12 && params.typeValue == "classes") {
        url = "/app/student/search"
        params.classesId = params.pid
    }
    http.post(url, params)
        .then(({ data }) => {
            state.list = data
        })
        .finally(() => {})
}

//  切换tabs选择类型
function changeType(item) {
    state.bread = []
    state.list = []
    state.selectType = item
    if (!isTypeMultiple.value) {
        state.selected = []
    }
    getList()
}

// 下一级
async function handleNext(item) {
    state.selectPeople = false
    if (~state.selected.findIndex((i) => i.id === item.id)) return
    await getList({ typeValue: item.typeValue, pid: item.id })
    state.bread.push({
        lable: item.name,
        value: item.id,
        ...item
    })
}

// 全部
async function handleBreadAll() {
    state.bread = []
    getList()
}

// 选择事件
function selectItemThis(item, pItem) {
    const index = state.selected.findIndex((i) => i.id === item.id)
    if (~index) {
        state.selected.splice(index, 1)
    } else {
        state.selected.push({ ...item, pName: state.bread[state.bread.length - 1]?.name, student: { ...pItem, elterns: [] } })
    }
    console.log(state.selected, "selected")
}

// 全选
function allSelectPeople() {
    state.selectPeople = !state.selectPeople
    let arr = []
    if (state.selectType.selectLevel == "parent") {
        state.list?.forEach((i) => {
            i.elterns?.forEach((e) => {
                arr.push({ ...e, pid: state.bread[state.bread.length - 1].id, pName: state.bread[state.bread.length - 1]?.name, student: { ...i, elterns: [] } })
            })
        })
    } else {
        arr = state.list?.map((i) => {
            return {
                ...i,
                pName: state.bread[state.bread.length - 1]?.name
            }
        })
    }
    if (state.selectPeople) {
        // 合并去重
        state.selected = [...new Set([...arr, ...state.selected])]
    } else {
        // 取消全选就直接过滤不要的
        if (state.selectType.selectLevel == "parent") {
            state.selected = state.selected.filter((item1) => !arr.some((item2) => item2.id === item1.id))
        } else {
            state.selected = state.selected.filter((item1) => !state.list.some((item2) => item2.id === item1.id))
        }
    }
    console.log(arr, state.selected)
}

watch(
    () => state.selected,
    (val) => {
        if (val && val.length) {
            if (state.selectType.selectLevel == "parent") {
                const arrSelected = []
                state.list?.forEach((i) => {
                    i.elterns?.forEach((e) => {
                        arrSelected.push({
                            ...e,
                            pid: state.bread[state.bread.length - 1].id,
                            pName: state.bread[state.bread.length - 1]?.name,
                            student: { ...i, elterns: [] }
                        })
                    })
                })
                state.selectPeople = arrSelected.every((item1) => val.some((item2) => item2.id === item1.id))
            } else {
                state.selectPeople = state.list.every((item1) => val.some((item2) => item2.id === item1.id))
            }
        } else {
            state.selectPeople = false
        }
    },
    {
        deep: true,
        immediate: true
    }
)

// 点击面包屑的某一层
function handleBread(item, index) {
    state.selectPeople = false
    state.bread = state.bread.slice(0, index + 1)
    getList({ typeValue: item.typeValue, pid: item.id })
}

// 打开选择了人的弹框
function openhasSelectPopup() {
    // 每次展开的时候 直接深拷贝一个数据拿过来用用啊
    state.copySelected = JSON.parse(JSON.stringify(state.selected))
    hasSelectPopup.value.open()
}

// 选择弹框下的确认人员
const confirmBtn = () => {
    state.selected = JSON.parse(JSON.stringify(state.copySelected))
    hasSelectPopup.value.close()
}

// 确认完选择的人员
const outputPeople = () => {
    const ids = state.selected.map((i) => i.id)
    emit("confirm", ids, state.selected)
    close()
}

// 移除
const removeItem = (item) => {
    state.copySelected = state.copySelected.filter((copyitem) => copyitem.id !== item.id)
}

// 打开选择组件
const open = async (typeArr, typeMultiple) => {
    // 不知道怎么用的话可以看文件同层级的demo
    state.typeList = typeArr
    state.selectType = state.typeList[0]
    isTypeMultiple.value = typeMultiple // 是否类型可以多选，就是可以选多个类型的人或班级
    await getList()
    selectorPopup.value?.open()
}

const close = () => {
    selectorPopup.value.close()
}

// 关闭选择组件
const pageBack = () => {
    if (state.isSearchPage) {
        state.isSearchPage = !state.isSearchPage
    } else {
        close()
    }
}

// —————————————————搜索———————————————————————

// 搜索
function searchSelectList(options = {}) {
    const params = {
        treeType: getTreeType(state.selectType),
        businessType: getBusinessType(state.selectType),
        code: null,
        isRule: false,
        searchKey: state.searchKey,
        pageNo: 1,
        pageSize: 500
    }

    http.post("/cloud/v3/tree/selectTree/search", params)
        .then(({ data }) => {
            state.searchreqListArr = data.list
        })
        .finally(() => {})
}

// 搜索选中这个item
const searchselectItemThis = (item) => {
    const index = state.searchselected.findIndex((i) => i.id === item.id)
    if (~index) {
        state.searchselected.splice(index, 1)
    } else {
        state.searchselected.push({ ...item, pName: item.pidName })
    }
}

// 点击确认选择搜索出来的人
const savasearch = () => {
    if (state.searchselected && state.searchselected.length) {
        state.searchselected.forEach((item) => {
            state.selected.push(item)
            state.isSearchPage = false
        })
    }
}

const showsearch = () => {
    // 点击搜索清除数据
    state.searchreqListArr = []
    state.searchselected = []
    state.searchKey = ""
    state.isSearchPage = true
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.selector_page {
    height: 100vh;
    width: 100vw;
    background: $uni-bg-color-grey;

    .type_box {
        width: calc(100% - 60rpx);
        padding: 30rpx;
        /* #ifdef MP-WEIXIN */
        // padding: calc(var(--status-bar-height) + 60rpx) 30rpx 30rpx 30rpx;
        /* #endif */
        background: $uni-bg-color;

        .type_class {
            background: #f1f2f6;
            display: flex;
            border-radius: 30rpx;
        }

        .type_item {
            height: 64rpx;
            border-radius: 30rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 40rpx;
            color: #5e5f60;
        }

        .select_type {
            background: var(--primary-color);
            color: $uni-text-color-inverse;
        }
    }

    .content {
        margin-top: 20rpx;
        padding: 30rpx;
        background: $uni-bg-color;

        .search_box {
            margin-bottom: 30rpx;
        }

        .breadcrumb_box {
            overflow-x: scroll;
            font-size: 28rpx;

            .breadcrumb {
                display: flex;

                .breadcrumb_item {
                    display: flex;
                    align-items: center;
                    white-space: nowrap;

                    .item_slot {
                        padding: 0 10rpx;
                        color: var(--primary-color);
                    }
                }

                .breadcrumb_item :last-of-type .item_slot {
                    color: $uni-text-color-grey;
                }

                .breadcrumb_item :last-of-type .icon {
                    display: none;
                }
            }
        }
    }

    .all_select_box {
        border-bottom: 1px solid $uni-border-color;

        .all_select {
            height: 100rpx;
            display: flex;
            align-items: center;

            .select_img_box {
                width: 36rpx;
                height: 36rpx;

                .select_img {
                    width: 36rpx;
                    height: 36rpx;
                }
            }

            .select_name {
                padding-left: 20rpx;

                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;

                text-align: left;
                font-style: normal;
            }
        }
    }

    .scroll_y {
        height: calc(100vh - 575rpx);
        /* #ifdef MP-WEIXIN */
        height: calc(100vh - (575rpx + var(--status-bar-height)));
        /* #endif */
    }

    .list_box {
        .list_item {
            border-bottom: 1rpx solid $uni-border-color;
            padding: 24rpx 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .item_box {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .left_box {
                    align-items: center;
                    display: flex;
                    height: 100%;
                    flex: 1;

                    .select_img_box {
                        width: 36rpx;
                        height: 36rpx;

                        .image_url {
                            width: 36rpx;
                            height: 36rpx;
                        }
                    }

                    .name_title {
                        padding-left: 20rpx;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: $uni-text-color;
                    }
                }

                .right_box {
                    padding-left: 30rpx;
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    border-left: 1rpx solid $uni-border-color;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 40rpx;

                    .next_img_box {
                        width: 40rpx;
                        height: 40rpx;

                        .next_img {
                            width: 40rpx;
                            height: 40rpx;
                        }
                    }

                    .next_name_active {
                        color: var(--primary-color);
                    }

                    .next_name_not_active {
                        color: $uni-text-color-grey;
                    }
                }
            }
            .elterns_list {
                .elterns_item {
                    padding: 8rpx 0;
                    align-items: center;
                    display: flex;
                    .select_img_box {
                        width: 36rpx;
                        height: 36rpx;

                        .image_url {
                            width: 36rpx;
                            height: 36rpx;
                        }
                    }
                    .name_title {
                        padding-left: 16rpx;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: $uni-text-color-grey;
                        align-items: center;
                        display: flex;
                    }
                    .relations_tip {
                        color: var(--primary-color);
                        background-color: #00b78136;
                        font-size: 16rpx;
                        padding: 4rpx 8rpx;
                        margin-left: 8rpx;
                        border-radius: 5rpx;
                    }
                }
            }
        }
    }

    .foot_box_out {
        display: flex;
        align-items: center;
        border-top: 1px solid $uni-border-color;
        position: absolute;
        bottom: 0;
        background: $uni-bg-color;
        background: $uni-bg-color;
        width: 100%;
        height: 155rpx;

        .foot_box {
            padding-left: 30rpx;
            padding-right: 30rpx;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .has_select {
                font-size: 24rpx;
                flex: 1;
            }

            .sub_box {
                display: flex;
                align-items: center;

                .up_img {
                    padding-left: 20rpx;
                    padding-right: 20rpx;
                }

                .sub_btn {
                    color: $uni-text-color-inverse;
                    width: 156rpx;
                    height: 80rpx;
                    background: var(--primary-color);
                    font-size: 28rpx;
                    border-radius: 10rpx;
                    text-align: center;
                    line-height: 80rpx;
                }
            }
        }
    }

    .has_select_popup {
        .titleBox {
            padding: 30rpx;

            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            line-height: 42rpx;
        }

        .hasListBox {
            padding-left: 30rpx;
            padding-right: 30rpx;
            height: 720rpx;
            overflow: auto;

            .hasListItem {
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-bottom: 1rpx solid $uni-border-color;
                padding: 30rpx 0rpx;

                .item_left {
                    display: flex;
                    align-items: center;

                    .item_left_avatar {
                        padding-right: 16rpx;
                    }

                    .item_left_name {
                        font-weight: 400;
                        font-size: 28rpx;
                        color: $uni-text-color;
                    }
                }

                .item_right {
                    .right_btn {
                        width: 96rpx;
                        height: 48rpx;
                        background: $uni-bg-color;
                        border-radius: 8rpx;
                        border: 1rpx solid $uni-border-color;

                        font-weight: 400;
                        font-size: 26rpx;
                        color: $uni-text-color;
                        line-height: 48rpx;
                        text-align: center;
                    }
                }
            }
        }

        .submitFootBox {
            position: fixed;
            width: 100%;
            bottom: 0;
            border-top: 1px solid $uni-border-color;
            background: $uni-bg-color;

            .submitFootBtn {
                margin: 30rpx;
                height: 92rpx;
                background: var(--primary-color);
                border-radius: 10rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 400;
                font-size: 32rpx;
                color: $uni-text-color-inverse;
            }
        }
    }

    //———————————————搜索—————————————————————————————
    .scroll_search_y {
        height: calc(100vh - 373rpx);
        /* #ifdef MP-WEIXIN */
        height: calc(100vh - (403rpx + var(--status-bar-height)));
        /* #endif */
    }

    .search_page {
        background: $uni-bg-color;

        .search_box {
            padding: 20rpx 30rpx;
        }

        .search_list_box {
            padding-left: 30rpx;
            padding-right: 30rpx;

            .item_select {
                padding: 30rpx 0rpx;
                display: flex;
                border-bottom: 1px solid $uni-border-color;
                align-items: center;
                justify-content: space-between;

                .select_img_box {
                    width: 36rpx;
                    height: 36rpx;

                    .select_img {
                        width: 36rpx;
                        height: 36rpx;
                    }
                }

                .avatar_box {
                    padding-left: 20rpx;
                }

                .select_name {
                    padding-left: 20rpx;

                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;

                    text-align: left;
                    font-style: normal;
                }
            }

            .item_select_left {
                display: flex;
                align-items: center;
                flex: 1;
            }

            .itemSelect_right {
                display: flex;
                flex-shrink: 0;
                align-items: center;
                border-left: 1px solid $uni-border-color;
                padding-left: 30rpx;

                .nextImgBox {
                    width: 40rpx;
                    height: 40rpx;

                    .nextImg {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }
        }

        .foot_box_out {
            .foot_box {
                justify-content: flex-end;
            }
        }
    }
}
</style>
<style lang="scss">
.selector_page {
    :deep(.uni-navbar--fixed) {
        z-index: 999 !important;
    }
}
</style>
