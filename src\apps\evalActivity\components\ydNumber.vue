<template>
    <div class="def_input">
        <div class="step_btn" @click="defChangeScore('minus')">-</div>
        <input class="input_num" type="text" placeholder="请评分" v-model="score" @blur="blurScore" />
        <div class="step_btn" @click="defChangeScore('add')">+</div>
    </div>
</template>

<script setup>
import { watch } from "vue"

const emit = defineEmits(["changeScore"])
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {}
        }
    },
    score: {
        type: [Number, String],
        default: ""
    },
    step: {
        type: Number,
        default: 1
    },
    min: {
        type: Number,
        default: 0
    },
    max: {
        type: Number,
        default: 0
    },
    ruleData: {
        type: Object,
        default: () => {
            return {}
        }
    }
})

const score = ref("")

watch(
    () => props.score,
    (val) => {
        score.value = val ? parseFloat(val.toFixed(2)) : val
    },
    {
        immediate: true,
        deep: true
    }
)

const j = computed(() => props.data)
const minScore = computed(() => props.min)
const maxScore = computed(() => props.max)
const ruleData = computed(() => props.ruleData)

function defChangeScore(type) {
    const step = ruleData?.value?.minRatingStep ? parseFloat(ruleData?.value?.minRatingStep) : 1
    if (type == "add") {
        const newScore = parseFloat(score.value + step)
        if (newScore <= maxScore.value) {
            score.value = parseFloat(newScore.toFixed(2))
            emit("changeScore", parseFloat(newScore.toFixed(2)))
        }
    } else if (type == "minus") {
        const newScore = parseFloat(score.value - step)
        if (newScore >= minScore.value) {
            score.value = parseFloat(newScore.toFixed(2))
            emit("changeScore", parseFloat(newScore.toFixed(2)))
        }
    }
}

function blurScore(e) {
    const newScore = parseFloat(e.detail.value) || 0
    if (newScore > maxScore.value || newScore < minScore.value) {
        score.value = 0
        emit("changeScore", 0)
    } else {
        score.value = parseFloat(newScore.toFixed(2))
        emit("changeScore", parseFloat(newScore.toFixed(2)))
    }
}
</script>

<style lang="scss" scoped>
.def_input {
    padding: 20rpx;
    border: 1rpx solid #dadada;
    border-radius: 10rpx;
    background: #ffffff;
    display: flex;
    justify-content: space-between;

    :deep(.uni-input-wrapper) {
        text-align: center;
    }
    .input_num {
        text-align: center;
    }
    .step_btn {
        width: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 48rpx;
        padding: 0;
        color: #fff;
        background: var(--primary-color);
    }
}
</style>
