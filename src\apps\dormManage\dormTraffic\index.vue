<template>
    <div class="dorm_traffic">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="宿舍通行" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
        <lgd-tab v-model="state.tabKey" :tabValue="state.tabList" @change="tabChangeFn" underlineColor="#4566D5" text-color="#4566D5" />
        <!-- 统计卡片 -->
        <div class="pass_card">
            <div class="filter">
                <div class="date" @click="trafficCalendarFn">
                    <span>{{ state.parame?.queryDate }}</span>
                    <div class="image"></div>
                </div>
                <!--   -->
                <div class="criteria" v-if="state.roleType == 1" @click="filterCriteria('class')">
                    <span class="text">{{ state.screen.className }}</span>
                    <div class="image"></div>
                </div>
            </div>
            <div class="card">
                <div class="title">
                    <span class="text">
                        {{ state.tabKey === 0 ? "学生出入宿舍情况" : "宿管出入宿舍情况" }}
                        <span class="unit">（人）</span>
                    </span>
                    <!-- <div class="image"></div> -->
                </div>
                <div class="count">
                    <div class="item">
                        <span class="number">{{ state.count?.sumCount }}</span>
                        <span class="count_title">通行总次数</span>
                    </div>
                    <div class="border"></div>
                    <div class="item">
                        <span class="number">{{ state.count?.inCount }}</span>
                        <span class="count_title">入寝次数</span>
                    </div>
                    <div class="border"></div>
                    <div class="item">
                        <span class="number">{{ state.count?.outCount }}</span>
                        <span class="count_title">出寝次数</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- 列表 -->
        <div class="room_list">
            <!-- 筛选 -->
            <div class="filter_criteria">
                <div class="criteria" @click="filterCriteria('building')">
                    <span class="text">{{ state.screen?.buildingName }}</span>
                    <div class="image"></div>
                </div>
                <div class="criteria" v-if="state.tabKey === 0" @click="filterCriteria('floor')">
                    <span class="text">{{ state.screen?.floorName }}</span>
                    <div class="image"></div>
                </div>
                <div class="criteria" @click="filterCriteria('direction')">
                    <span class="text">{{ state.screen?.inOutName }}</span>
                    <div class="image"></div>
                </div>
            </div>
            <!-- 学生列表 -->
            <div class="list" v-if="state.tabKey === 0">
                <div style="width: 100%" v-if="state.studentList?.length">
                    <div class="item" v-for="(item, index) in state.studentList" :key="index">
                        <div class="top" @click="lookInfoFn(item)">
                            <span class="text"> 寝室号：{{ item?.roomNum || "-" }}（{{ item?.peppleNum || 0 }}人间） </span>
                            <uni-icons type="forward" size="16" color="#000000" />
                        </div>
                        <div class="bottom">
                            <div class="text">
                                通行方向：<span class="way" :class="typeColor[item?.direction]">
                                    {{ inOutList[item?.direction] }}
                                </span>
                            </div>
                            <div class="count">
                                {{ item?.direction === 1 ? item?.outCount : item?.inCount }} /
                                {{ item?.peppleNum }}
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="no_data">
                    <yd-empty text="暂无数据" />
                </div>
                <uni-load-more iconType="auto" :status="status" />
            </div>
            <!-- 宿管列表 -->
            <div v-else class="housemaster">
                <CollapseList :list="state.personList" v-if="state.personList?.length">
                    <template #left="{ data }">
                        {{ data?.userName }}
                    </template>
                    <template #body="{ data }">
                        {{ data?.lastTrafficDetail.time }}
                    </template>
                    <template #right="{ data }">
                        <span class="right_data" :class="typeColor[data?.direction]">{{
                            {
                                1: "出寝",
                                2: "入寝"
                            }[data?.direction]
                        }}</span>
                    </template>
                    <template #content="{ childList }">
                        <div class="info" v-if="childList.trafficDetailList?.length">
                            <div class="title_box">
                                <span class="text">详细记录</span>
                                <span class="count">今日通行{{ childList.trafficDetailList?.length || 0 }}次</span>
                            </div>
                            <div class="info_list">
                                <div class="info_item" v-for="(item, index) in childList.trafficDetailList" :key="index">
                                    <span class="text" :style="index + 1 === childList.trafficDetailList?.length ? 'border: none' : ''">
                                        <span class="type" :class="typeColor[item?.direction]">
                                            {{ inOutList[item?.direction] }}
                                        </span>
                                        <div v-if="index === 0 || index + 1 === childList.trafficDetailList?.length" class="earliest_latest">
                                            {{ index === 0 ? "最早" : "最晚" }}
                                        </div>
                                        <i :class="typeBackground[item?.direction]" class="icon"></i>
                                    </span>
                                    <div class="content">
                                        <span class="content_text">{{ item?.time }}</span>
                                        <span class="content_place">{{ item?.siteName }} — {{ item?.deviceName }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </CollapseList>
                <div class="no_data" v-else><yd-empty text="暂无数据" /></div>
            </div>
        </div>
        <div class="add_release" v-if="state.tabKey === 0 && state.siRelease" @click="addTrafficPersonnel">
            <div class="icon"></div>
            <span class="text">放行</span>
        </div>
        <uni-calendar :lunar="true" @confirm="confirm" :insert="false" ref="calendarPopup" />
        <SelectPopup @closePopup="classClosePopup" ref="selectClassRef" :list="classsPopupList" title="请选择" />
        <SelectPopup @closePopup="floorClosePopup" ref="floorRef" :list="floorPopupList" title="请选择" />
        <SelectPopup @closePopup="buildingClosePopup" ref="buildingRef" :list="buildingPopupList" title="请选择" />
        <SelectPopup @closePopup="inOutClosePopup" ref="inOutRef" :list="inOutPopupList" title="请选择" />
    </div>
</template>

<script setup>
import { onReachBottom } from "@dcloudio/uni-app"
import { inOutList } from "../utils/staticData"
import lgdTab from "@/subModules/components/lgd-tab/components/lgd-tab/lgd-tab.vue"
import useQueryList from "../hooks/useQueryList.js"
import CollapseList from "../components/collapseList.vue"
import SelectPopup from "../components/selectPopup.vue"
import dayjs from "dayjs"

const typeColor = { 1: "type_enter", 2: "type_leave" }
const typeBackground = { 1: "icon_enter", 2: "icon_leave" }
const inOutRef = ref(null)
const floorRef = ref(null)
const buildingRef = ref(null)
const selectClassRef = ref(null)

const { getList, dataList, pageNo, status } = useQueryList()

const state = reactive({
    count: {
        sumCount: 0,
        inCount: 0,
        outCount: 0
    },
    tabKey: 0,
    tabList: ["学生", "宿管"],
    parame: {
        direction: null,
        buildingId: null,
        classId: null,
        dormSiteId: null,
        floor: null,
        queryDate: dayjs().format("YYYY-MM-DD") || ""
    },
    screen: {
        buildingName: "全部楼栋",
        className: "全部班级",
        floorName: "全部楼层",
        inOutName: "全部方向"
    },
    isLoadAll: false,
    pagination: {
        pageNo: 1,
        pageSize: 10
    },
    studentList: [],
    personList: [],
    filterType: "direction",
    roleType: 0,
    siRelease: false
})

const calendarPopup = ref(null)
const floorPopupList = ref([])
const classsPopupList = ref([])
const buildingPopupList = ref([])
const inOutPopupList = ref([])
const queryParams = ref({})

onLoad((params) => {
    Object.keys(params).forEach((key) => {
        params[key] = decodeURIComponent(params[key])
    })
    queryParams.value = params || {}
    if (params) {
        state.roleType = params?.roleType || 0
        if (params.roleType == 1) {
            state.tabList = ["学生", ""]
        }
    }
})

function getPage(params) {
    return http.post("/app/dorm/pass/pageDormPass", params)
}

const statisticsPageDormByBuildingFn = async () => {
    const {
        pagination,
        parame: { buildingId, queryDate, direction, floor }
    } = state
    const params = {
        buildingId,
        floor,
        queryDate,
        direction,
        identityType: state.tabKey, // 0：学生 1：老师
        classId: state.parame.classId,
        ...pagination
    }
    //   let URL = "/app/dorm/traffic/statistics/pageDormByBuilding"
    // if (state.tabKey) {
    //   // 宿管
    //   URL = "/app/dorm/traffic/statistics/detailList"
    // }
    http.post("/app/dorm/traffic/statistics/pageDormByBuilding", params).then(({ data }) => {
        const { inCount = 0, outCount = 0, totalCount = 0, page } = data || {}
        state.count = {
            sumCount: totalCount || 0,
            inCount,
            outCount
        }
        if (state.tabKey) {
            // 暂时没有  页面给空就行
            // 宿管
            state.personList = page?.list || []
        } else {
            // 学生
            state.studentList = page?.list || []
        }
    })
}
function tabChangeFn(type) {
    statisticsPageDormByBuildingFn()
}

onReachBottom(() => {
    getList(getPage, parame)
})

function confirm(e) {
    state.parame.queryDate = e?.fulldate
    calendarPopup.value.close()
    dataList.value = []
    pageNo.value = 1
    state.studentList = []
    statisticsPageDormByBuildingFn(state.parame)
}
// 日期
function trafficCalendarFn() {
    calendarPopup.value.open()
}

function filterCriteria(key) {
    state.filterType = key
    switch (key) {
        case "building":
            buildingRef.value.open()
            state.screen.floorName = "全部楼层"
            state.parame.floor = ""
            state.parame.direction = null
            state.screen.inOutName = "全部方向"

            break
        case "class":
            selectClassRef.value.open()

            break
        case "floor":
            floorRef.value.open()

            break
        case "direction":
            inOutPopupList.value = [
                { name: "全部方向", value: null },
                { name: "出", value: 1 },
                { name: "入", value: 2 }
            ]
            inOutRef.value.open()
            break
        default:
            break
    }
}

function floorClosePopup(value) {
    console.log(value)
    if (value) {
        state.screen.floorName = value.name || ""
        state.parame.floor = value.id || ""
        tabChangeFn(state.tabKey)
    }
}

function buildingClosePopup(value) {
    if (value) {
        floorPopupList.value = value.children || []
        state.screen.buildingName = value.name || ""
        state.parame.buildingId = value.id || ""
        tabChangeFn(state.tabKey)
    }
}

function classClosePopup(value) {
    if (value) {
        state.screen.className = value.name || ""
        state.parame.classId = value.value || ""
        tabChangeFn(state.tabKey)
    }
}

function inOutClosePopup(value) {
    if (value) {
        state.parame.direction = value.value || null
        state.screen.inOutName = value.name || ""
        tabChangeFn(state.tabKey)
    }
}

function lookInfoFn(item) {
    navigateTo({
        url: "/apps/dormManage/dormTraffic/trafficInfo",
        query: {
            dormSiteId: item.dormSiteId,
            queryDate: state.parame.queryDate
        }
    })
}

function getBuildingCommon() {
    http.get("/app/dormitory/buildingCommon/tree", {}).then(({ data }) => {
        buildingPopupList.value = [{ name: "全部", value: null }, ...data]
    })
}

function getClasssCommon() {
    http.get("/app/master/class/queryClassMaterList", {}).then((res) => {
        classsPopupList.value =
            res.data?.map((i) => {
                return {
                    value: i.id || "",
                    name: i.name || ""
                }
            }) || []
    })
}

function addTrafficPersonnel() {
    navigateTo({
        url: "/apps/dormManage/dormTraffic/addTrafficPersonnel"
    })
}
function getSchoolQuickPassSettingByTypeInfo() {
    http.post("/app/quick/passSetting/getSchoolQuickPassSettingByType", { type: 1 })
        .then(({ data }) => {
            state.siRelease = data.isEnabled
        })
        .catch((error) => {
            console.log(error)
        })
}
onMounted(() => {
    getBuildingCommon()
    getClasssCommon()
    getSchoolQuickPassSettingByTypeInfo()
})
</script>

<style lang="scss" scoped>
.dorm_traffic {
    min-height: 100vh;
    padding-bottom: 50rpx;
    background: $uni-bg-color-grey;
}

.calendar_popup {
    min-height: 500rpx;
    padding-bottom: 30rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    position: relative;

    .title_box {
        background: #ffffff;
        position: absolute;
        top: 20rpx;
        left: 30rpx;
        font-size: 28rpx;
        color: #333333;
        line-height: 48rpx;
    }
}

.room_list {
    background: #ffffff;
    margin-top: 20rpx;

    .filter_criteria {
        height: 100rpx;
        border-bottom: 1rpx solid #d9d9d9;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0rpx 30rpx;

        .criteria {
            display: flex;
            align-items: center;

            .image {
                height: 28rpx;
                width: 28rpx;
                background: url("https://alicdn.1d1j.cn/announcement/20230726/20daa8cd92c245cda0c06f648bc9e6f8.png") no-repeat;
                background-size: contain;
            }

            .text {
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
            }
        }
    }

    .list {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 28rpx;

        .item {
            width: 90%;
            height: 110rpx;
            box-shadow:
                0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.05),
                0rpx -4rpx 12rpx 0rpx rgba(0, 0, 0, 0.03);
            border-radius: 20rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 28rpx;
            margin-bottom: 28rpx;

            .top,
            .bottom {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;

                .text {
                    font-weight: 400;
                    line-height: 40rpx;
                    font-size: 28rpx;
                }

                .way {
                    font-size: 28rpx;
                    font-weight: 400;
                    line-height: 40rpx;
                }

                .count {
                    font-size: 40rpx;
                    color: #333333;
                    line-height: 58rpx;
                }
            }

            .top .text {
                color: #333333;
            }

            .bottom .text {
                color: #666666;
            }
        }
    }
}

.add_release {
    position: fixed;
    bottom: 60rpx;
    right: 30rpx;
    width: 112rpx;
    height: 112rpx;
    background: #4566d5;
    box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(69, 102, 213, 0.2);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .icon {
        background: url("https://alicdn.1d1j.cn/announcement/20230727/c02616ca19404386b6d7ba5483c58354.png") no-repeat;
        background-size: contain;
        height: 44rpx;
        width: 44rpx;
    }

    .text {
        font-size: 24rpx;
        font-weight: 400;
        color: #ffffff;
        line-height: 34rpx;
    }
}

.pass_card {
    height: 304rpx;
    background: #ffffff;
    margin-top: 20rpx;
    padding: 30rpx;

    .filter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        line-height: 40rpx;

        .criteria {
            display: flex;
            align-items: center;

            .image {
                height: 28rpx;
                width: 28rpx;
                background: url("https://alicdn.1d1j.cn/announcement/20230726/20daa8cd92c245cda0c06f648bc9e6f8.png") no-repeat;
                background-size: contain;
            }

            .text {
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
            }
        }

        .date {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .image {
                height: 28rpx;
                width: 28rpx;
                background: url("https://alicdn.1d1j.cn/announcement/20230726/20daa8cd92c245cda0c06f648bc9e6f8.png") no-repeat;
                background-size: contain;
            }
        }
    }

    .card {
        width: 690rpx;
        height: 234rpx;
        background: url("https://alicdn.1d1j.cn/announcement/20230727/7582f3053f32448798f5a7f424f38f41.png") no-repeat;
        background-size: contain;
        margin-top: 30rpx;
        position: relative;

        .title {
            width: 91%;
            display: flex;
            justify-content: space-between;
            position: absolute;
            top: 30rpx;
            left: 0rpx;
            padding: 0rpx 30rpx;

            .text {
                font-size: 30rpx;
                font-weight: 500;
                color: #ffffff;
                line-height: 42rpx;
            }

            .unit {
                font-size: 24rpx;
                font-weight: 400;
                line-height: 34rpx;
            }

            // .image {
            //   height: 44rpx;
            //   width: 44rpx;
            //   background: url("https://alicdn.1d1j.cn/announcement/20230727/1d1f112f7cd2462582231859f66ce956.png")
            //     no-repeat;
            //   background-size: contain;
            // }
        }

        .count {
            position: absolute;
            bottom: 30rpx;
            left: 30rpx;
            width: 91%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100rpx;

            .item {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                color: #ffffff;
                height: 100%;
                width: 32%;

                .number {
                    font-size: 40rpx;
                    line-height: 58rpx;
                }

                .count_title {
                    font-size: 24rpx;
                    font-weight: 400;
                    line-height: 26rpx;
                }
            }

            .border {
                width: 1rpx;
                height: 60rpx;
                background: #ffffff;
                border-radius: 7rpx;
                opacity: 0.5;
            }
        }
    }
}

.housemaster {
    .right_data {
        font-size: 28rpx;
        font-weight: 400;
        color: #faad14;
        line-height: 40rpx;
    }

    .info {
        background: $uni-bg-color-grey;
        border-radius: 20rpx;
        margin: 28rpx;

        .title_box {
            height: 88rpx;
            border-bottom: 1rpx solid #d9d9d9;
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: #333333;
            padding: 0rpx 24rpx;

            .text {
                font-size: 28rpx;
                font-weight: 600;
                line-height: 40rpx;
            }

            .count {
                font-size: 26rpx;
                font-weight: 400;
                line-height: 36rpx;
            }
        }

        .info_list {
            display: flex;
            flex-direction: column;
            padding: 30rpx 28rpx 0rpx 36rpx;

            .info_item {
                display: flex;
                width: 100%;

                .text {
                    font-size: 28rpx;
                    padding: 0rpx 36rpx;
                    font-weight: 400;
                    line-height: 40rpx;
                    border-right: 1rpx solid #d9d9d9;
                    position: relative;

                    .type {
                        position: absolute;
                        top: -5px;
                        left: 6rpx;
                        font-size: 28rpx;
                        font-weight: 400;
                        line-height: 40rpx;
                    }

                    .icon {
                        position: absolute;
                        top: 0rpx;
                        right: -9rpx;
                        width: 16rpx;
                        height: 16rpx;
                        border-radius: 50%;
                    }

                    .icon_leave {
                        background: #4566d5;
                    }

                    .icon_enter {
                        background: #faad14;
                    }

                    .earliest_latest {
                        position: absolute;
                        top: 18px;
                        left: -14rpx;
                        width: 64rpx;
                        height: 28rpx;
                        text-align: center;
                        border-radius: 14rpx;
                        border: 1rpx solid #333333;
                        font-size: 20rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 28rpx;
                    }
                }

                .content {
                    display: flex;
                    flex-direction: column;
                    padding: 0rpx 0rpx 50rpx 28rpx;
                    margin-top: -12rpx;

                    .content_text {
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 40rpx;
                    }

                    .content_place {
                        font-size: 26rpx;
                        font-weight: 400;
                        color: #666666;
                        line-height: 36rpx;
                    }
                }
            }
        }
    }

    .type_leave {
        color: #4566d5;
    }

    .type_enter {
        color: #faad14;
    }
}

.type_leave {
    color: #4566d5;
}

.type_enter {
    color: #faad14;
}

.no_data {
    text-align: center;
    line-height: 100rpx;
}

:deep(.uni-calendar-item--checked) {
    background: #4566d5;
}

:deep(.uni-calendar-item--isDay) {
    background: #4566d5;
    color: #fff !important;
}

:deep(.uni-calendar-item--isDay-text) {
    color: #4566d5;
}
</style>
