<template>
    <div class="school_table">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="课表" :leftWidth="navBarLeftWidth(100)" :rightWidth="navBarRightWidth(100)">
            <!-- #ifdef H5 || H5-WEIXIN-->
            <template v-slot:right>
                <view class="right_class" @click="selectWeekFn">
                    <text class="text">第{{ selectWeek || 0 }}周</text>
                    <image class="image" src="@nginx/workbench/moralEducationEvaluation/selectWeek.png" alt="" />
                </view>
            </template>
            <!-- #endif -->
            <!-- #ifdef APP-PLUS-->
            <template v-slot:right>
                <view class="right_class" @click="selectWeekFn">
                    <text class="text">第{{ selectWeek || 0 }}周</text>
                    <image class="image" src="@nginx/workbench/moralEducationEvaluation/selectWeek.png" alt="" />
                </view>
            </template>
            <!-- #endif -->
        </uni-nav-bar>
        <!-- #ifdef MP-WEIXIN -->
        <view class="right_class" @click="selectWeekFn">
            <text class="text">第{{ selectWeek || 0 }}周</text>
            <image class="image" src="@nginx/workbench/moralEducationEvaluation/selectWeek.png" alt="" />
        </view>
        <!-- #endif -->
        <!-- tabs切换 -->
        <uv-tabs lineWidth="20" lineColor="var(--primary-color)" :current="tabsCurrent" :scrollable="false" :activeStyle="{ color: 'var(--primary-color)' }" :inactiveStyle="{ color: '#999999' }" :customStyle="{ background: '#fff' }" :list="tabsList" @click="tabsClick"></uv-tabs>
        <div v-if="weekList && weekList.length">
            <my-table ref="myTableRef" v-if="tabsId == 1 && !['eltern', 'student'].includes(roleCode)" :thisWeek="thisWeek" @backWeek="backWeekFn" />
            <class-table ref="classTableRef" v-if="tabsId == 2" :thisWeek="thisWeek" @backWeek="backWeekFn" />
            <site-table ref="siteTableRef" v-if="tabsId == 3" :thisWeek="thisWeek" @backWeek="backWeekFn" />
        </div>
        <yd-empty text="本学期暂无周次" v-else :isMargin="true" />
        <yd-select-popup ref="selectWeekRef" title="周次选择" :list="weekList" @closePopup="closePopup" :selectId="[selectWeek]"></yd-select-popup>
    </div>
</template>

<script setup>
import MyTable from "./components/myTable.vue"
import ClassTable from "./components/classTable.vue"
import SiteTable from "./components/siteTable.vue"
import { nextTick } from "vue"

const { system, user } = store()
const selectWeekRef = ref() // 选择周次弹窗
const myTableRef = ref(null) // 我的课表
const classTableRef = ref(null) // 班级课表
const siteTableRef = ref(null) // 场地课表

const tabsCurrent = ref(0) // tabs当前索引
const weekList = ref([]) // 周次列表
const thisWeek = ref(null) // 当前的周次
const selectWeek = ref(null) // 选中的周次
const classList = ref([]) // 班级列表
const siteList = ref([]) // 场地列表
const roleCode = computed(() => {
    return user?.identityInfo?.roleCode
})
const tabsId = ref(null) // tabs当前id
const tabsList = ref([])

// // 调用接口
// function getStoreList() {
//     nextTick(() => {
//         if (tabsId.value == 1) {
//             // 调用我的课表列表
//             myTableRef.value?.getList()
//         } else if (tabsId.value == 2) {
//             classTableRef.value?.getList()
//         } else if (tabsId.value == 3) {
//             siteTableRef.value?.getList()
//         }
//     })
// }

function getTableList() {
    setTimeout(() => {
        if (tabsId.value == 1) {
            // 调用我的课表列表
            myTableRef.value?.getList()
        } else if (tabsId.value == 2) {
            classTableRef.value?.getList()
        } else if (tabsId.value == 3) {
            siteTableRef.value?.getList()
        }
    }, 500)
}

// 选择周次
function closePopup(obj) {
    if (!obj || obj.value == selectWeek.value) return
    system.setAppData({ sys: "schoolTable", data: { tableSelectWeek: obj.value } })
    selectWeek.value = obj.value
    getTableList()
}

// tabs切换点击
function tabsClick(item) {
    tabsCurrent.value = item.index
    tabsId.value = item.id
    getTableList()
}

// 选择周次
function selectWeekFn() {
    selectWeekRef.value.open()
}

// 回到当前周当前天
function backWeekFn() {
    selectWeek.value = thisWeek.value
    system.setAppData({ sys: "schoolTable", data: { tableSelectWeek: thisWeek.value } })
    getTableList()
}

// 获取周次
async function getWeekList() {
    await http.get("/app/timetable/getTimetableWeekList").then((res) => {
        thisWeek.value = res.data.thisWeek
        selectWeek.value = res.data.thisWeek
        weekList.value = res.data.weekList.map((i) => {
            return {
                ...i,
                label: `第${i.week}周（${i.startTime}-${i.endTime}）`,
                value: i.week
            }
        })
        const storeData = {
            tableThisWeek: res.data.thisWeek,
            tableSelectWeek: res.data.thisWeek,
            tableWeekList: weekList.value
        }
        system.setAppData({ sys: "schoolTable", data: storeData })
        getTableList()
    })
}

// 获取班级
async function getClassList() {
    await http.get("/app/timetable/getClassList").then((res) => {
        classList.value = res.data
        // 设置子应用数据
        system.setAppData({ sys: "schoolTable", data: { tableClassList: classList.value } })
    })
}

// 获取场地
async function getSiteList() {
    await http.get("/app/timetable/getSiteList").then((res) => {
        siteList.value = res.data
        // 设置子应用数据
        system.setAppData({ sys: "schoolTable", data: { tableSiteList: siteList.value } })
    })
}

async function tabsDefault() {
    const isTeacher = !["eltern", "student"].includes(roleCode.value)
    tabsId.value = isTeacher ? 1 : 2
    tabsList.value = isTeacher
        ? [
              { name: "我的课表", id: 1 },
              { name: "班级课表", id: 2 },
              { name: "场地课表", id: 3 }
          ]
        : [
              { name: "班级课表", id: 2 },
              { name: "场地课表", id: 3 }
          ]
}

onMounted(async () => {
    await tabsDefault()
    await getClassList()
    getSiteList()
    nextTick(async () => {
        await getWeekList()
    })
})
</script>

<style lang="scss" scoped>
.school_table {
    min-height: calc(100vh - 10rpx);
    background: $uni-bg-color-grey;
    padding-bottom: 10rpx;
    .right_class {
        // #ifdef MP-WEIXIN
        padding: 10rpx;
        // #endif
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .text {
            font-size: 28rpx;
            font-weight: 400;
            color: $uni-text-color;
            line-height: 40rpx;
        }

        .image {
            width: 44rpx;
            height: 44rpx;
        }
    }
}
</style>
