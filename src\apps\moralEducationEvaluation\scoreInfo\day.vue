<template>
    <div class="scoreInfo">
        <!-- 头部自定义导航栏 -->
        <view class="head">
            <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="pageParams.week || '-'" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        </view>
        <day-score-list :scoreObj="state.scoreObj" :totalScore="pageParams?.totalScore || '0分'" />
        <div class="day_list" v-if="state.list && state.list.length > 0">
            <uni-list :border="false">
                <uni-list-item v-for="(item, index) in state.list" :key="index" :title="(item.seq || index + 1) + '. ' + item.secondIndicator" :note="item.groupName" @click="notesFn(item)" link>
                    <template v-slot:footer>
                        <div class="footer_class">
                            <img class="emote" :src="item.score > 0 ? '@nginx/workbench/moralEducationEvaluation/happyIcon.png' : '@nginx/workbench/moralEducationEvaluation/sadIcon.png'" alt="" />
                            <span class="text" :class="item.score > 0 ? 'bonus_color' : 'deduct_color'">{{ item.score + "分" }}</span>
                        </div>
                    </template>
                </uni-list-item>
            </uni-list>
        </div>
        <yd-empty :isMargin="true" text="暂无数据" v-else />
        <uni-popup ref="notesProps" type="bottom">
            <div class="notes_props">
                <div class="title">
                    <span class="text">备注</span>
                    <img class="image" @click="closeFn" src="@nginx/workbench/moralEducationEvaluation/ruleClose.png" alt="" />
                </div>
                <div class="textarea_box">
                    <textarea :disabled="true" class="textarea" placeholder="暂无备注" :maxlength="150" auto-height v-model="state.notes"></textarea>
                </div>
            </div>
        </uni-popup>
    </div>
</template>

<script setup>
import DayScoreList from "../components/dayScoreList.vue"
import { onLoad } from "@dcloudio/uni-app"

const pageParams = ref({})
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    pageParams.value = options
})
const notesProps = ref()
const state = reactive({
    notes: "",
    scoreObj: {
        totalAddScore: 0,
        totalSubtractScore: 0
    },
    list: []
})
function notesFn(item) {
    state.notes = item.remark
    notesProps.value.open()
}

function clickLeft() {
    uni.navigateBack()
}

function dayInfoFn() {
    const { activityId, cycleId, targetId, backUpDate } = pageParams.value
    const params = {
        activityId, // 活动标识
        cycleId, // 周期标识
        targetId, // 我的班级
        scoreTime: backUpDate
    }
    http.post("/app/moralEducationActivityMobile/scoringDetail", params).then((res) => {
        state.scoreObj = res?.data || {}
        state.list = res.data?.lineList || []
    })
}

function closeFn() {
    notesProps.value.close()
}
onMounted(() => {
    dayInfoFn()
})
</script>

<style lang="scss" scoped>
.head {
    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: $uni-text-color;
    }
}
.day_list {
    max-height: calc(100vh - 350rpx);
    overflow: auto;
    .footer_class {
        display: flex;
        align-items: center;
        .text {
            font-size: 30rpx;
            font-weight: 400;
            line-height: 42rpx;
        }
        .emote {
            width: 48rpx;
            height: 48rpx;
            margin-right: 20rpx;
        }
        .bonus_color {
            color: var(--primary-color);
        }
        .deduct_color {
            color: $uni-color-error;
        }
    }
}
.notes_props {
    min-height: 300rpx;
    max-height: 750rpx;
    overflow-y: auto;
    background: $uni-bg-color;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding-bottom: 90rpx;

    .title {
        height: 100rpx;
        display: flex;
        align-items: center;

        .text {
            font-size: 34rpx;

            font-weight: 500;
            color: $uni-text-color;
            line-height: 48rpx;
            width: 100vh;
            text-align: center;
        }

        .image {
            height: 44rpx;
            width: 44rpx;
            margin-right: 18rpx;
        }
    }
    .textarea_box {
        padding: 30rpx;
        .textarea {
            padding: 20rpx;
            width: 94%;
            background: $uni-bg-color-grey;
            min-height: 160rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: $uni-text-color;
            line-height: 40rpx;
        }
    }
}
</style>
