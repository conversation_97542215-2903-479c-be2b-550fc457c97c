<template>
    <view class="achievement">
        <z-paging ref="paging" :auto="false" @query="getList" v-model="state.examBasicList">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="成绩管理" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <view class="reset-select">
                    <view class="reset-select-item">
                        <uni-data-select v-model="state.examTimeType" :localdata="examTimeType" @change="changeSelectTimeType" :clear="false"></uni-data-select>
                    </view>
                    <view class="reset-select-item type">
                        <uni-data-select v-model="state.type" :localdata="examType" @change="changeSelectTyp" :clear="false" placeholder="考试类型"></uni-data-select>
                    </view>
                </view>
            </template>
            <div v-if="state.examBasicList && state.examBasicList.length">
                <view class="achievement-item" v-for="item in state.examBasicList" :key="item">
                    <view class="title">
                        <text>{{ item.name }}</text>
                    </view>
                    <view class="content">
                        <view class="list-item" v-for="it in state.examBasicInfo" :key="it.key" @click="handlerToDetails(item)">
                            <span class="label">{{ it.label }}</span>
                            <span class="value" v-if="it.key == 'isImport'">{{ item[it.key] ? "已导入" : "未导入" }}</span>
                            <span class="value" v-else>{{ item[it.key] }}</span>
                        </view>
                    </view>
                </view>
            </div>
            <view v-if="!state.examBasicList?.length && pageLoading" class="loading">
                <uv-loading-icon :show="pageLoading" text="加载中..." :vertical="true"></uv-loading-icon>
            </view>

            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup name="scoreManage">
const paging = ref(null)
const pageLoading = ref(false)
const state = reactive({
    examBasicInfo: [
        { key: "examTime", label: "考试日期" },
        { key: "typeName", label: "考试类型" },
        { key: "subjectName", label: "考试科目" },
        { key: "isImport", label: "成绩导入" },
        { key: "createTime", label: "创建时间" }
    ],
    examBasicList: [],
    examTimeType: 0,
    type: 0,
    subjectName: ""
})
const examTimeType = [
    { value: 0, text: "全部时间" },
    { value: 1, text: "本学期" }
]
const examType = [
    { value: 0, text: "全部类型" },
    { value: 1, text: "期末统考 " },
    { value: 2, text: "期中统考" },
    { value: 3, text: "学科小考" }
]

const getList = async (pageNo, pageSize) => {
    try {
        pageLoading.value = true
        const { examTimeType, type } = state
        const params = {
            pageNo,
            pageSize,
            examTimeType,
            type: type || null
        }
        await http
            .post("/app/score/exam/employeePage", params)
            .then(({ data }) => {
                paging.value?.complete(data.list)
            })
            .finally(() => {
                pageLoading.value = false
            })
    } catch (error) {
        console.log(error, "errorerror")
        pageLoading.value = false
        paging.value?.complete(false)
    }
}
// 跳详情
const handlerToDetails = (item) => {
    // * identity 0 是学生  1是教职工  2是家长
    navigateTo({
        url: "/apps/scoreManage/scoreList",
        query: { identity: "teacher", examId: item.id }
    })
}
// 下拉查询
const changeSelectTimeType = (item) => {
    state.examTimeType = item
    paging.value?.reload()
}

const changeSelectTyp = (item) => {
    state.type = item
    paging.value?.reload()
}

onLoad(() => {
    nextTick(() => {
        paging.value?.reload()
    })
})
</script>

<style scoped lang="scss">
.achievement {
    background-color: $uni-bg-color-grey;
    min-height: 100vh;

    .reset-select {
        display: flex;
        justify-content: space-between;
        padding: 0 20rpx 20rpx;
        background-color: $uni-bg-color;

        .reset-select-item {
            width: 204rpx;

            :deep(.uni-select__input-text) {
                font-size: 28rpx;
                color: #666666;
            }

            :deep(.uni-select) {
                border: none;
                height: 20rpx;
                margin-top: 20rpx;

                .uni-select__input-text {
                    width: auto;
                }

                .uni-icons:before {
                    content: "";
                    display: block;
                    border: 10rpx solid transparent;
                    margin-left: 6rpx;
                }

                .uniui-bottom:before {
                    border-top: 10rpx solid var(--primary-color);
                    border-bottom-width: 1px;
                    margin-top: 6rpx;
                }

                .uniui-top:before {
                    border-bottom-color: var(--primary-color);
                    border-top-width: 1px;
                    margin-bottom: 6rpx;
                }
            }

            &.type {
                :deep(.uni-select__input-box) {
                    justify-content: end;
                }
            }
        }
    }

    .achievement-item {
        margin: 20rpx 30rpx;
        padding: 0 30rpx;
        background-color: $uni-bg-color;
        border-radius: 20rpx;

        .title {
            border-bottom: 1rpx solid $uni-border-color;
            padding: 30rpx 0;
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            line-height: 42rpx;
        }

        .content {
            padding: 12rpx 0;

            .list-item {
                display: flex;
                justify-content: space-between;
                padding: 12rpx 0;

                .label {
                    color: $uni-text-color-grey;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 40rpx;
                    min-width: 140rpx;
                }

                .value {
                    text-align: right;
                    padding-left: 12rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #262626;
                    line-height: 40rpx;
                }
            }
        }
    }
    .loading {
        margin-top: 300rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
