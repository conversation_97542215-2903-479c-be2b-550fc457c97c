<template>
    <view class="select_admin_box">
        <z-paging>
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" fixed statusBar @clickLeft="back" leftText="取消" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
                    <view class="top_nav_bar">
                        <view class="top_text">管理员</view>
                    </view>
                </uni-nav-bar>
                <view class="top">
                    <img src="https://alicdn.1d1j.cn/1531914651808833538/default/26c5462c5b8f4dd7a49732cda6e1003f.png" class="yd_img" />
                    <text>深圳宝安实验中学</text>
                </view>
            </template>
            <view class="main_box">
                <treeList :flagList="['check']" flag="rollValue" :rightList="['right']" rightType="rightValue" :list="treeListData" :tree-click-item="treeClick">
                    <template #default="{ item: { name, num, rollValue, img } }">
                        <view class="tree_item_box">
                            <view v-if="rollValue === 'check'" class="left_box">
                                <img class="img" v-if="img" :src="img" />
                                <view v-else class="chart_at">{{ name.charAt(0) }}</view>
                                <text class="text">{{ name }}</text>
                            </view>
                            <template v-else>
                                <text>{{ name }}</text>
                                <text class="text_right">{{ num }}个学生</text>
                            </template>
                        </view>
                    </template>
                </treeList>
            </view>
            <template #bottom>
                <view class="footer_box">
                    <button type="default" class="yd_btn_primary" @click="save">保存</button>
                </view>
            </template>
        </z-paging>
    </view>
</template>
<script setup>
import treeList from "../../components/treeList.vue"
import { setCheck, getCheck } from "../../utils/treeMethod.js"

// 树组件
const treeListData = ref(
    Array.from({ length: 1 }, (_, index) => ({
        id: 1 + "_" + index,
        name: "宿管部",
        num: 345,
        children: Array.from({ length: 3 }, (_, idx) => ({
            id: 2 + "_" + idx,
            name: `宿管${idx + 1}部`,
            num: 345,
            children: Array.from({ length: 8 }, (_, ind) => ({
                id: ind,
                name: `${ind}号`,
                num: 60 + ind,
                rollValue: "check",
                children: []
            }))
        }))
    }))
)

const treeClick = (item) => {
    if (item.rollValue === "check") {
        const params = {
            sourceArr: treeListData.value,
            obj: item,
            type: "rollValue",
            flag: "check",
            multiple: false
        }
        setCheck(params)
    }
}

const save = () => {
    console.log("保存")
}

const back = () => {
    uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.select_admin_box {
    background-color: $uni-bg-color-grey;
    :deep(.uni-navbar__content) {
        border-bottom-color: transparent !important;
    }
    .top_nav_bar {
        text-align: center;
        margin: auto;
        .top_text {
            font-size: 32rpx;
            font-weight: 600;
        }
    }
    .top {
        padding: 14rpx 28rpx;
        background-color: #fff;
        display: flex;
        align-items: center;
        color: #333;
        font-size: 32rpx;
        font-weight: 600;
        margin: 20rpx 0;
        .yd_img {
            width: 64rpx;
            height: 64rpx;
            margin-right: 20rpx;
        }
    }
    .main_box {
        background-color: #fff;
        padding-bottom: 150rpx;
        .tree_item_box {
            width: 100%;
            display: flex;
            justify-content: space-between;
            font-size: 32rpx;
            color: #333333;
            .left_box {
                display: flex;
                align-items: center;
                .img {
                    width: 60rpx;
                    height: 60rpx;
                    border-radius: 50%;
                    overflow: hidden;
                }
                .chart_at {
                    display: inline-block;
                    width: 60rpx;
                    height: 60rpx;
                    text-align: center;
                    line-height: 60rpx;
                    color: #fff;
                    font-size: 32rpx;
                    border-radius: 50%;
                    background-color: #4566d5;
                    font-weight: 600;
                }
                .text {
                    font-size: 28rpx;
                    color: #333;
                    margin-left: 24rpx;
                }
            }
            .text_right {
                font-size: 28rpx;
                color: #999999;
                margin: auto 0;
            }
        }
    }
    .footer_box {
        padding: 30rpx;
        background-color: #fff;
        .yd_btn_primary {
            background-color: #4566d5;
            color: #fff;
        }
        button {
            font-size: 32rpx;
            margin-left: 0;
            margin-right: 0;
        }
    }
}
</style>
