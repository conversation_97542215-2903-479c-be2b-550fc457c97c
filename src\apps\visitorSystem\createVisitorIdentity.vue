<!-- createVisitorIdentity -->
<!-- 新增访客身份 -->

<template>
    <view class="create-visitor-identity">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="state.createForm.id ? '编辑访客' : '新增访客'" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <uni-forms class="content-form" ref="contentFormRef" label-position="top" label-width="100vw" :modelValue="state.createForm">
            <template v-for="(item, idx) in state.visitorFormList">
                <uni-forms-item :key="idx + '_' + item.attribute" v-if="item.attribute === 'picture' && state.verifyType.includes('2')" :label="item.title" :required="item.props.required" :name="item.attribute">
                    <!-- 访客人脸照片 -->
                    <!-- 人脸验证 -->
                    <view v-if="state.verifyType.includes('2')">
                        <view class="picture_image" v-if="state.createForm.picture">
                            <cover-image style="width: 90px; height: 90px" @click="showZoomRef.open()" :src="state.createForm.picture"></cover-image>
                            <view class="picture_close" @click="state.createForm.picture = ''">X</view>
                        </view>
                        <uni-file-picker v-else class="pointer-events" limit="1" :imageStyles="state.imageStyles" @select="handerPicker" v-model:value="state.createForm.picture">
                            <view class="icon">+</view>
                            <view class="text">点击上传</view>
                        </uni-file-picker>
                    </view>
                </uni-forms-item>
                <uni-forms-item v-if="item.attribute !== 'picture'" :key="idx" :label="item.title" :required="item.props.required" :name="item.attribute">
                    <!-- 证件号 -->
                    <view v-if="item.attribute === 'idCard'">
                        <uni-easyinput type="idcard" v-model="state.createForm[item.attribute]" :inputBorder="state.inputBorder" :maxlength="state.createForm.idCardType == 1 ? 18 : 30" :placeholder="item.props.placeholder" />
                    </view>

                    <!-- 证件类型 -->
                    <view v-else-if="item.attribute === 'idCardType'" @click="popupIdRef.open('butoom')">
                        <uni-easyinput class="is_disableds" ref="idCardTypeRef" disabled v-model="state.createForm.idCardTypeName" :inputBorder="state.inputBorder" :readonly="true" :placeholder="item.props.placeholder" suffixIcon="forward" />
                    </view>
                    <view v-else>
                        <uni-easyinput @change="handerEasyinput(item)" :type="item.name === 'number' ? 'number' : ''" v-model="state.createForm[item.attribute]" :inputBorder="state.inputBorder" :maxlength="item.props.maxlength" :placeholder="item.props.placeholder" />
                    </view>
                </uni-forms-item>
            </template>
        </uni-forms>
        <view class="footer">
            <button class="button" type="primary" :loading="state.confirmLoading" @tap="visitorSubmit">提交</button>
        </view>
        <!-- 弹出插件 -->
        <view class="unit">
            <uni-popup ref="popupIdRef" type="bottom" background-color="#fff">
                <view class="interviewee hender">
                    <view class="title">证件类型</view>
                    <uni-icons class="icons" type="closeempty" @click="popupIdRef.close()"></uni-icons>
                </view>
                <radio-group @change="radioIdChange">
                    <label class="interviewee" v-for="item in state.IDCardTypeActions" :key="item.attribute">
                        <view>{{ item.name }}</view>
                        <radio :value="JSON.stringify(item)" :checked="item.id === state.createForm.idCardType" />
                    </label>
                </radio-group>
            </uni-popup>
        </view>
        <uni-popup ref="showZoomRef" posinset="center">
            <cover-image style="width: 100%; height: 100%" :src="state.createForm.picture"></cover-image>
        </uni-popup>
    </view>
</template>

<script setup>
import { checkPlatform } from "@/utils/sendAppEvent.js"
import { compressImage } from "./utils.js"

const popupIdRef = ref(null)
const showZoomRef = ref(null)
const contentFormRef = ref(null)

const state = reactive({
    route: {},
    visitorFormList: [],
    verifyType: [],
    imageStyles: {
        width: 100,
        height: 100
    },
    confirmLoading: false,
    inputBorder: false,
    IDCardTypeActions: [
        { name: "身份证", key: 1, id: 1 },
        { name: "其他", key: 2, id: 2 }
        // { name: '香港身份证', key: 'hgID' },
        // { name: '香港身份证', key: 'macaoID' },
        // { name: '港澳护照', key: 'hgmacaoID' },
    ],
    createForm: {
        id: "",
        fromUserName: "", //访客姓名
        phone: "", //手机号
        idCardType: null, //证件类型 1：身份证 2：其他
        // toUserId: route.toUserId, //受访人-教职工ID
        idCardTypeName: "",
        idCard: "", //证件号
        picture: "", //人脸照片
        personType: 1 //人员类型 1：访客 2：临时人员
    },
    fileLists: []
})

const fromRules = {
    fromUserName: {
        rules: [{ required: true, errorMessage: "访客姓名不能为空！" }]
    },
    idCardType: {
        rules: [{ required: true, errorMessage: "证件类型不能为空！" }]
    },
    picture: {
        rules: [{ required: true, errorMessage: "访客人脸照片不能为空！" }]
    },
    phone: {
        rules: [
            { required: true, errorMessage: "手机号不能为空！" },
            {
                validateFunction: (rule, value, data, callback) => {
                    // 只要13、14、15、16、17、18、19开头即可
                    const check = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/
                    if (!check.test(value)) {
                        callback("手机号格式不正确！")
                    }
                    return true
                }
            }
        ]
    },
    idCard: {
        rules: [
            { required: true, errorMessage: "证件号不能为空！" },
            {
                validateFunction: (rule, value, data, callback) => {
                    //大陆身份证 澳门、香港、港澳护照
                    let IDCard = /([1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$)|(^[a-zA-Z]\d{6}\([\dA]\))|(^[1|5|7]\d{6}\(\d\)$)|(^[EeKkGgDdSsPpHh]\d{8}$)|(^(([Ee][a-fA-F])|([DdSsPp][Ee])|([Kk][Jj])|([Mm][Aa])|(1[45]))\d{7}$)/
                    if (!IDCard.test(value)) {
                        callback("证件号格式不正确！")
                    }
                    return true
                }
            }
        ]
    }
}

const handerPicker = async (fileItem, value) => {
    const tempFile = fileItem.tempFiles[0]
    const isLt5M = tempFile.file.size / 1024 / 1024 > 5
    if (isLt5M) {
        uni.showToast({
            title: "图片大于5M，请重新上传",
            duration: 2000,
            icon: "none"
        })
        nextTick(() => {
            state.createForm.picture = ""
        })
        state.createForm.picture = ""
        return true
    } else {
        compressImage(tempFile.file, 0.9)
            .then((compressedBlob) => {
                // 在这里可以对压缩后的 Blob 进行进一步处理，比如上传等
                http.uploadFile("/file/common/upload", compressedBlob, { folderType: "visitorFace" })
                    .then((res) => {
                        state.createForm.picture = res || ""
                    })
                    .catch(() => (state.createForm.picture = ""))
            })
            .catch((error) => {
                state.createForm.picture = ""
                console.error("压缩图片失败：", error)
            })
    }
}
// 访客机验证方式设置
const getViteSettingGlobalInfo = async () => {
    const params = {}
    if (state.route.schoolId) {
        params.schoolId = state.route.schoolId
    }
    await http.get("/cloud/visitor/setting/global/get", params).then(({ data }) => {
        state.verifyType = data?.verifyType
        if (!data?.verifyType.includes("2")) {
            fromRules.picture.rules[0].required = false
        }
    })
}

// 给createForm赋值 以便校验用
const handerEasyinput = (item) => {
    const { key, value } = item
    state.createForm[key] = value
}

// 证件类型
const radioIdChange = (event) => {
    const { name, id } = JSON.parse(event.detail.value)
    state.createForm.idCardType = id
    state.createForm.idCardTypeName = name
    state.createForm.idCard = ""
    popupIdRef.value.close()
}
function clickLeft() {
    // #ifdef H5 || H5-WEIXIN
    const roleArr = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"]
    if (roleArr.includes(checkPlatform())) {
        navigateTo({ url: "/apps/visitorSystem/create", query: state.route })
    } else {
        uni.navigateBack()
    }
    // #endif
    // #ifdef MP-WEIXIN || APP-PLUS
    uni.navigateBack()
    // #endif
}

const visitorSubmit = () => {
    contentFormRef.value.validate().then(async () => {
        state.confirmLoading = true
        const url = state.createForm.id ? "/app/visitor/user/update" : "/app/visitor/user/create"
        const { manageUserId, schoolId } = state.route
        const params = {
            ...state.createForm,
            manageUserId,
            schoolId
        }
        params.name = params.fromUserName
        http.post(url, params)
            .then(() => {
                navigateTo({ url: "/apps/visitorSystem/create", query: state.route })
            })
            .finally(() => {
                state.confirmLoading = false
            })
    })
}
onReady(async () => {
    await getViteSettingGlobalInfo()
    // 需要在onReady中设置规则
    contentFormRef.value?.setRules(fromRules)
})

onLoad((item) => {
    state.route = item
    // 停止当前页面下拉刷新
    uni.stopPullDownRefresh()
    uni.setNavigationBarTitle({
        title: state.createForm.id ? "编辑访客" : "新增访客" // 新标题内容
    })
    const { visitorFormList, visitorObj } = item
    if (visitorFormList) {
        state.visitorFormList = JSON.parse(visitorFormList)
    }
    if (visitorObj) {
        state.createForm = JSON.parse(visitorObj)
        state.createForm.fromUserName = state.createForm.name
        state.createForm.picture = state.createForm.picture
        state.createForm.idCardTypeName = ["", "身份证", "其他"][1 || state.createForm.idCardType]
    }
})
</script>

<style scoped lang="scss">
.create-visitor-identity {
    height: 100vh;

    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: $uni-text-color;
    }

    .content-form {
        background: $uni-bg-color;
        margin: 20rpx 0;
        padding: 20rpx;

        .pointer-events {
            :deep(.file-picker__box-content) {
                background-color: $uni-bg-color-grey;
                display: block;
                text-align: center;

                .icon {
                    color: $uni-text-color;
                    margin-top: 40rpx;
                    font-size: 40rpx;
                }

                .text {
                    color: $uni-text-color-grey;
                }
            }
        }
    }

    :deep(.is-input-border) {
        border: none;
    }

    .is_disableds {
        :deep(.is-disabled) {
            background-color: $uni-bg-color !important;
            color: $uni-text-color;
        }
    }

    .title {
        flex: 1;
        text-align: center;
        font-size: 32rpx;
    }

    .interviewee {
        display: flex;
        justify-content: space-between;
        padding: 20rpx 30rpx;
    }

    .footer {
        padding: 30rpx;
        background: $uni-bg-color;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
    }

    uni-button[type="primary"] {
        background-color: var(--primary-color);
    }

    .picture_image {
        position: relative;
        width: 180rpx;
        height: 200rpx;

        .picture_close {
            position: absolute;
            right: -10rpx;
            top: -10rpx;
            width: 40rpx;
            height: 40rpx;
            line-height: 40rpx;
            background-color: $uni-text-color;
            border-radius: 50%;
            color: $uni-bg-color;
            text-align: center;
            font-size: 24rpx;
        }
    }
}
</style>
