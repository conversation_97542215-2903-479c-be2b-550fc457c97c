<template>
    <view class="notice_container">
        <z-paging>
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" left-icon="left" fixed statusBar :border="false" :title="state.title" @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
            </template>
            <view class="main">
                <view class="top">{{ state.identityUserName }} 发布于 {{ state.timerDate }}</view>
                <view class="msg" v-if="state.content && state.contentType == 0">
                    <rich-text :nodes="state.content"></rich-text>
                </view>
                <view class="img" v-else-if="state.contentImg && state.contentType == 1">
                    <image class="cover_img" :src="state.contentImg" mode="widthFix" />
                </view>
                <yd-empty v-else text="暂无内容" :isMargin="true" />
            </view>
        </z-paging>
    </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'

const state = ref({})

const back = () => {
    uni.navigateBack()
}

onLoad(async(options) => {
	 const {
        data
    } = await http.post("/app/mobile/mess/receive/info", {id: options.id })
    if (!data) return
    state.value = data
})
</script>

<style lang="scss" scoped>
.notice_container {
    background: #fafafafa;
    .main {
        padding: 30rpx;

        .top {
            font-size: 24rpx;
            color: #b5b5b5;
        }
        .msg {
            word-wrap: break-word;
            background: var(--primary-bg-color);
            padding: 20rpx;
            border-radius: 8rpx;
            margin-top: 30rpx;
            font-size: 28rpx;
            max-width: 100vw;
            // #ifdef H5 || H5-WEIXIN
            :deep(img) {
                max-width: 100%;
                height: auto;
            }
            // #endif
            // #ifdef MP-WEIXIN
            :deep(image) {
                max-width: 100%;
                height: auto;
            }
            // #endif
        }
        .img {
            margin-top: 20rpx;
            width: 100%;
            .cover_img {
                width: 100%;
            }
        }
    }
}
</style>
