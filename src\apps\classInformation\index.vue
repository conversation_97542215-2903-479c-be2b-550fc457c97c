<template>
    <div class="classes_page">
        <uni-nav-bar :showMenuButtonWidth="true" :border="false" left-icon="left" @clickLeft="routerBack" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" title="班级信息"> </uni-nav-bar>
        <view class="classes_info">
            <image :src="classesInfo.classesIcon || '@nginx/components/class.png'" class="class_icon" mode="scaleToFill" />
            <view class="info">
                <text class="classes_name">{{ classesInfo.classesName }}</text>
                <view class="num_teacher">
                    <text
                        >全班人数：<text class="num">{{ classesInfo.studentNums || 0 }}</text>
                    </text>
                    <text class="line">|</text>
                    <text
                        >班主任：<text class="num">{{ classesInfo.master || "-" }}</text>
                    </text>
                </view>
            </view>
        </view>
        <view class="teaching_faculty"> </view>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const classesId = ref("")
const classesInfo = ref({})
function getClassesDetail() {
    http.get("/app/master/class/getClassesDetail", { classesId: classesId.value }).then((res) => {
        classesInfo.value = res.data
        console.log(classesInfo.value)
    })
}

onLoad((options) => {
    classesId.value = options.classesId
    getClassesDetail()
})
</script>

<style lang="scss" scoped>
.classes_page {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .classes_info {
        background: #fff;
        padding: 40rpx 28rpx;
        display: flex;
        .class_icon {
            width: 120rpx;
            min-width: 120rpx;
            height: 120rpx;
            margin-right: 28rpx;
            border-radius: 50%;
        }
        .info {
            display: flex;
            flex-direction: column;
            flex: 1;
            justify-content: space-evenly;
            .classes_name {
                font-weight: 600;
                font-size: 32rpx;
                color: #333333;
                line-height: 44rpx;
            }
            .num_teacher {
                font-weight: 400;
                font-size: 28rpx;
                color: #999999;
                line-height: 40rpx;
                .line {
                    padding: 0rpx 20rpx;
                }
                .num {
                    color: #333333;
                }
            }
        }
    }
    .teaching_faculty {
        margin-top: 20rpx;
        padding: 28rpx;
    }
}
</style>
