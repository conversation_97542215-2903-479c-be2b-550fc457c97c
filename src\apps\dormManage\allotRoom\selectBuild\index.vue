<template>
    <view class="allot_room_container">
        <view class="top_box">
            <uv-steps :current="current">
                <uv-steps-item :title="allotInfo.one"></uv-steps-item>
                <uv-steps-item :title="allotInfo.two"></uv-steps-item>
                <uv-steps-item :title="allotInfo.three"></uv-steps-item>
            </uv-steps>
        </view>
        <!-- 内容信息 -->
        <view class="main_box">
            <view class="title">请选择您要分配的楼栋 </view>
            <radioSelect :list="floorList">
                <template #left="{ left: { name, totalRoomCount, checkInPeopleCount, residueBedCount } }">
                    <view class="left_box">
                        <view class="sel_top_box">
                            <img class="img" src="https://alicdn.1d1j.cn/1531914651808833538/default/0535d32d7f484e14b2a7c7235d0f2e51.png" />
                            <view>
                                <view>{{ name }}</view>
                                <view class="text_btm">合计寝室：{{ totalRoomCount }}间</view>
                            </view>
                        </view>
                        <view class="sel_btm_box">
                            <text class="text">已住：{{ checkInPeopleCount }}人</text>
                            <text class="text">剩余床位：{{ residueBedCount }}个</text>
                        </view>
                    </view>
                </template>
            </radioSelect>
        </view>
        <!-- 底部按钮 -->
        <div class="footer_box">
            <view class="btn_group">
                <button class="yd_plain" @click="previous">上一步</button>
                <button class="yd_btn_primary" @click="next">确认分配该楼栋</button>
            </view>
        </div>

        <TipPopup ref="tipDomePopup" title="您选择的楼栋" confirmBtnText="确认选择" @closePopup="tipClose" @confirm="tipConfirm">
            <template #tipText>
                <div v-for="(item, index) in tipMsg" :key="index">{{ item }}</div>
            </template>
        </TipPopup>
    </view>
</template>
<script setup>
import radioSelect from "./components/radioSelect.vue"
import TipPopup from "../../components/tipPopup.vue"

const floorList = ref([])
let tipMsg = ref([])
let allotInfo = reactive({})

onLoad((params) => {
    Object.keys(params).forEach((key) => {
        params[key] = decodeURIComponent(params[key])
    })
    Object.assign(allotInfo, params)
    allotInfo.classesIdList = JSON.parse(allotInfo.classesIdList)
    allotInfo.studentIdList = JSON.parse(allotInfo.studentIdList)
    getBuild()
})

async function getBuild() {
    const { data } = await http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryBuilding", {
        gender: allotInfo.studentGender
    })
    floorList.value = data
}

let current = ref(2)

const previous = () => {
    uni.navigateBack()
}

const next = () => {
    const arr = floorList.value.filter((i) => i.checked)
    if (!arr.length) return
    allotInfo.buildingIdList = arr.map((i) => i.id)
    tipMsg.value = arr.map((i) => `${i.name},共${i.atLeastBedCount}个床位`)
    openPopup()
}

const tipDomePopup = ref(null)
function openPopup() {
    tipDomePopup.value.open()
}

function tipClose() {
    tipDomePopup.value.closeDialog()
}

async function tipConfirm() {
    const params = {
        buildingIdList: allotInfo.buildingIdList,
        classesIdList: allotInfo.classesIdList,
        studentIdList: allotInfo.studentIdList,
        gender: allotInfo.studentGender
    }
    await http.post("/app/dormitory/intelligentDormitorySeparation/separationByBuilding", params)
    uni.showToast({
        title: "分寝成功",
        icon: "none"
    })
    tipClose()
    setTimeout(() => {
        uni.reLaunch({
            url: "/apps/dormManage/dormInfo/index"
        })
    }, 1000)
}
</script>
<style lang="scss" scoped>
.allot_room_container {
    background-color: $uni-bg-color-grey;
    .top_box {
        padding: 40rpx 0;
        :deep(.uv-steps-item__wrapper) {
            background-color: $uni-bg-color-grey;
        }
    }
    .main_box {
        background-color: #fff;
        border-radius: 40rpx 40rpx 0 0;
        padding: 40rpx 0rpx 150rpx 0rpx;
        .title {
            text-align: center;
            font-size: 32rpx;
            color: #333;
            font-weight: 600;
        }
        .left_box {
            flex: 1;
			/* #ifdef MP-WEIXIN */
            width: calc(100vw - 180rpx);
            /* #endif */

            .sel_top_box {
                display: flex;
                align-items: center;
				width: 100%;
                .img {
                    flex-shrink: 0;
                    width: 100rpx;
                    height: 100rpx;
                    border-radius: 50%;
                    overflow: hidden;
                    border: 10rpx solid #cad6ff;
                    margin-right: 28rpx;
                }
                .text_btm {
                    font-size: 28rpx;
                    font-weight: normal;
                    padding-top: 16rpx;
                }
            }
            .sel_btm_box {
				width: 100%;
                display: flex;
                font-size: 28rpx;
                font-weight: normal;
                padding-top: 40rpx;
                .text {
                    width: 50%;
                    text-align: center;
                    &:first-of-type {
                        border-right: 1rpx solid #d9d9d9;
                    }
                }
            }
        }
    }

    .footer_box {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        bottom: 0rpx;
        padding: 30rpx;
        background-color: #fff;

        .btn_group {
            display: flex;
            justify-content: space-between;
            .yd_plain {
                background-color: #fff;
                color: #4566d5;
                border: 1rpx solid #4566d5;
            }
            .yd_btn_primary {
                background-color: #4566d5;
                color: #fff;
            }
            button {
                width: 330rpx;
                font-size: 32rpx;
                margin-left: 0;
                margin-right: 0;
            }
        }
    }
}
</style>
