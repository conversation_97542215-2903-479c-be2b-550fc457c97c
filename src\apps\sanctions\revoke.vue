<template>
    <view class="punishment">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="back" title="撤销处分" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="content">
            <view class="list-itme">
                <uni-forms class="list-itme-hande" ref="valiForm" :rules="rules" :modelValue="state.formData">
                    <uni-forms-item required label="撤销说明" :label-width="100" label-position="top" name="revokeRemarks">
                        <uni-easyinput type="textarea" v-model="state.formData.revokeRemarks" placeholder="请输入撤销说明" />
                    </uni-forms-item>
                </uni-forms>
            </view>
            <button type="primary" class="btn" @click="submit">提交</button>
        </view>
    </view>
</template>

<script setup>
const valiForm = ref(null)
const state = reactive({
    formData: {
        id: "",
        revokeRemarks: ""
    }
})
const rules = {
    revokeRemarks: {
        rules: [
            {
                required: true,
                errorMessage: "请输入撤销说明"
            }
        ]
    }
}

const back = () => {
    uni.navigateBack({
        delta: 1
    })
}
const submit = () => {
    valiForm.value
        .validate()
        .then((res) => {
            http.post("/app/disciplinary/action/revoke", state.formData).then((res) => {
                uni.showToast({
                    title: res.message
                })
                back()
            })
        })
        .catch((err) => {
            console.log("err", err)
        })
}
onLoad((item) => {
    state.formData.id = item.id
})
</script>

<style lang="scss" scoped>
.punishment {
    background: #f9faf9;
    height: 100vh;
    .content {
        background: #fff;
        margin: 20rpx 30rpx;
        border-radius: 20rpx;
        height: calc(100vh - 150rpx);
        position: relative;
        .list-itme {
            margin: 20rpx 30rpx;
            padding-bottom: 32rpx;
            height: calc(100vh - 304rpx);
            .list-itme-hande {
                align-items: center;
                font-weight: 600;
                font-size: 14rpx;
                margin-bottom: 20rpx;
                :deep(.uni-forms-item) {
                    display: block;
                    .uni-forms-item__content {
                        margin-top: 14rpx;
                    }
                }
            }
        }
        .btn {
            margin: 0 30rpx;
            position: absolute;
            bottom: 86rpx;
            left: 0rpx;
            width: calc(100% - 65rpx);
            background-color: var(--primary-color);
        }
    }
}
</style>
