<template>
    <view class="notification-scope">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="state.propsForm.title" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" />
        <uv-tabs
            :list="tabsList"
            :current="state.activeTab"
            @click="clickTabs"
            :activeStyle="{ color: 'var(--primary-color)' }"
            :inactiveStyle="{ color: '#606266' }"
            lineWidth="20"
            :customStyle="{ background: '#fff' }"
            :itemStyle="{
                height: '50rpx',
                flex: 1,
                paddingBottom: '20rpx'
            }"
            lineColor="var(--primary-color)"
        ></uv-tabs>
        <uni-list class="reset-list">
            <uni-list-item v-for="item in state.scopeList" :key="item.id" :title="item.name" showArrow :rightText="`共 ${item.nums} 人`" link @click="deptPeople(item.id)" />
            <yd-empty class="yd-empty" v-if="state.scopeList.length == 0" text="暂无数据" />
        </uni-list>
    </view>
</template>

<script setup>
import { reactive, ref } from "vue"
const tabsList = [
    {
        name: "教职工",
        key: 1
    },
    {
        name: "家长",
        key: 2
    }
]

const state = reactive({
    scopeList: [],
    activeTab: 0,
    propsForm: {
        id: "",
        identity: 1,
        title: "通知范围"
    }
})

const clickLeft = () => {
    uni.navigateBack()
}
const deptPeople = (deptId) => {
    const deptObj = { deptId }
    const classesObj = { classesId: deptId }
    const obj = state.propsForm.identity == 1 ? deptObj : classesObj
    navigateTo({
        url: "/apps/notice/components/notificationScopePeople",
        query: {
            id: state.propsForm.id,
            ...obj
        }
    })
}
const clickTabs = (item) => {
    state.propsForm.identity = item.key
    getNotifyInfo()
}

const getNotifyInfo = () => {
    http.post("/cloud/mobile/mess/publish/notifyInfo", state.propsForm).then(({ data }) => {
        state.scopeList = data
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.propsForm.id = options.id
    state.propsForm.title = options.title || "通知范围"
    getNotifyInfo()
})
</script>

<style lang="scss" scoped>
.notification-scope {
    height: 100vh;
    background: $uni-bg-color-grey;

    .reset-list {
        margin: 10rpx 0;
        overflow: hidden auto;
        height: calc(100vh - 164rpx);
    }
}

.yd-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* #ifdef MP-WEIXIN */
    transform: translate(-50%, 100%);
    /* #endif */
}
</style>
