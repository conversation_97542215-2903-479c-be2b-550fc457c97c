<template>
    <div class="page">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="预警消息" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <div class="info">
            <div class="item">事件ID：{{ info.eventId }}</div>
            <div class="item">事件类型：{{ eventTypeText[info.eventType] }}</div>
            <div class="item">事件源类型：{{ info.srcType }}</div>
            <div class="item">事件源编号：{{ info.srcIndex }}</div>
            <div class="item">事件源名称：{{ info.srcName }}</div>
            <div class="item">事件发生时间：{{ info.happenTime }}</div>
            <div class="item other">
                <div class="title">事件其他扩展事件：</div>
                <div class="text" v-if="eventData.dataType">数据模型标识：{{ eventData.dataType }}</div>
                <div class="text" v-if="eventData.recvTime">数据接收时间：{{ dataTime(eventData.recvTime) }}</div>
                <div class="text" v-if="eventData.sendTime">数据发送时间：{{ dataTime(eventData.sendTime) }}</div>
                <div class="text" v-if="eventData.dateTime">数据触发时间：{{ dataTime(eventData.dateTime) }}</div>
                <div class="text" v-if="eventData.ipAddress">设备的IP地址：{{ eventData.ipAddress }}</div>
                <div class="text" v-if="eventData.portNo">设备端口号：{{ eventData.portNo }}</div>
                <div class="text" v-if="eventData.channelID">设备通道号：{{ eventData.channelID }}</div>
                <div class="text" v-if="eventData.eventDescription">事件类型名称：{{ eventData.eventDescription }}</div>
                <div class="text" v-if="eventData.eventType">事件类型：{{ eventData.eventType }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
import dayjs from "dayjs"
const dataTime = (value) => {
    const time = dayjs(value).format("YYYY-MM-DD HH:mm:ss")
    return time
}

const info = ref({})
const eventData = ref({})
const eventTypeText = {
    192515: "火点检测",
    192517: "温度报警",
    131605: "倒地",
    131596: "剧烈运动",
    131664: "人数异常",
    131593: "人员聚集",
    131590: "徘徊侦测",
    131586: "进入区域",
    131587: "离开区域",
    131585: "越界侦测",
    327937: "子系统布防",
    327938: "撤防",
    327941: "消警",
    327939: "防区旁路",
    327940: "旁路恢复",
    327681: "报警",
    327687: "紧急报警",
    131588: "区域入侵",
    131702: "人员超限"
}

const clickLeft = () => {
    uni.navigateBack()
}

function getInfo(id) {
    http.get("/app/hk/message/get", { id }).then((res) => {
        info.value = res.data
        eventData.value = JSON.parse(res.data.eventData)
    })
}

onLoad((options) => {
    getInfo(options.id)
})
</script>

<style lang="scss" scoped>
.page {
    background: $uni-bg-color-grey;
}
.info {
    padding: 36rpx;
    background: #fff;
    .item {
        font-weight: 400;
        word-break: break-all;
        overflow-wrap: break-word;
        font-size: 28rpx;
        color: #333333;
        margin: 30rpx 0rpx;
        line-height: 40rpx;
        .text {
            margin: 20rpx 0rpx;
        }
    }
    .other {
        padding: 26rpx;
        min-height: 298rpx;
        background: var(--primary-bg-color);
        .title {
            font-weight: 600;
            font-size: 30rpx;
            color: #333333;
        }
    }
    .fallDown {
        background: #fff;
        padding: 20rpx;
        margin: 10rpx 0rpx;
        .position {
            background: #f4f4f4;
            padding: 10rpx 20rpx;
            margin: 10rpx 0rpx;
        }
    }
}
</style>
