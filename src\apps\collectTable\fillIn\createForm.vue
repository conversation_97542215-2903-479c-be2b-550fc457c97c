<template>
    <view class="create_form">
        <z-paging ref="paging" v-model="state.dataList" @query="initPage" :auto="false" :refresher-enabled="false">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="填写" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
            </template>
            <view class="content">
                <uni-list class="list-item">
                    <uni-list-item class="h1" :title="state.createForm.title" />
                    <uni-list-item :title="state.createForm.description" />
                </uni-list>
                <view class="form">
                    <uni-forms ref="baseForm" :modelValue="state.createForm">
                        <view class="form-item" v-for="(item, idx) in state.createForm.components" :key="item.id">
                            <uni-forms-item
                                :required="!state.isEdit && item.props.required"
                                :name="['components', idx, 'value']"
                                :rules="[
                                    {
                                        required: isVerify(item),
                                        errorMessage: item.title + '不能为空!'
                                    }
                                ]"
                            >
                                <view class="label">
                                    <text class="order_color">{{ orderNumber(idx) }}.</text>
                                    <text>{{ item.title }}</text>
                                </view>

                                <uni-list-item v-if="item.name === 'TextInput' && _keyboard(item)" clickable @click="handleropen(item, idx)" :title="item.value || '请输入'" :disabled="state.isEdit" />

                                <uni-easyinput v-else-if="item.name === 'TextInput'" v-model="item.value" :disabled="state.isEdit" :inputBorder="false" :maxlength="item.props.maxlength" placeholder="请输入" />

                                <radio-group v-else-if="item.name === 'SelectInput'" @change="(evt) => (item.value = evt.detail.value)">
                                    <label class="uni-list-cell" v-for="(it, index) in item.props.options" :key="index">
                                        <view style="margin: 10px 0">
                                            <radio color="#00b781" :value="it" :checked="it === item.value" :disabled="state.isEdit">{{ it }}</radio>
                                        </view>
                                    </label>
                                </radio-group>

                                <checkbox-group v-else-if="item.name === 'MultipleSelect'" @change="handlerMultipleSelect($event, item)">
                                    <label class="uni-list-cell" v-for="(it, index) in item.props.options" :key="index">
                                        <view style="margin: 10px 0">
                                            <checkbox :value="it" :checked="_checked(item, it)" color="#00b781" :disabled="state.isEdit">{{ it }}</checkbox>
                                        </view>
                                    </label>
                                </checkbox-group>

                                <uni-list-item v-else-if="item.name === 'ClassesPicker'" showArrow :title="_value(item.value)" :disabled="state.isEdit" />

                                <uni-list-item v-else-if="item.name === 'DeptPicker'" showArrow :title="_value(item.value)" :disabled="state.isEdit" />

                                <view v-else-if="item.name === 'DateTime'" class="date_time">
                                    <uni-datetime-picker type="datetime" :disabled="state.isEdit" :border="false" v-model="item.value" @change="(evt) => (item.value = evt.detail.value)" />
                                </view>

                                <view v-else-if="item.name === 'FileUpload'" class="image-preview">
                                    <!-- #ifndef MP-WEIXIN -->
                                    <uni-file-picker v-if="item.props.imageOnly" v-model="item._value" :limit="10" :sizeType="['compressed']" :del-icon="!state.isEdit" :disabled="state.isEdit" :imageStyles="{ width: 100, height: 100 }" @select="fileSelect($event, item)" @delete="fileDelete($event, item)"></uni-file-picker>

                                    <uni-file-picker v-else :file-mediatype="'all'" ref="files" :del-icon="!state.isEdit" :auto-upload="false" :limit="10" :disabled="state.isEdit" v-model="item._value" @select="fileSelect($event, item)" @delete="fileDelete($event, item)">
                                        <button>选择文件</button>
                                    </uni-file-picker>
                                    <!-- #endif -->

                                    <!-- #ifdef MP-WEIXIN -->
                                    <uni-file-picker v-if="item.props.imageOnly" v-model="item._value" :limit="10" :sizeType="['compressed']" :del-icon="!state.isEdit" :disabled="state.isEdit" :imageStyles="{ width: 100, height: 100 }" @select="fileSelect($event, item)" @delete="fileDelete($event, item)"></uni-file-picker>

                                    <view v-else>
                                        <view @click="chooseFileForMP(item)">
                                            <button>选择文件</button>
                                        </view>
                                        <view class="file_list" v-for="(ic, _idx) in item._value">
                                            <text>{{ ic.name }}</text>
                                            <view style="margin-left: 10px" v-if="!state.isEdit" @click="fileDelete({ index: _idx }, item)">
                                                <uni-icons type="closeempty" size="18"></uni-icons>
                                            </view>
                                        </view>
                                    </view>
                                    <!-- #endif -->
                                </view>

                                <view class="sign-warp" v-else-if="item.name === 'SignName'">
                                    <yd-sign-input :hideDelete="state.isEdit" @deleteImg="() => handlerSignReset(item)" :src="item._value" ref="sign" bgColor="#ffffff" canvasId="twoDrowCanvas" canvasIds="twoRotateCanvas" :formData="state.formData" :header="state.header" :action="state.action" @signToUrl="(e) => signToUrl(e, item)"> </yd-sign-input>
                                </view>

                                <template v-else-if="item.name === 'Location'">
                                    <uni-list-item v-if="item.props.autoLocate" showArrow :title="item.value || '获取位置'" clickable @click="autoLocation(idx)" />
                                    <template v-else>
                                        <uni-data-picker v-if="state.isEdit" :readonly="state.isEdit" :map="{ text: 'name', value: 'id' }" type="cascader" :localdata="state.dataTree" v-model="item._value" @change="onchange($event, item)" placeholder="请选择省/市/区"> </uni-data-picker>
                                        <uni-data-picker v-else-if="item.name === 'Location'" :map="{ text: 'name', value: 'id' }" type="cascader" :localdata="state.dataTree" v-model="item._value" @change="onchange($event, item)" placeholder="请选择省/市/区"> </uni-data-picker>
                                    </template>
                                </template>
                            </uni-forms-item>
                        </view>
                    </uni-forms>
                </view>

                <view class="button-group">
                    <template v-if="state.isEdit">
                        <button plain="true" type="primary" style="border-color: #00b781; color: #00b781" @click="updataSubmit">修改</button>
                        <button plain="true" type="primary" style="border: none; color: #00b781" @click="viewCollect">查看此收集统计></button>
                    </template>
                    <button v-else type="primary" style="background-color: #00b781; color: #fff" @click="submit">提交</button>
                </view>
                <!--  地图 -->
                <uni-popup ref="inputDialog" type="dialog">
                    <uni-popup-dialog type="msgType" cancelText="拒绝" confirmText="允许" title="提示" content="允许获取你的地理位置？" @confirm="dialogConfirm"></uni-popup-dialog>
                </uni-popup>
                <Map v-if="state.isContainer" :paramsAddress="state.address" :isAutoLocate="state.createForm.components[state.keyboardIdx].props.autoLocate" @emitSaveMap="handerSaveMap" />
                <!--  地图 -->

                <!-- 键盘 -->
                <uv-keyboard ref="keyboard" :mode="state.keyboardMode" :safeAreaInsetBottom="true" @backspace="keyboardBackspace" @change="keyboardChange" @confirm="keyboardConfirm" @cancel="keyboardCancel"></uv-keyboard>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
// @ts-ignore

// import signInput from "@/components/am-sign-input/index.vue"
import { getToken } from "@/utils/storageToken.js"

import { verifyPhone, verifyIdCard } from "../toolsValidate.ts"
import { computed, nextTick, ref } from "vue"

import Map from "../map.vue"
const paging = ref(null)
const inputDialog = ref(null)
const sign = ref(null)

const state = reactive({
    createForm: {
        title: "",
        description: "",
        components: []
    },
    formId: "",
    isEdit: false,
    // 签名
    showTip: true,
    isAmplify: false,
    esign: {
        img: "",
        lineWidth: 2,
        isCrop: false,
        lineColor: "#000000",
        vueEsignBgColor: "#F9FAF9FF"
    },
    createBtn: false,
    esignWidth: "90%",
    keyboardMode: "number",
    keyboardIdx: 0,
    keyboardOldValue: "",
    dataTree: [],
    // 地图配置
    isContainer: false,
    address: {
        chain: "",
        address: "",
        geo: "",
        lat: ""
    },
    signatureData: "",
    childrenId: "",
    action: import.meta.env.VITE_BASE_API + "/file/common/upload",
    header: {
        Authorization: "Bearer " + getToken()
    },
    formData: {
        folderType: "userSignature"
    }
})

// 签名回调
function signToUrl(e, item) {
    if (e.error_code && e.error_code === "201") {
        uni.showToast({
            title: e.msg,
            icon: "none"
        })
        return
    }

    const data = JSON.parse(e.data)
    if (data.code == 0) {
        const url = data.data[0]?.url
        // sign.value.showImg = url
        item._value = url
        item.value = JSON.stringify({ name: "file.png", status: 1, url })
    }
}

const _checked = computed(() => {
    return (item, it) => {
        if (item.value?.length) {
            return item.value.includes(it)
        }
        return false
    }
})
// 地图
const autoLocation = (idx) => {
    state.keyboardIdx = idx
    inputDialog.value.open()
}
const dialogConfirm = () => {
    state.isContainer = true
}
const handerSaveMap = (item) => {
    state.isContainer = false
    if (!item.chain) {
        return
    }
    if (state.createForm.components[state.keyboardIdx].props.autoLocate) {
        state.cityProper = item.chain + item.address
        state.createForm.components[state.keyboardIdx].value = JSON.stringify(item)
    } else {
        if (!state.cityProper) {
            state.cityProper = item.chain
        }
        state.createForm.components[state.keyboardIdx].value = JSON.stringify(item)
    }
}
// 地图end
const vueEsignRef = ref(null)
const vueEsignRefs = ref(null)

const _value = computed(() => {
    return (val) => {
        const _val = JSON.parse(val)
        return _val.map((item) => item.name).join(",")
    }
})
const _keyboard = computed(() => {
    return (val) => {
        return ["phone", "idCard", "age"].includes(val.limitCondition)
    }
})

const keyboard = ref(null)
const baseForm = ref(null)

const handlerSignReset = (item) => {
    item._value = ""
    item.value = ""
}
const handleropen = (item, idx) => {
    debugger
    if (state.isEdit) return
    // 键盘模式
    if (["phone", "age"].includes(item.limitCondition)) {
        state.keyboardMode = "number"
    } else if (item.limitCondition === "idCard") {
        // 身份证
        state.keyboardMode = "card"
    }
    // 索引
    state.keyboardIdx = idx
    // 旧值
    state.keyboardOldValue = item.value
    // state.keyboardOldValue = item[`${item.limitCondition}_value`]
    keyboard.value.open()
}
// 键盘输入
const keyboardChange = (e) => {
    let item = state.createForm.components[state.keyboardIdx]
    if (item.value == null) {
        item.value = e + ""
    } else {
        item.value += e + ""
    }
}
//键盘中的 删除
const keyboardBackspace = (e) => {
    const len = state.createForm.components[state.keyboardIdx].value
    if (len.length == 0) return
    state.createForm.components[state.keyboardIdx].value = len.substring(0, len.length - 1)
}
// 确定键盘输入
const keyboardConfirm = (e) => {
    keyboard.value.close()
}
const keyboardCancel = () => {
    state.createForm.components[state.keyboardIdx].value = state.keyboardOldValue
    keyboard.value.close()
}

// 查看统计
const viewCollect = () => {
    navigateTo({
        url: "/apps/collectTable/index",
        query: {
            activeTab: "statistics",
            childrenId: state.childrenId
        }
    })
}
const updataSubmit = () => {
    state.isEdit = false
}
// 索引的样式
const orderNumber = (order) => {
    let num = order + 1
    return `${num > 9 ? num : "0" + num}`
}

const isVerify = (item) => {
    if (item.props.required) {
        if (item.limitCondition === "age") {
            if (item.value !== null && item.value !== "") {
                return false
            }
            return item.props.required
        }
        if (item.limitCondition === "phone") {
            if (item.value !== null && item.value !== "") {
                if (verifyPhone(item.value)) {
                    return false
                }
            }
            return item.props.required
        }
        if (item.limitCondition === "idCard") {
            if (item.value !== null && item.value !== "") {
                if (verifyIdCard(item.value)) {
                    return false
                }
                return true
            }
        }
        if (item.value === null || item.value === "") {
            return item.props.required
        }
        return false
    }
    return item.props.required
}
const handlerMultipleSelect = async (evt, item) => {
    item.value = JSON.stringify(evt.detail.value)
    item._value = evt.detail.value
}

async function initPage() {
    const parasm = {
        id: state.formId,
        studentId: state.childrenId
    }
    await http
        .get("/app/collectTableWrite/formDetail", parasm)
        .then(({ data }) => {
            data.components?.forEach((item, index) => {
                if (item.name == "FileUpload") {
                    item._value = item.value ? JSON.parse(item.value) : []
                }
                // 位置
                else if (item.name == "Location") {
                    item._value = item.value ? JSON.parse(item.value).address - 0 : 0
                }
                // 签名
                else if (item.name == "SignName") {
                    item._value = item.value ? JSON.parse(item.value).url : ""

                    // sign.value.showImg = item._value

                    // setTimeout(()=>{
                    //     console.log(sign.value,item._value)

                    //     sign.value[].
                    // })
                } else if (item.name == "MultipleSelect") {
                    item._value = item.value ? JSON.parse(item.value) : []
                } else if (item.name == "FileUpload") {
                    item._value = item.value ? JSON.parse(item.value) : []
                }
            })
            state.createForm = data
            state.createForm.id = state.formId
            state.createForm.value = null
        })
        .finally(() => {})
}

// 上传文件
const fileDelete = (evt, item) => {
    // 删除文件
    item._value.splice(evt.index, 1)
    if (item._value.length) {
        item.value = JSON.stringify(item._value)
    } else {
        item.value = ""
    }
}

const fileSelect = ({ tempFiles }, item) => {
    try {
        tempFiles.forEach((v) => {
            const { path, name, uuid, size } = v
            if (size / 1024 / 1024 > 20) {
                uni.showToast({
                    title: `文件 ${name} 超过20M，上传失败`,
                    icon: "none"
                })
                return
            } else {
                http.uploadFile("/file/common/upload", path, { folderType: "app" }).then((url) => {
                    const params = {
                        url,
                        name,
                        uuid: uuid + size
                    }
                    if (!params.name) {
                        // 从path中获取文件名 去掉后缀
                        const path = params.url
                        const _name = path.split("/").pop().split(".")[0]
                        params.name = _name
                        // 如果uuid不存在,从path中获取文件名
                        params.uuid = _name + size
                    }
                    item._value.push(params)
                    item.value = JSON.stringify(item._value)
                })
            }
        })
    } catch (err) {
        console.log(err)
    }
}

// 小程序文件选择方法
const chooseFileForMP = (item) => {
    if (state.isEdit) return
    // 使用uni.chooseMessageFile兼容小程序 从聊天记录中选择文件。
    let itemList = ["从相册选择图片", "从聊天记录选择文件"]
    if (item.props.imageOnly) {
        itemList = ["从相册选择图片"]
    }
    uni.showActionSheet({
        itemList,
        success: (res) => {
            if (res.tapIndex === 0) {
                // 选择图片
                uni.chooseImage({
                    count: 10 - item._value.length,
                    success: (res) => {
                        fileSelect({ tempFiles: res.tempFiles }, item)
                    }
                })
            } else if (res.tapIndex === 1) {
                // 从聊天记录选择
                uni.chooseMessageFile({
                    count: 10 - item._value.length,
                    success: (res) => {
                        fileSelect({ tempFiles: res.tempFiles }, item)
                    }
                })
            }
        }
    })
}

// 级联
const onchange = (evt, item) => {
    let chain = evt.detail.value.map((v) => {
        return v.text
    })
    if (chain.length) {
        chain = chain.join(",")
        item.value = JSON.stringify({ chain, address: item._value })
    }
}
const areaMap = (data) => {
    data.forEach((item) => {
        if (item.area?.length) {
            areaMap(item.area)
        }
        item.children = item.area
    })
    return data
}
const localdataDataTree = async () => {
    await http.get("/app/area/list").then(({ data }) => {
        state.dataTree = areaMap(data)
    })
}

// 提交
const submit = () => {
    const isShow = state.createForm.components.some((item) => {
        if (isVerify(item)) {
            uni.showToast({
                title: `${item.title}格式不正确!`,
                icon: "none"
            })
            return true
        }
        return false
    })

    baseForm.value
        .validate()
        .then((res) => {
            if (!isShow) {
                const params = {
                    studentId: state.childrenId,
                    ...state.createForm
                }
                http.post("/app/collectTableWrite/write", params).then(({ message }) => {
                    uni.showToast({
                        title: message
                    })
                    navigateTo({
                        url: "/apps/collectTable/submitted",
                        query: {
                            id: state.formId,
                            childrenId: state.childrenId
                        }
                    })
                })
            }
        })
        .catch((err) => {
            console.log("err", err)
        })
}
const clickLeft = () => {
    uni.navigateBack()
}
onLoad((item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.formId = item.id
    state.isEdit = item.isEdit == "true" || false
    state.childrenId = item.childrenId || ""
    localdataDataTree()
    nextTick(() => {
        paging.value?.reload()
    })
    // initPage()
})
</script>
<style scoped lang="scss">
@import "../style.scss";

.create_form {
    background-color: #f6f6f6;
    height: 100vh;

    .content {
        min-height: 200px;
        .list-item {
            margin: 10rpx 0;

            .h1 {
                :deep(.uni-list-item__content-title) {
                    font-size: 40rpx;
                }
            }
        }

        .form {
            background-color: #ffffff;
            // #ifdef MP-WEIXIN
            margin-top: 10rpx;

            // #endif
            .label {
                padding: 0 10rpx 10px;

                .order_color {
                    color: var(--primary-color);
                    font-size: 34rpx;
                    font-weight: 700;
                    padding-right: 10rpx;
                }
            }

            .form-item {
                padding: 20rpx 20rpx 40rpx 20rpx;
                border-bottom: 15rpx solid #f6f6f6;
                margin: 0;

                :deep(.uni-forms-item) {
                    margin: 0;
                }

                :deep(.uni-forms-item__label) {
                    padding: 0;
                }
            }
        }

        .sign-warp {
            position: relative;

            .sign_icon_reset {
                position: absolute;
                left: 114px;
                top: -10px;
                z-index: 999;
            }
        }

        .file_list {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12rpx;
            border-top: 8rpx solid #f6f6f6;
            border-bottom: 8rpx solid #f6f6f6;
        }
    }

    .button-group {
        padding: 20rpx;
    }

    .show_esign {
        overflow: hidden;
        border-radius: 15rpx;

        .show_esign_box {
            width: 690rpx;
            height: 640rpx;
            position: relative;

            // 横版
            &.sign_active {
                width: 100vw;
                height: 100vh;

                .title {
                    transform: rotate(90deg);
                    top: 50%;
                    right: -22rpx;
                    left: unset;
                    margin-top: -48rpx;
                }

                .icon_reset {
                    left: 60rpx;
                    right: inherit;
                    bottom: 28rpx;
                    transform: rotate(90deg);
                }

                .icon_amplify {
                    bottom: 28rpx;
                    top: inherit;
                    transform: rotate(90deg);
                }

                .tip {
                    transform: rotate(90deg);
                    width: 100vh;
                    height: 100vw;
                    line-height: 100vw;
                    left: -440rpx;
                    top: 450rpx;
                }

                .footer {
                    transform: rotate(90deg);
                    bottom: 50%;
                    margin-left: -334px;
                }

                .esign {
                    height: calc(100vh - 104rpx);
                    padding: 80rpx 80rpx 20rpx;
                }
            }

            .esign {
                position: relative;
                padding: 80rpx 30rpx 20rpx;
                height: 440rpx;
            }

            .icon_reset,
            .icon_amplify,
            .tip,
            .footer,
            .title {
                text-align: center;
                position: absolute;
                left: 0;
                right: 0;
            }

            .title {
                top: 0;
                padding: 30rpx 0;
                color: $heading-color;
                @include fontSize(34rpx);
            }

            .tip {
                color: #e8e8e8ff;
                @include fontSize(56rpx);
                top: 45%;
                z-index: 99;
                top: 100rpx;
                bottom: 100rpx;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .icon_amplify {
                top: 40rpx;
                right: 40rpx;
                left: inherit;
                @include fontSize(34rpx);
                color: $heading-color;
            }

            .icon_reset {
                left: inherit;
                right: 40rpx;
                bottom: 120rpx;
                @include fontSize(34rpx);
                color: var(--primary-color);
                padding: 10rpx;
            }

            .footer {
                padding: 30rpx 0;
                bottom: 0;

                span {
                    color: var(--primary-color);
                    @include fontSize(34rpx);
                    padding: 0 40rpx;
                }
            }
        }
    }
}
.date_time {
    :deep(.icon-calendar) {
        display: none !important;
    }
    :deep(.uni-date-x) {
        justify-content: flex-start !important;
    }
}
</style>
