<template>
    <view class="notic">
        <z-paging ref="paging" v-model="state.dataList" @query="initPage" :auto="false">
            <template #top>
                <!-- <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="所有发布"
                    status='all' :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar> -->

                <NavBar title="所有发布" status="-" :clickLeft="clickLeft" />
                <uv-tabs :list="allTabList" :current="state.activeTab" @click="clickTabs" :activeStyle="{ color: pattern.line }" :inactiveStyle="{ color: pattern.inactive }" lineWidth="20" :customStyle="{ background: pattern.custom }" :lineColor="pattern.line"></uv-tabs>

                <view class="content">
                    <DropDownFiltering @emitChangeSelect="emitChangeSelect" :tabKey="state.activeTab" tabType="publish" />
                </view>
            </template>
            <Announcement ref="comRef" v-if="allTabList[state.activeTab]?.key === 'announcement'" :dataList="state.dataList" :paramsForm="state.paramsForm" @complete="completeFn" @reload="reloadFn" />
            <News ref="comRef" v-else-if="allTabList[state.activeTab]?.key === 'news'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="news" @complete="completeFn" @reload="reloadFn" />
            <CampusStyle ref="comRef" v-else-if="allTabList[state.activeTab]?.key === 'campusStyle'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="campusStyle" @complete="completeFn" @reload="reloadFn" />
            <Article ref="comRef" v-else-if="allTabList[state.activeTab]?.key === 'article'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="article" @complete="completeFn" @reload="reloadFn" />
            <Receive ref="comRef" v-else-if="allTabList[state.activeTab]?.key === 'receive'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="receive" @complete="completeFn" @reload="reloadFn" />
            <Poster ref="comRef" v-else-if="allTabList[state.activeTab]?.key === 'poster'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="poster" @complete="completeFn" @reload="reloadFn" />
            <OfficialDoc ref="comRef" v-else-if="allTabList[state.activeTab]?.key === 'officialDoc'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="officialDoc" @complete="completeFn" @reload="reloadFn" />
            <Aio_machine ref="comRef" v-else-if="allTabList[state.activeTab]?.key === 'aio_machine'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="aio_machine" @complete="completeFn" @reload="reloadFn" />
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import { defineAsyncComponent, nextTick } from "vue"
import DropDownFiltering from "../components/dropDownFiltering.vue"

import Announcement from "../announcement/list.vue"
import News from "../news/list.vue"
import CampusStyle from "../campusStyle/list.vue"
import Article from "../article/list.vue"
import Receive from "../receive/list.vue"
import Poster from "../poster/list.vue"
import OfficialDoc from "../officialDoc/list.vue"
import Aio_machine from "../aio_machine/list.vue"

const pattern = {
    inactive: "#606266",
    line: "var(--primary-color)",
    custom: "#fff"
}

const comRef = ref(null)
const paging = ref(null)
const allTabList = shallowRef([])

const state = reactive({
    activeTab: 0,
    paramsForm: {
        type: "publish",
        startTime: "",
        endTime: ""
    },
    dataList: []
})
// tables
const clickTabs = (item) => {
    const index = item.index
    state.activeTab = index
}

const clickLeft = () => {
    uni.navigateBack()
}

const emitChangeSelect = (item) => {
    state.paramsForm = item
    state.paramsForm.type = "publish"
}

// 确认
const getAppRouters = () => {
    http.post("/app/appCenter/getAppRouters", { code: "notice" }).then(({ data }) => {
        if (data.length) {
            allTabList.value = []
            // 所有发布
            data[0].children.forEach((item) => {
                if (item.component === "allPublish") {
                    allTabList.value = []
                    item.btnList.forEach((v) => {
                        // 如果是图片/视频， 则改成aio_machine
                        if (v.perms === "aioMachine") {
                            v.perms = "aio_machine"
                        }
                        // 如果是字幕通知这隐藏 因为app 原生没做
                        if (v.perms !== "captions_notice") {
                            allTabList.value.push({
                                name: v.name,
                                componentList: defineAsyncComponent(() => import(`../${v.perms}/list.vue`)),
                                key: v.perms
                            })
                        }
                    })
                }
            })
        }
    })
}
function reloadFn() {
    paging.value?.reload()
}

function completeFn(list) {
    paging.value?.complete(list || false)
}

function initPage(pageNo, pageSize) {
    // 初始化分页 如果comRef.value 存在则初始化分页 否则定时调用
    if (comRef.value) {
        comRef.value.initPage(pageNo, pageSize, true)
    } else {
        setTimeout(() => {
            initPage(pageNo, pageSize, true)
        }, 100)
    }
}

onLoad((options) => {
    initPage(1, 10, true)
    getAppRouters()
})
</script>

<style lang="scss" scoped>
.notic {
    height: 100vh;
    background: $uni-bg-color-grey;

    .bar_right {
        color: var(--primary-color);
    }

    .popup_container {
        background: $uni-bg-color;
        width: 100vw;
        min-height: 200rpx;
        box-sizing: border-box;
        padding: 22rpx 38rpx 34rpx 38rpx;
        border-radius: 16rpx;

        .uni-title {
            height: 60rpx;
            text-align: right;
        }

        .grid-item-box {
            text-align: center;

            .image {
                width: 80rpx;
                height: 80rpx;
            }

            .text {
                font-size: 12px;
                margin: 10rpx auto 20rpx;
            }
        }
    }
}
</style>
