<template>
    <view class="score-list">
        <z-paging ref="paging" :auto="false" @query="getList" v-model="state.scoreList" :inside-more="true">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="成绩表" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <view class="reset-select">
                    <view class="reset-select-item">
                        <uni-data-picker placeholder="请选择班级" popup-title="选择班级" :map="{ text: 'classesName', value: 'classesId' }" :localdata="state.classList" :clear-icon="false" v-model="state.classesId" @change="changeSelect"> </uni-data-picker>
                    </view>
                    <view class="reset-select-item type"> </view>
                </view>
            </template>
            <div v-if="state.scoreList && state.scoreList.length">
                <view class="score-list-item" v-for="item in state.scoreList" :key="item" @click="handlerToDetails(item)">
                    <view class="userName"
                        ><span class="adaptive_hiding user">{{ item.studentName }}</span
                        ><span class="adaptive_hiding clazz">（{{ item.classesName }}）</span>
                    </view>
                    <view class="item" v-if="item.totalScore">{{ item.totalScore }}分</view>
                    <view class="item" v-if="item.totalRank">第{{ item.totalRank }}名<uni-icons type="right" size="14" color="#333"></uni-icons></view>
                </view>
            </div>
            <view v-if="!state.scoreList?.length && pageLoading" class="loading">
                <uv-loading-icon :show="pageLoading" text="加载中..." :vertical="true"></uv-loading-icon>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup name="scoreList">
const paging = ref(null)
const pageLoading = ref(false)
const state = reactive({
    classesId: 0,
    classList: [],
    scoreList: [],
    socleParams: {}
})

const getClassListInfo = async () => {
    await http.get("/app/score/exam/getClassesList", { id: state.socleParams?.examId }).then(({ data }) => {
        state.classList = [{ classesId: 0, classesName: "全部班级" }, ...data]
    })
}
const getList = async (pageNo, pageSize) => {
    pageLoading.value = true
    const params = {
        pageNo,
        pageSize,
        examId: state.socleParams?.examId || "",
        classesId: state.classesId || ""
    }
    await http
        .post("/app/score/exam/employeeDetailPage", params)
        .then(({ data }) => {
            paging.value?.complete(data.list)
        })
        .finally(() => {
            pageLoading.value = false
        })
}
// 跳详情
const handlerToDetails = (item) => {
    const { scoreDetailList = [], studentName = "", examName = "" } = item
    const params = {
        studentName,
        examName,
        scoreDetailList: JSON.stringify(scoreDetailList)
    }
    navigateTo({
        url: "/apps/scoreManage/scoreDetails",
        query: params
    })
}
// 下拉查询
const changeSelect = () => {
    paging.value?.reload()
}

onLoad((item) => {
    state.socleParams = item
    nextTick(async () => {
        await getClassListInfo()
        paging.value?.reload()
    })
})
const clickLeft = () => {
    uni.navigateBack()
}
</script>

<style scoped lang="scss">
.score-list {
    background-color: $uni-bg-color-grey;
    min-height: 100vh;

    .score-list-item {
        margin: 20rpx 30rpx;
        padding: 30rpx;
        background-color: $uni-bg-color;
        border-radius: 50rpx;
        display: flex;
        justify-content: space-between;

        .userName {
            display: flex;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            .adaptive_hiding {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                &.user {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }

                &.clazz {
                    flex: 1;
                    color: $uni-text-color-grey;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 40rpx;
                }
            }
        }

        .item {
            min-width: 130rpx;
            text-align: center;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }
    }

    .reset-select {
        display: flex;
        justify-content: space-between;
        padding: 0 20rpx 0;
        background-color: $uni-bg-color;

        .reset-select-item {
            :deep(.uni-data-tree) {
                .input-value-border {
                    border: none;
                }

                .arrow-area {
                    transform: rotate(0deg);

                    &:before {
                        content: "";
                        display: block;
                        border: 10rpx solid transparent;
                        margin-left: 6rpx;
                        border-top: 10rpx solid var(--primary-color);
                        border-bottom-width: 1px;
                        margin-top: 6rpx;
                    }

                    .input-arrow {
                        display: none;
                    }
                }
            }

            &.type {
                :deep(.uni-select__input-box) {
                    justify-content: end;
                }
            }
        }
    }

    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: $uni-text-color;
    }
    .loading {
        margin-top: 300rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
