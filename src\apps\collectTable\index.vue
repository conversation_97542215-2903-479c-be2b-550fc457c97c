<template>
    <view class="collect">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="state.navBarTitlte" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>

        <view class="content">
            <view class="children-select" v-if="identityType != 'teacher'">
                <uv-drop-down ref="dropChildrenDown" sign="Children_1" text-active-color="#00b781" :extra-icon="{ name: 'arrow-down-fill', color: '#666', size: '26rpx' }" :extra-active-icon="{ name: 'arrow-up-fill', color: '#00b781', size: '26rpx' }" :custom-style="{ padding: '0 30rpx', border: '0rpx' }">
                    <uv-drop-down-item name="type" type="2" :label="state.yourChildren.label" :value="state.yourChildren.value"> </uv-drop-down-item>
                </uv-drop-down>
                <uv-drop-down-popup sign="Children_1" :click-overlay-on-close="true" :currentDropItem="state.yourChildren" @clickItem="clickItem" @popupChange="openPopup"></uv-drop-down-popup>
            </view>
            <view class="collect_tabs" v-if="identityType == 'teacher'">
                <uv-tabs :list="tabsList" :current="state.activeTab" @click="clickTabs" :activeStyle="activeStyle" :inactiveStyle="inactiveStyle" lineWidth="20" :customStyle="customStyle" :lineColor="pattern.buttonColor"></uv-tabs>
            </view>

            <Creates v-if="state.isCreate" v-model:activeTab="state.activeTab" :childrenId="state.yourChildren.value" />
            <template v-else>
                <Statistics v-if="state.activeTab == 1" v-model:activeTab="state.activeTab" :childrenId="state.yourChildren.value" />
                <NewBuild v-else-if="state.activeTab == 2" v-model:isCreate="state.isCreate" v-model:activeTab="state.activeTab" :childrenId="state.yourChildren.value" />
                <FillIn v-else v-model:activeTab="state.activeTab" :childrenId="state.yourChildren.value" />
            </template>
        </view>
    </view>
</template>
<script setup>
import { computed, reactive, watch } from "vue"
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
import Creates from "./creates/index.vue"
import FillIn from "./fillIn/index.vue"
import Statistics from "./statistics/index.vue"
import NewBuild from "./newBuild/index.vue"
import useStore from "@/store"
const { collectTable, user } = useStore()
const dropChildrenDown = ref(null)

const pattern = {
    inactive: "#606266",
    custom: "#fff",
    buttonColor: "var(--primary-color)"
}
const customStyle = {
    background: pattern.custom
}
const inactiveStyle = {
    color: pattern.inactive,
    borderBottom: `2px solid transparent`,
    paddingBottom: "10px"
}
const activeStyle = {
    color: pattern.buttonColor,
    borderBottom: `2px solid ${pattern.buttonColor}`,
    paddingBottom: "10px"
}

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})
const state = reactive({
    dataList: [],
    activeTab: 1,
    navBarTitlte: "新建",
    isCreate: false,
    yourChildren: {
        label: "",
        value: "",
        activeIndex: 0,
        color: "$uni-text-color",
        activeColor: "var(--primary-color)",
        child: []
    }
})
const tabsList = computed(() => {
    if (identityType.value == "teacher") {
        return [
            {
                name: "填写",
                icon: "@nginx/workbench/notice/notice_icon.png",
                key: "fillIn"
            },
            {
                name: "统计",
                icon: "@nginx/workbench/notice/notice_icon.png",
                key: "statistics"
            },
            {
                name: "新建",
                icon: "@nginx/workbench/notice/notice_icon.png",
                key: "newBuild"
            }
        ]
    } else {
        return [
            {
                name: "填写",
                icon: "@nginx/workbench/notice/notice_icon.png",
                key: "fillIn"
            }
        ]
    }
})

watch(
    () => state.isCreate,
    (val) => {
        if (val) {
            state.navBarTitlte = "创建收集"
        }
    }
)
function clickLeft() {
    // #ifdef H5
    const roleArr = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"]
    if (roleArr?.includes(checkPlatform())) {
        sendAppEvent("backApp", {}) ||
            uni.switchTab({
                url: "/pages/workbench/index"
            })
        return
    }
    // #endif
    uni.switchTab({
        url: "/pages/workbench/index"
    })
}
// tables
const clickTabs = (item) => {
    const { index, name } = item
    state.activeTab = index
    state.navBarTitlte = name
    state.isCreate = false
    collectTable.resetDetComponents()
}
function openPopup(e) {
    dropChildrenDown.value?.open()
}
const clickItem = (e) => {
    state.yourChildren.label = e.label || ""
    state.yourChildren.value = e.value || ""
}
// 获取家长孩子
const getYourChildrenInfo = async () => {
    http.get("/app/collectTableWrite/yourChildren")
        .then(({ data }) => {
            state.yourChildren.child = data.map((v, idx) => {
                if (!idx) {
                    state.yourChildren.value = v.studentId
                    state.yourChildren.label = v.studentName
                }
                return {
                    ...v,
                    label: v.studentName,
                    value: v.studentId
                }
            })
        })
        .finally(() => {})
}
onMounted(async () => {
    if (identityType.value != "teacher") {
        await getYourChildrenInfo()
    }
})
onLoad((item) => {
    if (identityType.value == "teacher") {
        if (item?.activeTab == "statistics") {
            tabsList.forEach((v, idx) => {
                if (v.key == "statistics") {
                    clickTabs({ ...v, index: idx })
                }
            })
        }
    } else {
        state.activeTab = 0
        state.navBarTitlte = "填写"
    }
})
</script>
<style scoped lang="scss">
.collect {
    height: 100vh;
    overflow: hidden;
    .collect_tabs {
        z-index: 9999;
        position: absolute;
    }

    :deep(.uv-tabs) {
        border-top: 1px solid #f5f5f5;

        .uv-tabs__wrapper {
            z-index: 9;
        }

        .uv-tabs__wrapper__nav {
            justify-content: space-around;

            .uv-tabs__wrapper__nav__item {
                padding: 0 50px;
            }

            .uv-tabs__wrapper__nav__line {
                display: none;
            }
        }
    }

    .children-select {
        position: relative;
        z-index: 99999;
        border-top: 1rpx solid #f5f5f5;
    }
}
</style>
<style lang="scss">
.reset-select {
    .uv-drop-down {
        border: none !important;
    }
}
</style>
