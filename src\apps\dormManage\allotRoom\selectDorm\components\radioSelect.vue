<template>
  <view class="radio_select_box">
    <view
      :class="['item', { isChecked: item.checked }, { poiner_none: item.atLeastBedCount === 0 }]"
      v-for="(item, index) in list"
      :key="index"
      @click="handleClick(item)"
    >
      <view class="left">
        <slot name="left" :left="item">
          <view class="img">
            <cover-image
              :src="item.img || 'https://alicdn.1d1j.cn/1531914651808833538/default/9d907ff1a80c43a18aa6cbd1f49b2343.png'"
            ></cover-image>
          </view>
          <view>{{ item.name }}</view>
        </slot>
      </view>
      <view :class="['right', { hidden: item.empty }]"
        ><radio @click.stop="handleClick(item)" :value="`${index}`" :checked="item.checked"
      /></view>
    </view>
  </view>
</template>
<script setup>
const props = defineProps({
  value: {
    type: Object,
    default: () => [],
  },
  list: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:value'])

const handleClick = item => {
  item.checked = !item.checked
  const arr = props.list.filter(i => i.checked === true)
  emit('update:value', arr)
}
</script>
<style lang="scss">
.radio_select_box {
  display: flex;
  flex-wrap: wrap;
  .item {
    width: 330rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 28rpx;
    border: 1rpx solid #d9d9d9;
    border-radius: 20rpx;
    margin: 28rpx 0 0 28rpx;
    .left {
      flex: 1;
      display: flex;
      align-items: center;
      color: #333;
      font-size: 32rpx;
      font-weight: 600;
      .sele_btn_text {
        font-size: 28rpx;
        font-weight: normal;
        padding-top: 16rpx;
      }
    }
    .right {
      :deep(.uni-radio-input) {
        margin-right: 0;
      }
    }
  }
  .poiner_none {
    pointer-events: none;
    background-color: #d9d9d9;
    .left {
      color: #fff;
    }
  }
  .hidden {
    display: none;
  }
  .isChecked {
    border-color: #4566d5;
  }
}
</style>
