<template>
    <view class="visitor_record">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="访客系统" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="visitor_record_search">
            <text v-if="!['eltern', 'student'].includes(roleCode)" class="invitation" @click="handleInviteCode">邀请访客</text>
            <uni-search-bar class="search" clearButton="none" placeholder="请输入要搜索的内容" radius="20" v-model.trim="state.name" @cancel="handerCancelConfirm(true)" @confirm="handerCancelConfirm(false)" />
            <div class="system_search_total" v-if="state.searchTotal">搜索结果：共 {{ state.searchTotal }} 条</div>
        </view>
        <view class="visitor_record_content" v-if="state.avatarList.length" :class="{ active: !state.avatarList.length }">
            <view style="height: 11px"></view>
            <uni-list v-for="items in state.avatarList" :key="items.id" :border="false" @click="handerToDetails(items.id)">
                <div class="list">
                    <view class="list_hander">
                        <view class="user_iamge">
                            <image class="iamge" src="@nginx/workbench/visitorSystem/defaultAvatar.png"> </image>
                            <text class="user_name adaptive_hiding">{{ items.fromUserName }}的到访记录</text>
                        </view>
                        <view class="time">
                            {{ createTime(items) }}
                        </view>
                    </view>
                    <view class="list_conent">
                        <view class="item" v-for="item in state.recordList" :key="item.key"> {{ item.label }}：{{ items[item.key] || "-" }} </view>
                    </view>
                </div>
            </uni-list>
            <uni-load-more iconType="circle" :status="state.status" :contentText="state.contentText" />
        </view>
        <yd-empty text="暂无数据" :isMargin="true" v-else />

        <view class="apporval" @click="apporval" v-if="!['eltern', 'student'].includes(roleCode)">
            <view>访客</view>
            <view>审批</view>
            <text class="badge" :class="{ active: state.apporvalCount > 99 }" v-if="state.apporvalCount">
                {{ state.apporvalCount > 99 ? "99+" : state.apporvalCount }}
            </text>
        </view>
    </view>
</template>

<script setup>
import useStore from "@/store"
import dayjs from "dayjs"
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
import { computed } from "vue"

const { user } = useStore()
const mySchoolId = computed(() => user?.schoolInfo?.id)
const roleCode = computed(() => user?.identityInfo?.roleCode)

const state = reactive({
    route: { isTeacher: "false" },
    manageUserId: "",
    apporvalCount: 0,
    avatarList: [],
    status: "more",
    contentText: {
        contentdown: "查看更多", //more
        contentrefresh: "加载中", // loading
        contentnomore: "没有更多" //noMore
    },
    searchTotal: 0,
    recordList: [
        { label: "通行入校时间", key: "inTrafficTime" },
        { label: "通行出校时间", key: "outTrafficTime" },
        { label: "到访原因", key: "reason" }
    ],
    name: "",
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
})

const createTime = computed(() => {
    return (items) => {
        return items.createTime ? dayjs(items.createTime)?.format("YYYY-MM-DD") : "-"
    }
})

// 访客机验证方式设置
const handleInviteCode = () => {
    const params = { schoolId: state.route.schoolId }
    http.get("/cloud/visitor/setting/global/get", params).then(({ data }) => {
        if (data?.schoolId) {
            navigateTo({
                url: "/apps/visitorSystem/visitorInvitation"
            })
        } else {
            uni.showToast({
                title: "未能读取到正确的访客系统设置，请联系管理员在平台端访客应用修改系统设置",
                duration: 3000,
                icon: "none"
            })
        }
    })
}
const getVisitorList = async () => {
    const { personType, name, pagination, manageUserId } = state
    const params = {
        ...pagination,
        name,
        personType,
        manageUserId,
        schoolId: state.route.schoolId
    }
    state.status = "loading"
    await http
        .post("/app/visitor/records/page", params)
        .then(({ data }) => {
            const { list, pageNo, pageSize, total } = data
            state.pagination = { pageNo, pageSize, total }
            if (name) {
                // 搜索条数
                state.searchTotal = total
                state.avatarList = list || []
            } else {
                state.searchTotal = 0
                if (state.avatarList.length) {
                    state.avatarList = state.avatarList.concat(list)
                } else {
                    state.avatarList = list || []
                }
            }
        })
        .finally(() => {
            state.status = "noMore"
            uni.stopPullDownRefresh()
        })
}

function clickLeft() {
    // #ifdef H5 || H5-WEIXIN
    const roleArr = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"]
    if (state.route.routeName && state.route.routeName !== "undefined") {
        // window.parent.postMessage({ visitorSystemBackApp: true }, "*")
        uni.navigateBack()
    }
    if (roleArr.includes(checkPlatform())) {
        sendAppEvent("backApp", {})
    } else {
        uni.navigateBack()
    }
    // #endif
    // #ifdef MP-WEIXIN || APP-PLUS
    uni.navigateBack()
    // #endif
}

const getByPhoneInfo = async () => {
    await http.post("/app/visitor/manageUser/getByPhone", {}).then(({ data }) => {
        const { id } = data
        state.manageUserId = id
    })
}
// 删除清空 查询
const handerCancelConfirm = async (bol) => {
    state.pagination = {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
    bol && (state.name = "")
    state.avatarList = []
    uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
    })
    await getVisitorList()
    uni.stopPullDownRefresh()
}
const getVisitorApproveCountInfo = () => {
    http.get("/cloud/visitor/approve/count").then(({ data }) => {
        state.apporvalCount = data
    })
}
onPullDownRefresh(() => {
    handerCancelConfirm(true)
})
onLoad(async (item) => {
    const { token, schoolId } = item
    state.route = item
    state.route.schoolId = schoolId || mySchoolId.value
    // 原生app 进来 没有schoolId  就获取学校id
    if (!state.route.schoolId && token) {
        await http.get("/cloud/visitor/setting/global/get").then(({ data }) => {
            state.route.schoolId = data.schoolId
        })
    }
    uni.setNavigationBarTitle({
        title: "访客系统" // 新标题内容
    })
    await getByPhoneInfo()
    getVisitorList()
    getVisitorApproveCountInfo()
    uni.pageScrollTo({
        scrollTop: 0
    })
})

// 跳详情
const handerToDetails = (callId) => {
    const params = {
        ...state.route,
        callId,
        noTodo: "visitorRecord"
    }
    navigateTo({
        url: "/apps/visitorSystem/details",
        query: params
    })
}

function apporval() {
    navigateTo({
        url: "/apps/visitorSystem/visitorApproval",
        query: { ...state.route, noTodo: "visitorRecord" }
    })
}

// #ifdef H5
onUnload(() => {
    sendAppEvent("backApp", {})
})
// #endif
// 滚动加载
onReachBottom(() => {
    const { pagination } = state
    if (pagination.total > pagination.pageSize * pagination.pageNo) {
        state.pagination.pageNo++
        getVisitorList()
    }
})
</script>

<style scoped lang="scss">
.visitor_record {
    min-height: 100vh;
    background-color: $uni-bg-color-grey;

    .visitor_record_search {
        background-color: $uni-bg-color;
        margin-bottom: 20rpx;
        .invitation {
            margin: 0 20rpx 0;
            color: var(--primary-color);
            display: flex;
            justify-content: flex-end;
        }

        .system_search_total {
            font-size: 26rpx;
            padding: 10rpx 40rpx;
            background-color: $uni-bg-color-grey;
            position: absolute;
            bottom: -50rpx;
            left: 0;
            right: 0;
        }
    }

    .visitor_record_content {
        background-color: $uni-bg-color;

        &.active {
            padding: 0;
        }

        .list {
            padding-bottom: 22rpx;
            margin: 22rpx 28rpx;

            &:not(:last-child) {
                border-bottom: 2rpx solid $uni-bg-color-grey;
            }

            .list_hander {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .user_iamge {
                    display: flex;
                    align-items: center;
                    margin-bottom: 10rpx;

                    .iamge {
                        width: 60rpx;
                        height: 60rpx;
                    }

                    .user_name {
                        font-size: 32rpx;
                        font-weight: 600;
                        margin-left: 10rpx;
                    }
                }

                .time {
                    color: $uni-text-color-grey;
                    font-size: 24rpx;
                    width: 144rpx;
                }
            }

            .list_conent {
                color: $uni-text-color-grey;

                .item {
                    font-size: 28rpx;
                    margin: 10rpx 0;
                }
            }
        }

        :deep(.uni-load-more) {
            background-color: $uni-bg-color-grey;
        }
    }

    .apporval {
        position: fixed;
        bottom: 226rpx;
        right: 30rpx;
        width: 112rpx;
        height: 112rpx;
        background: var(--primary-color);
        box-shadow: 0rpx 8rpx 8rpx 0rpx #11c68533;
        color: $uni-bg-color;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        font-size: 28rpx;
        flex-direction: column;
        align-items: center;

        .uni-badge-left-margin {
            font-size: 24rpx;
            position: absolute;
            top: 0;
            right: 0;
        }

        .badge {
            transform: scale(0.8);
            width: 50rpx;
            height: 50rpx;
            line-height: 50rpx;
            border-radius: 50%;
            text-align: center;
            position: absolute;
            top: -10rpx;
            right: -10rpx;
            color: $uni-bg-color;
            background-color: $uni-color-error;

            &.active {
                width: 80rpx;
                height: 50rpx;
                line-height: 50rpx;
                border-radius: 40%;
            }
        }
    }

    // 一行自适应隐藏
    .adaptive_hiding {
        width: calc(100vw - 280rpx);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    :deep(.uni-list) {
        border-bottom: 2rpx solid $uni-bg-color-grey;
        // padding: 22rpx 0;
    }
}
</style>
