<template>
    <div class="form_page">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="编辑相册" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <div class="form_box">
            <div class="form_item">
                <uni-easyinput :clearable="false" :maxlength="10" :inputBorder="false" :trim="true" v-model="form.name" placeholder="请输入相册名称" primaryColor="var(--primary-color)"></uni-easyinput>
            </div>
            <div class="switch_item">
                <div class="title">班牌显示该相册</div>
                <switch :checked="form.isShow" color="var(--primary-color)" style="transform: scale(0.7)" />
            </div>
        </div>
        <div class="button_box">
            <button class="delete_btn" @click="deleteSubmit" :disabled="deleteLoading" :loading="deleteLoading">删除相册</button>
            <button class="update_btn" @click="updateSubmit" :disabled="loading" :loading="loading">保存</button>
        </div>
        <yd-popup ref="confirmRef" :titleflag="false" @confirm="dialogConfirm">
            <view
                :style="{
                    padding: '33px 0px 10px 0px'
                }"
                >{{ "确定删除本相册， 并清空里面的所有照片？" }}</view
            >
        </yd-popup>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const confirmRef = ref(null)
const loading = ref(false)
const deleteLoading = ref(false)
const form = ref({})

function clickLeft() {
    uni.navigateBack()
}

function dialogConfirm() {
    deleteLoading.value = true
    http.post("/brand/album/classify/delete", { id: form.value.id })
        .then((res) => {
            uni.showToast({ title: res.message, icon: "none" })
            uni.navigateBack({
                delta: 2
            })
        })
        .finally(() => {
            deleteLoading.value = false
        })
}

function deleteSubmit() {
    confirmRef.value.open()
}

function updateSubmit() {
    if (!form.value.name) {
        uni.showToast({ title: "相册名称不能为空", icon: "none" })
        return
    }
    loading.value = true
    http.post("/brand/album/classify/update", form.value)
        .then((res) => {
            uni.showToast({ title: res.message, icon: "none" })
            uni.navigateBack()
        })
        .finally(() => {
            loading.value = false
        })
}

onLoad((option) => {
    Object.keys(option).forEach((key) => {
        option[key] = decodeURIComponent(option[key])
    })
    form.value = option
})
</script>

<style lang="scss" scoped>
.page_title_box {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: 500;
    font-size: 32rpx;
    color: $uni-text-color;
    line-height: 88rpx;
    text-align: center;
}
.form_page {
    background: $uni-bg-color-grey;
    min-height: calc(100vh - 88rpx);
}
.form_box {
    .form_item {
        background: $uni-bg-color;
        padding: 30rpx;
        margin-bottom: 20rpx;
        :deep(.uni-easyinput__content-input) {
            background: $uni-bg-color-grey;
        }
    }
    .switch_item {
        background: $uni-bg-color;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10rpx 30rpx;
        .title {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }
    }
}
.button_box {
    display: flex;
    justify-content: space-between;
    padding: 30rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    width: calc(100vw - 60rpx);
    background: #fff;
    align-items: center;
    .delete_btn {
        width: 40%;
        background: #fff;
        font-size: 32rpx;
        line-height: 88rpx;
        height: 92rpx;
        border: 1rpx solid $uni-color-error;
        color: $uni-color-error;
    }
    .update_btn {
        height: 92rpx;
        background: #fff;
        background: var(--primary-color);
        font-size: 32rpx;
        line-height: 88rpx;
        color: $uni-text-color-inverse;
        width: 40%;
    }
}
</style>
