<template>
  <view class="add-bank-card-page">
    <NavBar title="添加银行卡" :clickLeft="clickLeft" />
    <!-- 步骤1：首次绑定 - 基本信息和设置支付密码 -->
    <view v-if="currentStep === 1" class="step-content">
      <view class="form-section">
        <uni-list-item v-for="item in addBankFormList" :key="item.value" extra-icon="loop"
          :rightText="item.value || '-'" :clickable="item.value == 'payPassword' && !state.userForm.password"
          @click="handleSetPayPassword">
          <template v-slot:header>
            <view class="slot-box">
              <text class="slot-icon" v-if="item.value == 'payPassword'">*</text>
              <text class="slot-text">{{ item.label }}</text>
            </view>
          </template>
          <template v-slot:footer>
            <view class="slot-footer-box">
              <text class="slot-footer-text" v-if="item.value == 'payPassword'">
                {{ state.userForm.password ? '******' : '请设置支付密码' }}
              </text>
              <text class="slot-footer-text" v-else>{{ state.userForm[item.value] || '-' }}</text>
            </view>
          </template>
        </uni-list-item>
      </view>

      <!-- 下一步按钮 -->
      <view class="bottom-fixed">
        <button class="confirm-btn" :class="{ disabled: !state.userForm.password }" :disabled="!state.userForm.password"
          @click="handleNext">
          下一步
        </button>
      </view>
    </view>

    <!-- 步骤2：银行卡信息表单 -->
    <view v-if="currentStep === 2" class="step-content">
      <view class="form-section">
        <uni-list-item v-for="item in bankCardFormList" :key="item.value" extra-icon="loop"
          :rightText="item.value || '-'" :clickable="true" @click="handleClick(item.value)">
          <template v-slot:header>
            <view class="slot-box">
              <text class="slot-icon">*</text>
              <text class="slot-text">{{ item.label }}</text>
            </view>
          </template>

          <template v-slot:footer>
            <!-- 这里是右侧特需处理的内容 -->
            <view class="slot-footer-box select" v-if="item.value == 'bankId'">
              <uni-data-picker v-model="bankCardInfo.bankId" :localdata="bankOptions" placeholder="请选择银行"
                popup-title="选择开户银行" />
            </view>

            <view class="slot-footer-box select" v-else-if="item.value == 'cardType'">
              <uni-data-picker v-model="bankCardInfo.cardType" :localdata="cardTypeOptions" placeholder="请选择银行卡类型"
                popup-title="选择银行卡类型" />
            </view>

            <view class="sms-input-container" v-else-if="item.value == 'signMsgText'">
              <input ref="smsCodeInput" v-model="bankCardInfo.signMsgText" placeholder="请输入短信验证码" type="number"
                :maxlength="6" class="form-input sms-input" />
              <!-- 发送短信验证 -->
              <button class="sms-btn" :class="{ disabled: !canSendSms || smsCountdown > 0 }"
                :disabled="!canSendSms || smsCountdown > 0" @click="handleSendSms">
                {{ smsCountdown > 0 ? `${smsCountdown}s` : "发送" }}
              </button>
            </view>

            <!-- 这里是表单的右侧内容 -->
            <view class="slot-footer-box" v-else>
              <text class="slot-footer-text accountName" v-if="item.value == 'accountName'">
                <input type="text" style="text-align: right;" v-model="bankCardInfo[item.value]"
                  :placeholder="item.placeholder">
              </text>
              <text class="slot-footer-text" v-else-if="bankCardInfo[item.value]">
                {{ bankCardInfo[item.value] || '-' }}
              </text>
              <text class="slot-footer-placeholder" v-else>
                {{ item.placeholder }}
              </text>
            </view>
          </template>
        </uni-list-item>

        <!-- 协议勾选 -->
      </view>
      <view class="agreement-section">
        <view class="agreement-row" @click="toggleAgreement">
          <view class="checkbox-container">
            <view class="custom-checkbox" :class="{ checked: isAgreementChecked }">
              <uni-icons v-if="isAgreementChecked" type="checkmarkempty" size="16" color="#FFFFFF"></uni-icons>
            </view>
          </view>
          <view class="agreement-text">
            <text class="agreement-normal">请你认真阅读并同意以下协议与内容</text>
            <text class="agreement-link" @click.stop="showAgreement">《快捷支付服务协议》</text>
          </view>
        </view>
        <view class="warning-text">
          温馨提醒：为了保障能正常使用本系统，请核实并确保以上信息无误。
        </view>
      </view>

      <!-- 底部固定按钮 -->
      <view class="bottom-fixed">
        <button class="confirm-btn" :class="{ disabled: !canSubmit }" :disabled="!canSubmit" @click="handleSubmit">
          确 定
        </button>
      </view>
    </view>

    <!-- 数字键盘 -->
    <uv-keyboard ref="keyboard" :mode="state.mode" :tooltip="false" :dotDisabled="state.dotDisabled"
      :safeAreaInsetBottom="true" @backspace="keyboardBackspace" @change="keyboardChange"></uv-keyboard>
  </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import useStore from "@/store"
const { useSmartCard } = useStore()
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)
const addBankFormList = [
  { label: '学校名称', value: 'schoolName' },
  { label: '姓名', value: 'name' },
  { label: '学号/工号', value: 'jobNumber' },
  { label: '卡号', value: 'cardNo' },
  { label: '支付密码', value: 'payPassword', required: true },
]
const bankCardFormList = [
  { label: '姓名', value: 'accountName', placeholder: '请输入持卡人姓名' },
  { label: '身份证号码', value: 'certNo', placeholder: '请输入身份证号码' },
  { label: '开户银行', value: 'bankId', placeholder: '请选择银行' },
  { label: '银行卡类型', value: 'cardType', placeholder: '选择银行卡类型' },
  { label: '银行卡号', value: 'accountNo', placeholder: '请输入银行卡号' },
  { label: '手机号码', value: 'mobile', placeholder: '请输入银行卡绑定的手机号码' },
  { label: '短信验证', value: 'signMsgText', placeholder: '请输入短信验证码' },
]
const keyboard = ref(null)
const state = reactive({
  mode: 'card',
  modeType: '',
  modeLen: 0,
  dotDisabled: true,
  userForm: {
    schoolName: "",
    name: "",
    cardNo: "",
    jobNumber: "",
    password: ""
  },
  isPayPassword: false
})
// 响应式数据
const bankOptions = ref([]);
const currentStep = ref(1); // 当前步骤：1-基本信息，2-银行卡信息
const smsCountdown = ref(0); // 短信倒计时
const isAgreementChecked = ref(false); // 协议勾选状态

// 银行卡类型选项
const cardTypeOptions = reactive([{ value: "DEBITCARD", text: "储蓄卡" }]);
// 银行卡信息
const bankCardInfo = reactive({
  accountName: "",
  mobile: "",
  certNo: "",
  bankId: "",
  cardType: "",
  accountNo: "",
  signMsgText: "",
  signMsgMerTranNo: "",
});
const handleClick = (item) => {
  // 打开键盘
  if (['certNo', 'accountNo', 'mobile'].includes(item)) {
    openKeyBoard(item)
  } else {
  }
}
// 打开键盘
const openKeyBoard = (modeType) => {
  // 输入框长度
  const modeLenObj = {
    certNo: 18,
    accountNo: 19,
    mobile: 11
  }
  state.modeLen = modeLenObj[modeType]
  if (modeType == 'certNo') {
    state.mode = 'card'
  } else {
    state.mode = 'number'
  }
  state.modeType = modeType
  keyboard.value.open()
}
/**
 * 处理键盘按键点击
 * @param {string} key - 按键值
 */
const keyboardChange = (e) => {
  if (bankCardInfo[state.modeType].length >= state.modeLen) {
    return
  }
  bankCardInfo[state.modeType] += e
}
// 键盘中的 删除
const keyboardBackspace = () => {
  bankCardInfo[state.modeType] = bankCardInfo[state.modeType].slice(0, -1)
}



const canSendSms = computed(() => {
  return (
    bankCardInfo.mobile.trim() && /^1[3-9]\d{9}$/.test(bankCardInfo.mobile)
  );
});

const canSubmit = computed(() => {
  return (
    bankCardInfo.accountName &&
    bankCardInfo.certNo.trim() &&
    bankCardInfo.bankId &&
    bankCardInfo.cardType &&
    bankCardInfo.accountNo.trim() &&
    bankCardInfo.mobile.trim() &&
    bankCardInfo.signMsgText.trim() &&
    isAgreementChecked.value
  );
});



/**
 * 设置支付密码
 */
const handleSetPayPassword = () => {
  // 跳转到设置支付密码页面
  useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/bankCard/addBankCard'])
  uni.navigateTo({
    url: "/apps/smartCard/bankCard/payPasswordSetting",
  });
};

// 获取银行卡列表
const getBanklist = () => {
  http.post("/unicard/app/bocom/bank-card/bank-list", {}).then(({ data }) => {
    bankOptions.value = data.map((item) => {
      return {
        value: item.id,
        text: item.bankName,
      };
    });
  });
};
/**
 * 下一步
 */
const handleNext = () => {
  if (!state.userForm.password) return;
  currentStep.value = 2;
  getBanklist()
};

/**
 * 发送短信验证码
 */
const handleSendSms = async () => {
  if (!canSendSms.value || smsCountdown.value > 0) return;
  const params = {
    ...bankCardInfo,
    personId: _returnParams.value.personId
  }
  await http.post("/unicard/app/bocom/bank-card/sign-card-send-sms", params).then(({ data }) => {
    if (data?.responseStatus) {
      bankCardInfo.signMsgMerTranNo = data.signMsgMerTranNo; // 验证码序号
      uni.showToast({
        title: "验证码已发送",
        icon: "none",
      });
      // 开始倒计时
      smsCountdown.value = 60;
      const timer = setInterval(() => {
        smsCountdown.value--;
        if (smsCountdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    }
  }).catch((error) => {
    uni.showToast({
      title: error.message,
      icon: "none",
    });
  });
};


/**
 * 切换协议勾选状态
 */
const toggleAgreement = () => {
  isAgreementChecked.value = !isAgreementChecked.value;
};

/**
 * 显示协议
 */
const showAgreement = () => {
  // 跳转快捷服务协议页面
  // useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/bankCard/addBankCard'])
  uni.navigateTo({
    url: "/apps/smartCard/bankCard/paymentServiceAgreement",
  });
};

/**
 * 提交银行卡信息
 */
const handleSubmit = async () => {

  if (!canSubmit.value) return;

  try {
    uni.showLoading({
      title: "绑定中...",
    });
    const parasm = {
      ...bankCardInfo,
      personId: _returnParams.value.personId
    }
    await http.post("/unicard/app/bocom/bank-card/sign-card-submit", parasm);
    uni.hideLoading();
    uni.showToast({
      title: "银行卡绑定成功",
      icon: "success",
    });
    // 延迟返回
    const _route = _returnRoute.value.pop() || '/apps/smartCard/bankCard/bankList'
    uni.navigateTo({ url: _route })
    // uni.navigateTo({
    //   url: _returnParams.value.crossRoute
    // })
    // useSmartCard.setReturnParams({ ..._returnParams.value, crossRoute: '' })
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "绑定失败，请重试",
      icon: "none",
    });
  }
};

/**
 * 返回处理
 */
function clickLeft() {
  if (currentStep.value == 2 && !state.isPayPassword) {
    currentStep.value = 1
    return
  }
  const _route = _returnRoute.value.pop() || '/apps/smartCard/bankCard/bankList'
  navigateTo({ url: _route })
}
// 获取银行卡信息
const initInfo = () => {
  try {
    return new Promise(async (resolve, reject) => {
      const { data } = await http.post("/unicard/app/card/info", { id: _returnParams.value.personId })
      state.userForm = data
      resolve(data.password)
      if (data.password) {
        currentStep.value = 2
        getBanklist()
      }
    })

  } catch (error) { }
}

onShow(async () => {
  const rse = await initInfo();
  state.isPayPassword = !!rse
});
</script>

<style lang="scss" scoped>
.add-bank-card-page {
  height: calc(100vh - var(--window-top));
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* 步骤内容 */
.step-content {
  flex: 1;
  margin-top: 20rpx;
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  padding: 0 30rpx;

  .slot-box {
    font-weight: 400;
    font-size: 28rpx;

    .slot-icon {
      color: #F5222D
    }

    .slot-text {
      color: #333333;
    }

  }

  :deep(.uni-list-item__container) {
    padding: 28rpx 0 !important;
  }

  .slot-footer-box {
    .slot-footer-text {
      color: #666666;

      &.accountName {
        :deep(.uni-input-placeholder) {
          color: #******** !important;
        }
      }
    }

    &.select {
      :deep(.input-value) {
        height: auto !important;
      }
    }

    .slot-footer-placeholder {
      color: #********;
    }

    :deep(.input-value-border) {
      border: none;

      .placeholder {
        font-weight: 400;
        font-size: 28rpx;
        color: #******** !important;
      }
    }
  }

  .sms-input-container {
    display: flex;
    align-items: flex-start;
    text-align: right;
    gap: 24rpx;

    .sms-input {
      flex: 1;
      font-size: 28rpx;

      :deep(.uni-input-placeholder) {
        font-weight: 400;
        color: #******** !important;
      }
    }

    .sms-btn {
      width: 120rpx;
      height: 60rpx;
      background-color: #00b781;
      color: #ffffff;
      font-size: 24rpx;
      border-radius: 8rpx;
      border: none;
      margin-top: -4px;

      &.disabled {
        background-color: #cccccc;
        color: #999999;
      }

      &:active:not(.disabled) {
        background-color: #00b781;
      }
    }
  }

}

/* 协议区域 */
.agreement-section {
  padding: 0 32rpx;
  border-top: 20rpx solid #f7f7f7;
  background-color: #f7f7f7;

  .agreement-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8rpx;

    .checkbox-container {
      margin-right: 10rpx;

      .custom-checkbox {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid #cccccc;
        border-radius: 4rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &.checked {
          background-color: #00b781;
          border-color: #00b781;
        }
      }
    }

    .agreement-text {
      flex: 1;
      font-weight: 400;
      font-size: 24rpx;

      .agreement-normal {
        color: #999999;
      }

      .agreement-link {
        color: #00b781;
      }
    }
  }

  .warning-text {
    font-weight: 400;
    font-size: 24rpx;
    color: #F5222D;
  }
}

/* 底部固定按钮 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;

  .confirm-btn {
    width: 100%;
    height: 88rpx;
    background-color: #00b781;
    color: #ffffff;
    border: none;
    font-weight: 400;
    font-size: 32rpx;

    &.disabled {
      background-color: #cccccc;
      color: #999999;
    }

    &:active:not(.disabled) {
      background-color: #00b781;
    }
  }
}

/* uni-data-picker 样式调整 */
:deep(.uni-data-picker) {
  .uni-data-picker__input-box {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    height: auto !important;

    .uni-data-picker__input {
      font-size: 28rpx !important;
      color: #333333 !important;
      text-align: right !important;

      &.uni-data-picker__input--placeholder {
        color: #cccccc !important;
      }
    }

    .uni-data-picker__input-arrow {
      color: #cccccc !important;
      font-size: 16rpx !important;
    }
  }
}

.password-placeholder {
  font-weight: 400;
  font-size: 28rpx;
  color: #********;
  ;
}

/* 为底部固定按钮留出空间 */
.step-content {
  padding-bottom: 200rpx;
}
</style>
