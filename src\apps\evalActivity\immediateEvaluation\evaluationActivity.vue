<template>
    <view class="evaluation_activity">
        <z-paging ref="paging" v-model="state.activityList" @query="queryList" :auto="false" class="evaluation_activity">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="评价活动" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <view class="search_input">
                    <input type="text" class="query_input_text" v-model="state.personName" placeholder="搜索活动名称" @input="handlerInput" />
                </view>
                <view class="class_time">
                    <view class="reset_class">
                        <uni-data-select class="reset_select" v-model="state.status" :localdata="clazzList" @change="changeClazz" placeholder="所有"></uni-data-select>
                    </view>
                    <view class="reset_picker">
                        <uni-datetime-picker :border="false" :clearIcon="false" type="daterange" placeholder="选择时间" v-model="state.getStartEndTime" @change="calendarsConfirm" />
                    </view>
                </view>
            </template>
            <div v-if="state.activityList?.length" class="card_list">
                <view class="card" v-for="item in state.activityList" :key="item.id">
                    <view class="card_item">
                        <view class="card_item_content">
                            <view class="hender" @click="lookEvaluationInfo(item)">
                                <view class="status_title">
                                    <view class="status" :style="{ background: activityStatus[item.status]?.color }">{{ activityStatus[item.status]?.name }}</view>
                                    <view class="title ellipsis">{{ item.title }}</view>
                                </view>
                                <uni-icons class="icon_class" type="right" size="14"></uni-icons>
                            </view>
                            <view class="time_num">
                                <text class="time">{{ item.startDate + "-" + item.endDate }} </text>
                                <text class="num">今日参与：{{ item.partakePersonNum }}人</text>
                            </view>
                            <view class="time_num">
                                <text class="time ellipsis" style="padding: 0">{{ item.names?.join("、") }} </text>
                            </view>
                        </view>
                        <div class="split_line" v-if="item.isApprove || item.operationFlag == 1"></div>
                        <view class="card_item_footer">
                            <div class="click_text evaluation_audit" v-if="item.isApprove" @click="evaluationApprove(item)">评价审核</div>
                            <div class="click_text" v-if="item.status == 1 && item.operationFlag == 1" @click="gotoOperation(item)">立即评价</div>
                        </view>
                    </view>
                </view>
            </div>
            <view v-if="!state.activityList?.length && pageLoading" class="loading">
                <uv-loading-icon :show="pageLoading" text="加载中..." :vertical="true"></uv-loading-icon>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>
<script setup>
import { nextTick } from "vue"

const pageLoading = ref(false)
const activityStatus = {
    0: { name: "未开始", color: "#FFFFBB37" },
    1: { name: "进行中", color: "var(--primary-color)" },
    2: { name: "已结束", color: "#595959" }
}
const clazzList = [
    {
        text: "所有",
        value: null
    },
    {
        text: "未开始",
        value: "0"
    },
    {
        text: "进行中",
        value: "1"
    },
    {
        text: "已结束",
        value: "2"
    }
]
const state = reactive({
    personName: "",
    status: null,
    getStartEndTime: [],
    activityList: [],
    activityParams: {}
})
const paging = ref(null)

function clickLeft() {
    uni.navigateBack()
}
// 查询
const handlerInput = () => {
    paging.value.reload()
}

function lookEvaluationInfo(item) {
    navigateTo({
        url: "/apps/evalActivity/immediateEvaluation/evaluationInfo",
        query: item
    })
}

function evaluationApprove(item) {
    navigateTo({
        url: "/apps/evalActivity/approveEvaluation/index",
        query: {
            ...item,
            isApprove: true
        }
    })
}

function gotoOperation(item) {
    navigateTo({
        url: "/apps/evalActivity/immediateEvaluation/index",
        query: item
    })
}
// 调用List数据
function queryList(pageNo, pageSize) {
    const { evalTypeId } = state.activityParams
    const { getStartEndTime, personName, status } = state
    const [startDate, endDate] = getStartEndTime || []
    const params = {
        status,
        endDate,
        startDate,
        evalTypeId,
        pageNo,
        pageSize,
        title: personName
    }
    pageLoading.value = true
    http.post("/app/evalActivity/page", params)
        .then(({ data }) => {
            paging.value.complete(data.list)
        })
        .finally(() => {
            pageLoading.value = false
        })
}

// 选时间
const calendarsConfirm = (val) => {
    paging.value?.reload()
}
// 选择班级
const changeClazz = () => {
    paging.value?.reload()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.activityParams = options || {}
    nextTick(() => {
        paging.value?.reload()
    })
})
</script>
<style lang="scss" scoped>
@mixin reset-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.evaluation_activity {
    background: $uni-bg-color-grey;
    .search_input {
        background: $uni-bg-color;
        padding: 20rpx 30rpx;
        .query_input_text {
            padding: 10rpx 20rpx;
            border-radius: 30rpx;
            font-size: 30rpx;
            background: $uni-bg-color-grey;
        }
    }

    .class_time {
        display: flex;
        justify-content: space-between;
        background: $uni-bg-color;
        padding: 0 30rpx 20rpx;

        .reset_class {
            flex: 1;

            .reset_select {
                :deep(.uni-select) {
                    border: none;
                    height: 20rpx;
                    margin-top: 20rpx;

                    .uni-select__input-text {
                        width: auto;
                        font-size: 28rpx;
                        color: #666666;
                    }

                    .uni-icons:before {
                        content: "";
                        display: block;
                        border: 10rpx solid transparent;
                        margin-left: 6rpx;
                    }

                    .uniui-bottom:before {
                        border-top: 10rpx solid var(--primary-color);
                        border-bottom-width: 1px;
                        margin-top: 6rpx;
                    }

                    .uniui-top:before {
                        border-bottom-color: var(--primary-color);
                        border-top-width: 1px;
                        margin-bottom: 6rpx;
                    }
                }
            }
        }

        .reset_picker {
            display: flex;
            align-items: center;

            &::after {
                content: "";
                display: block;
                border: 10rpx solid transparent;
                border-top: 10rpx solid var(--primary-color);
                border-bottom-width: 1px;
                margin-left: 10rpx;
            }

            :deep(.uni-icons) {
                display: none;
            }
        }
    }
    .card {
        background: $uni-bg-color;
        border-radius: 12rpx;
        margin: 20rpx;

        .card_item {
            padding: 40rpx;

            .card_item_content {
                .hender {
                    @include reset-flex;
                    margin-bottom: 20rpx;

                    .status_title {
                        @include reset-flex;
                        display: flex;
                        align-items: center;
                        .title {
                            font-weight: 500;
                            font-size: 28rpx;
                            color: $uni-text-color;
                            line-height: 40rpx;
                            flex: 1;
                        }
                    }

                    .status {
                        background: var(--primary-color);
                        border-radius: 6rpx;
                        color: $uni-bg-color;
                        font-size: 20rpx;
                        padding: 4rpx 6rpx;
                        margin-right: 10rpx;
                    }
                }

                .time_num {
                    @include reset-flex;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: #666666;
                    margin-top: 10rpx;
                }
            }

            .split_line {
                height: 1rpx;
                background: #d9d9d9;
                width: 100%;
                margin: 30rpx 0rpx;
            }
            .card_item_footer {
                display: flex;
                justify-content: flex-end;
                .click_text {
                    padding: 10rpx 20rpx;
                    margin-left: 24rpx;
                    border: 1rpx solid var(--primary-color);
                    border-radius: 36rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: var(--primary-color);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                .evaluation_audit {
                    color: #fdb500;
                    border: 1rpx solid #fdb500;
                }
            }
        }
    }
}
.loading {
    margin-top: 300rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

:deep(.zp-paging-container) {
    background: $uni-bg-color-grey !important;
}
</style>
