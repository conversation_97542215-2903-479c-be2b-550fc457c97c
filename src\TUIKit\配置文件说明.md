# 微信小程序配置文件说明

本文档详细说明了项目中两个重要的微信小程序配置文件的各个配置项含义。

## 文件概述

- `project.config.json` - 微信小程序项目主配置文件
- `project.private.config.json` - 微信小程序项目私有配置文件
- `project.config.commented.js` - 带详细注释的主配置文件说明（仅供参考）
- `project.private.config.commented.js` - 带详细注释的私有配置文件说明（仅供参考）

> **注意**: JSON 文件不支持注释，因此实际的配置文件中不包含注释。带注释的 `.js` 文件仅作为说明文档使用。

## project.config.json 配置说明

### 基础配置
- `appid`: 微信小程序的 AppID，用于标识小程序身份
- `compileType`: 编译类型，`miniprogram` 表示小程序项目
- `libVersion`: 基础库版本，指定小程序运行的基础库版本

### 打包配置 (packOptions)
- `ignore`: 打包时忽略的文件或目录列表
- `include`: 打包时强制包含的文件或目录列表

### 编译设置 (setting)
- `coverView`: 是否启用 cover-view 组件调试
- `es6`: 是否启用 ES6 转 ES5 编译
- `postcss`: 是否启用 PostCSS 编译
- `minified`: 是否压缩代码
- `enhance`: 是否启用增强编译
- `showShadowRootInWxmlPanel`: 是否在 WXML 面板显示 shadow-root
- `packNpmRelationList`: npm 包关系列表，用于指定需要构建的 npm 包
- `compileWorklet`: 是否编译 worklet 文件
- `uglifyFileName`: 是否混淆文件名
- `uploadWithSourceMap`: 上传时是否携带 sourcemap 文件
- `packNpmManually`: 是否手动配置构建 npm 的路径
- `minifyWXSS`: 是否压缩 WXSS 文件
- `minifyWXML`: 是否压缩 WXML 文件
- `localPlugins`: 是否使用本地插件
- `disableUseStrict`: 是否禁用严格模式
- `useCompilerPlugins`: 是否使用编译器插件
- `condition`: 编译条件配置
- `swc`: 是否启用 SWC 编译器
- `disableSWC`: 是否禁用 SWC 编译器

#### Babel 配置 (babelSetting)
- `ignore`: Babel 编译时忽略的文件列表
- `disablePlugins`: 禁用的 Babel 插件列表
- `outputPath`: Babel 编译输出路径

### 编辑器设置 (editorSetting)
- `tabIndent`: 缩进方式
  - `insertSpaces`: 使用空格缩进
  - `insertTabs`: 使用制表符缩进
- `tabSize`: 缩进大小，表示一个缩进使用的空格数量

### 其他配置
- `condition`: 编译条件配置，用于配置不同的编译模式
- `simulatorPluginLibVersion`: 模拟器插件库版本配置

## project.private.config.json 配置说明

此文件中的内容将覆盖 `project.config.json` 中的相同字段，项目的改动优先同步到此文件中。

### 基础信息
- `description`: 项目描述信息
- `projectname`: 项目名称
- `libVersion`: 基础库版本，会覆盖主配置文件中的版本设置

### 私有设置 (setting)
这些设置会覆盖主配置文件中的相同配置：

- `compileHotReLoad`: 是否启用编译热重载功能
- `urlCheck`: 是否检查合法域名和 web-view 域名
- `coverView`: 是否启用 cover-view 组件调试
- `lazyloadPlaceholderEnable`: 是否启用懒加载占位组件
- `skylineRenderEnable`: 是否启用 Skyline 渲染引擎
- `preloadBackgroundData`: 是否启用数据预拉取
- `autoAudits`: 是否启用自动体验评分
- `useApiHook`: 是否启用 API Hook 功能，用于调试和监控 API 调用
- `useApiHostProcess`: 是否启用 API Host 进程
- `showShadowRootInWxmlPanel`: 是否在 WXML 面板显示 shadow-root
- `useStaticServer`: 是否使用静态服务器
- `useLanDebug`: 是否启用局域网调试
- `showES6CompileOption`: 是否显示 ES6 编译选项
- `checkInvalidKey`: 是否检查无效的 key
- `ignoreDevUnusedFiles`: 是否忽略开发时未使用的文件
- `bigPackageSizeSupport`: 是否支持大包体积（超过 2M 的包）

### 其他配置
- `condition`: 编译条件配置

## 使用建议

1. **主配置文件** (`project.config.json`) 应包含项目的基础配置和团队共享的设置
2. **私有配置文件** (`project.private.config.json`) 用于个人开发环境的特殊设置
3. 私有配置文件通常不应提交到版本控制系统中
4. 修改配置后建议重启微信开发者工具以确保配置生效

## 参考文档

- [微信小程序项目配置文件官方文档](https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html)
