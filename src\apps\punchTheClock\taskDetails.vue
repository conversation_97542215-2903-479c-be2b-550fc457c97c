<template>
    <div>
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed :border="false" left-icon="left" @clickLeft="clickLeft" title="任务详情" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <div class="task_details">
            <div class="task_content">
                <div class="title_box">
                    <div v-if="info.subjectList && info.subjectList[0].name != '不限'">
                        <div class="tip ellipsis" v-for="(item, index) in info.subjectList" :key="index">
                            {{ item.name }}
                        </div>
                    </div>

                    <div class="title ellipsis">{{ info.taskName }}</div>
                </div>
                <div class="content">{{ info.taskDescription }}</div>
            </div>
            <div class="task_content">
                <div class="info_item">
                    <div class="info_lable">发布范围</div>
                    <div class="info_value">
                        <span v-for="(item, index) in info.publishScopeObjList" :key="index"> <span v-if="index != 0">、</span> {{ item.name }} </span>
                    </div>
                </div>
                <div class="info_item">
                    <div class="info_lable">发布时间</div>
                    <div class="info_value">
                        {{ info.publishStart }}
                    </div>
                </div>
                <div class="info_item">
                    <div class="info_lable">截止时间</div>
                    <div class="info_value">
                        {{ info.publishEnd }}
                    </div>
                </div>
                <div class="info_item">
                    <div class="info_lable">打卡频次</div>
                    <div class="info_value" v-if="info.frequency == 'custom'">
                        <!-- 如果是custom自定义的 并且自定义的天数数组为7（周一到周日）那就是每天 -->
                        <span v-if="info.weekendList && info.weekendList.length == 7"> 每天 </span>
                        <!-- 如果自定义天数数组长度不是7则循环出来 -->
                        <span v-else>
                            <span v-for="(item, index) in info.weekendList" :key="index"> <span v-if="index != 0">、</span>{{ item.name }}</span>
                        </span>
                    </div>
                    <!-- 如果不等于custom,就是单次打卡 -->
                    <!-- single就是单次打卡 -->
                    <div class="info_value" v-else>单次打卡</div>
                </div>
                <!-- requirementList 数组不能为空才有打卡要求和上传图片 -->
                <div class="info_item" v-if="isRequire(info.requirementList)">
                    <div class="info_lable">打卡要求</div>
                    <div class="info_value">
                        {{ { upload_picture: "提交时必须包含图片" }[info.requirementList] }}
                    </div>
                </div>
                <!-- 没有打卡要求就没有上传图片 -->
                <div class="info_item" v-if="!parameter.todayHaveSign && parameter.todayCanSign && isRequire(info.requirementList)">
                    <div class="info_lable">上传图片</div>
                    <div class="info_value">
                        <div class="upload_img_box">
                            <div class="url_box" v-for="(url, urlIndex) in fileLists" :key="url">
                                <img class="url" :src="url" alt="" />
                                <uni-icons @click="handerDelete(urlIndex)" class="delect_btn" type="clear" size="24" color="#424242"></uni-icons>
                            </div>
                            <img v-if="fileLists && fileLists.length < 3" @click="handerPicker" class="upload_img" src="@nginx/workbench/punchTheClock/upload_img.png" alt="" />
                        </div>
                    </div>
                </div>
                <div class="info_item" v-if="parameter.alreadySignCount && parameter.totalSignCount">
                    <div class="info_lable">当前已打卡次数</div>
                    <div class="info_value">
                        第
                        <span style="color: var(--primary-color)">{{ parameter.alreadySignCount }}</span
                        >/ {{ parameter.totalSignCount }}天
                    </div>
                </div>
                <div class="info_item" v-if="parameter.signTaskRecordList && parameter.signTaskRecordList.length > 0">
                    <div class="info_lable">打卡时间</div>
                    <div class="clock_time_box" v-for="(item, index) in parameter.signTaskRecordList" :key="index">
                        <div class="time_info" v-if="info.frequency == 'custom'"></div>
                        <div class="line" v-if="info.frequency == 'custom' && index != parameter.signTaskRecordList.length - 1"></div>
                        <div
                            class="clock_time"
                            :style="{
                                marginLeft: info.frequency == 'custom' ? '30rpx' : '0rpx'
                            }"
                        >
                            <span>{{ info.frequency == "custom" ? "打卡时间：" : "单次打卡时间：" }}{{ item.signTime }}</span>
                            <div class="clock_img">
                                <img class="url" :src="url" alt="" v-for="url in item.fileUrlList" :key="url" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="task_content" v-if="!parameter.todayHaveSign && parameter.todayCanSign">
                <span class="punched_in">当前已打卡次数：第{{ parameter.alreadySignCount }}/ {{ parameter.totalSignCount }}天</span>
            </div>
            <div class="bottom_btn" v-if="!parameter.todayHaveSign && parameter.todayCanSign">
                <button hover-class="is-hover" :disabled="isUploadFile" class="btn" @click="punchIn" :loading="isPunchIn">
                    {{ isPunchIn ? "打卡中..." : "打卡" }}
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
const info = ref({})
const parameter = ref({})
const isPunchIn = ref(false)
const isUploadFile = ref(false)
const studentId = ref("")
const fileLists = ref([])
const isRequire = computed(() => {
    return (arr) => {
        return arr && arr.length > 0
    }
})

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    studentId.value = options.type == "parent" ? options.studentId : null
    parameter.value = JSON.parse(options.parameter)
    getInfo(parameter.value.id)
    console.log(parameter.value)
})

function clickLeft() {
    uni.navigateBack()
}

function punchIn() {
    if (!isPunchIn.value) {
        isPunchIn.value = true
        console.log(fileLists.value)
        http.post("/attweb/attendanceSignTask/app/sign", {
            fileUrlList: fileLists.value,
            signTaskId: parameter.value.id,
            studentId: studentId.value
        })
            .then((res) => {
                uni.showToast({
                    title: res.message,
                    icon: "none",
                    duration: 2000
                })
                uni.navigateBack()
            })
            .finally(() => {
                isPunchIn.value = false
            })
    }
}

function handerDelete(index) {
    fileLists.value.splice(index, 1)
}

const myCount = ref(0)

function uploadFile(path, count) {
    http.uploadFile("/file/common/upload", path, {
        folderType: "app"
    })
        .then((res) => {
            if (fileLists.value.length < 3) {
                fileLists.value.push(res)
            }
        })
        .finally(() => {
            myCount.value++
            if (myCount.value >= count) {
                uni.hideLoading()
                isUploadFile.value = false
            }
        })
}

const handerPicker = async () => {
    uni.chooseImage({
        count: 3 - fileLists.value.length,
        success: (res) => {
            if (res.tempFiles && res.tempFiles.length > 0) {
                uni.showLoading({
                    title: "加载中"
                })
                myCount.value = 0
                isUploadFile.value = true
                res.tempFiles.forEach((i) => {
                    uploadFile(i.path, res.tempFiles.length)
                })
            }
        }
    })
}

function getInfo(id) {
    uni.showLoading({
        title: "加载中"
    })
    http.get("/attweb/attendanceSignTask/app/get", { id })
        .then((res) => {
            info.value = res.data
        })
        .finally(() => {
            uni.hideLoading()
        })
}
</script>

<style lang="scss" scoped>
.task_details {
    padding-bottom: 220rpx;
    .title_box {
        display: flex;
        align-items: center;
        .tip {
            display: inline-block;
            min-width: 64rpx;
            max-width: 64rpx;
            border-radius: 4rpx;
            border: 1rpx solid var(--primary-color);
            font-weight: 400;
            font-size: 20rpx;
            color: var(--primary-color);
            line-height: 32rpx;
            text-align: center;
            margin-left: 10rpx;
            margin-bottom: -4rpx;
        }
        .title {
            flex: 1;
        }
    }
    .content {
        margin-top: 30rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 40rpx;
        // text-indent: 60rpx;
    }
    .task_content {
        background: $uni-bg-color;
        padding: 15rpx 30rpx;
        font-weight: 400;
        margin-top: 20rpx;
        .info_item {
            padding: 15rpx 0rpx;
            .info_lable {
                font-size: 24rpx;
                color: #666666;
                line-height: 34rpx;
            }
            .info_value {
                font-size: 28rpx;
                color: $uni-text-color;
                margin-top: 10rpx;
                line-height: 40rpx;
            }
            .upload_img {
                width: 150rpx;
                height: 150rpx;
            }
            .upload_img_box {
                display: flex;
                margin-top: 20rpx;
                .url_box {
                    position: relative;
                    display: flex;
                    width: 150rpx;
                    margin-right: 20rpx;
                    height: 150rpx;
                    .url {
                        width: 150rpx;
                        margin-right: 20rpx;
                        height: 150rpx;
                    }
                    .delect_btn {
                        position: absolute;
                        top: -20rpx;
                        right: -20rpx;
                        z-index: 999;
                    }
                }
            }
            .clock_time_box {
                margin-top: 20rpx;
                height: 100%;
                position: relative;
                display: flex;
                .time_info {
                    width: 16rpx;
                    position: absolute;
                    top: 8rpx;
                    height: 16rpx;
                    background: #b3b3b3;
                    border-radius: 50%;
                }
                .line {
                    position: absolute;
                    height: calc(100% + 30rpx);
                    left: 6rpx;
                    top: 8rpx;
                    width: 4rpx;
                    background: #b3b3b3;
                }
                .clock_time {
                    margin-left: 30rpx;
                }
                .clock_img {
                    display: flex;
                    margin-top: 20rpx;
                    .url {
                        width: 150rpx;
                        height: 150rpx;
                        margin-right: 10rpx;
                    }
                }
            }
        }
        .punched_in {
            font-weight: 500;
            font-size: 30rpx;
            color: $uni-text-color;
            line-height: 42rpx;
        }
    }
    .bottom_btn {
        width: calc(100% - 60rpx);
        padding: 0rpx 30rpx 20rpx 30rpx;
        height: 166rpx;
        background: $uni-bg-color;
        position: fixed;
        bottom: 0rpx;
        left: 0rpx;
        display: flex;
        align-items: center;
        .btn {
            width: 100%;
            height: 92rpx;
            font-weight: 400;
            font-size: 32rpx;
            color: $uni-text-color-inverse;
            line-height: 92rpx;
            text-align: center;
            background: var(--primary-color);
            border-radius: 10rpx;
        }
    }
}
</style>
