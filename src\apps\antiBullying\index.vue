<template>
    <view>
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="校园防霸凌" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
        <view class="container_box">
            <view class="alarm_box">
                <view class="item_box" @click="toAlarm(0, 1)">
                    <view class="l_box" style="background: #fef2ee">
                        <uni-icons fontFamily="antiBullying" :size="24" color="#FD7755">{{ "&#xe617;" }}</uni-icons>
                    </view>
                    <view class="r_box">
                        <view>{{ state.infoTody.todayAllNum || 0 }}</view>
                        <view class="txt">今日告警</view>
                    </view>
                </view>
                <view class="item_box" @click="toAlarm(1, 1)" style="justify-content: center">
                    <view class="l_box" style="background: #eff3fd">
                        <uni-icons fontFamily="antiBullying" :size="22" color="#5289FB">{{ "&#xe616;" }}</uni-icons>
                    </view>
                    <view class="r_box">
                        <view>{{ state.infoTody.todayHandledNum || 0 }}</view>
                        <view class="txt">已处理</view>
                    </view>
                </view>
                <view class="item_box" @click="toAlarm(2, 1)" style="justify-content: flex-end">
                    <view class="l_box" style="background: #ecfbf4">
                        <uni-icons fontFamily="antiBullying" :size="22" color="#10C683">{{ "&#xe619;" }}</uni-icons>
                    </view>
                    <view class="r_box">
                        <view>{{ state.infoTody.todayPendingNum || 0 }}</view>
                        <view class="txt">未处理</view>
                    </view>
                </view>
            </view>
            <view class="alarm_box">
                <view class="item_box" @click="toAlarm(0)">
                    <view class="l_box" style="background: #eff3fd">
                        <uni-icons fontFamily="antiBullying" :size="24" color="#5289FB">{{ "&#xe61a;" }}</uni-icons>
                    </view>
                    <view class="r_box">
                        <view>{{ state.info.allNum || 0 }}</view>
                        <view class="txt">总告警</view>
                    </view>
                </view>
                <view class="item_box" @click="toAlarm(1)" style="justify-content: center">
                    <view class="l_box" style="background: #fef6ee">
                        <uni-icons fontFamily="antiBullying" :size="22" color="#F98A43">{{ "&#xe616;" }}</uni-icons>
                    </view>
                    <view class="r_box">
                        <view>{{ state.info.allHandledNum || 0 }}</view>
                        <view class="txt">已处理</view>
                    </view>
                </view>
                <view class="item_box" @click="toAlarm(2)" style="justify-content: flex-end">
                    <view class="l_box" style="background: #ffefec">
                        <uni-icons fontFamily="antiBullying" :size="22" color="#FD7755">{{ "&#xe619;" }}</uni-icons>
                    </view>
                    <view class="r_box">
                        <view>{{ state.info.allPendingNum || 0 }}</view>
                        <view class="txt">未处理</view>
                    </view>
                </view>
            </view>

            <view class="line_box">
                <view class="top_box">
                    <view>告警统计</view>
                    <view>
                        <!-- <button :class="['btn', state.active === 0 ? 'actived' : '']" type="default" @click="change(0)">今日</button> -->
                        <button class="btn actived" type="default">近一周</button>
                    </view>
                </view>
                <line-charts class="main_box" :chartData="state.lineData" />
            </view>
            <view class="pie_box" style="margin-bottom: 80px">
                <view class="top_box">各类型告警数量占比</view>
                <view class="pie_main_box" v-if="state.pieData.series[0].data.length">
                    <pie-charts class="left_box" type="pie" :chartData="state.pieData" />
                    <view class="right_box">
                        <view class="item_box" v-for="(item, index) in state.pieData.series[0].data" :key="index">
                            <view>
                                <text class="point" :style="{ background: color[index] }"> </text>
                                {{ item.name }}：
                            </view>
                            数量：{{ item.value }} 占比：{{ fixedNum(item.value) }}%
                        </view>
                    </view>
                </view>
                <view v-else class="empty_box">暂无数据</view>
            </view>
            <view class="footer">
                <view class="item active">
                    <view>
                        <uni-icons fontFamily="antiBullying" :size="18" color="var(--primary-color)">{{ "&#xe615;" }}</uni-icons>
                    </view>
                    <view class="view">首页</view>
                </view>
                <view class="item" @click="tabClick">
                    <view>
                        <uni-icons fontFamily="antiBullying" :size="20">{{ "&#xe618;" }}</uni-icons>
                    </view>
                    <view class="view">告警</view>
                </view>
            </view>

            <!-- 预警弹窗 -->
            <uni-popup ref="popupRef" type="center" @change="popupChange">
                <view class="popup_container">
                    <view class="title">
                        <view class="t_left">
                            <uni-icons fontFamily="antiBullying" :size="20" color="#FB1717">{{ "&#xe622;" }}</uni-icons>
                            霸凌预防告警{{ state.popInfo.alarmTypeName }}！</view
                        >
                        <uni-icons type="closeempty" size="20" color="#9D9D9D" @click="handleClose"></uni-icons>
                    </view>
                    <view class="content_box">
                        {{ state.popInfo.siteName }}-{{ state.popInfo.deviceName }}发生告警，请尽快处理！！
                        <view class="time">{{ state.popInfo.eventTime }}</view>
                    </view>
                    <view class="footer_box">
                        <button class="btn" @click="handleClose">忽略</button>
                        <button class="btn primary" @click="toAlarmHandle">去处理</button>
                    </view>
                </view>
            </uni-popup>
        </view>
    </view>
</template>
<script setup>
import lineCharts from "./components/LineCharts.vue"
import pieCharts from "./components/PieCharts.vue"
const state = reactive({
    info: {},
    infoTody: {},
    active: 0,
    lineData: {
        categories: [],
        series: [
            {
                data: []
            }
        ]
    },
    pieData: {
        series: [
            {
                data: []
            }
        ]
    },
    popInfo: {},
    noticeList: []
})
const color = ["#F98A43", "#5289FB", "#10C683"]

const total = ref(0)

const fixedNum = (num) => {
    if (!num) return 0
    return Math.round((num / total.value) * 100)
}

const popupRef = ref(null)
const handleOpen = () => {
    if (!state.noticeList.length) return
    // popupRef.value.open()
    const len = state.noticeList.length
    const item = state.noticeList.shift()
    state.popInfo = item
    state.popInfo.alarmTypeName = `（${len}）`
}

const handleClose = () => {
    popupRef.value.close()
}

const popupChange = (e) => {
    if (e.show === false) {
        http.post("/app/anti-bullying/mgmt/alarm-stat/notice-close", { id: state.popInfo.id }).then((res) => {
            setTimeout(() => {
                handleOpen()
            }, 600)
        })
    }
}

const toAlarmHandle = () => {
    navigateTo({
        url: "/apps/antiBullying/alarmHandle/index",
        query: {
            id: state.popInfo.alarmId
        }
    })
    handleClose()
}

// 跳转告警列表
const toAlarm = (type, time = "all") => {
    navigateTo({
        url: "/apps/antiBullying/alarm/index",
        query: {
            type,
            time
        }
    })
}

const tabClick = () => {
    navigateTo({
        url: "/apps/antiBullying/alarm/index"
    })
}

// 获取告警统计信息(今日)
const getOverviewToday = () => {
    http.post("/app/anti-bullying/mgmt/alarm-stat/overview-today").then((res) => {
        state.infoTody = res.data
    })
}

// 获取告警统计信息(累计)
const getOverview = () => {
    http.post("/app/anti-bullying/mgmt/alarm-stat/overview").then((res) => {
        state.info = res.data
    })
}

// 告警时段统计(周)
const trendingLastDays = () => {
    http.post("/app/anti-bullying/mgmt/alarm-stat/trending-last-days", { days: 7 }).then((res) => {
        if (!res.data && !res.data.length) return
        const categories = []
        const data = []
        res.data.forEach((item) => {
            categories.push(item.date.slice(-2))
            data.push(item.alarmNum)
        })

        setTimeout(() => {
            state.lineData = {
                categories,
                series: [
                    {
                        data
                    }
                ]
            }
        }, 500)
    })
}

// 各类型告警数量占比
const countTypes = () => {
    http.post("/app/anti-bullying/mgmt/alarm-stat/count-by-types").then((res) => {
        if (!res.data && !res.data.length) return
        const data = res.data.map((item) => ({
            name: item.alarmTypeName,
            value: item.alarmNum
        }))

        total.value = data.reduce((a, b) => a + b.value, 0)
        data.forEach((item) => {
            item.labelText = fixedNum(item.value) + "%"
        })

        setTimeout(() => {
            state.pieData = {
                series: [
                    {
                        data
                    }
                ]
            }
        }, 500)
    })
}

// 获取告警弹窗列表
const listNotice = () => {
    http.post("/app/anti-bullying/mgmt/alarm-stat/list-notice", {}).then((res) => {
        if (!res.data.length) return
        state.noticeList = res.data.reverse()
        handleOpen()
    })
}

onMounted(() => {
    getOverviewToday()
    getOverview()
    trendingLastDays()
    countTypes()
    listNotice()
})
</script>
<style lang="scss" scoped>
.container_box {
    min-height: calc(100vh - 160rpx);
    background: #f7f7f7;
    padding: 24rpx 30rpx;
    .alarm_box {
        border-radius: 16rpx;
        background: $uni-bg-color;
        display: flex;
        height: 148rpx;
        padding: 38rpx 40rpx;
        box-sizing: border-box;
        margin-bottom: 24rpx;
        .item_box {
            flex: 1;
            flex-shrink: 0;
            display: flex;
            font-size: 20rpx;
            .l_box {
                width: 72rpx;
                height: 72rpx;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .r_box {
                display: flex;
                flex-direction: column;
                justify-content: space-evenly;
                margin-left: 10rpx;
                font-size: 24rpx;
                color: #2b2b2b;
                font-weight: 600;
                .txt {
                    color: #2b2b2b;
                }
            }
        }
    }
    .footer {
        position: fixed;
        width: 100%;
        bottom: 0;
        left: 0;
        display: flex;
        justify-content: space-evenly;
        height: 120rpx;
        background: $uni-bg-color;
        box-shadow: 0rpx 4rpx 12rpx 4rpx rgba(197, 197, 197, 0.5);
        font-size: 20rpx;
        .item {
            view-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100rpx;
            text-align: center;
            .view {
                margin-top: 8rpx;
            }
        }
        .active {
            color: var(--primary-color);
        }
    }
    .line_box {
        width: 100%;
        height: 480rpx;
        background: $uni-bg-color;
        border-radius: 16rpx;
        padding: 28rpx 0;
        box-sizing: border-box;
        margin-bottom: 24rpx;
        .top_box {
            display: flex;
            justify-content: space-between;
            font-size: 28rpx;
            font-weight: bold;
            color: #000;
            padding: 0 30rpx;
            margin-bottom: 10rpx;
            .btn {
                height: 36rpx;
                display: inline-block;
                color: #7f7f7f;
                font-size: 24rpx;
                line-height: 36rpx;
                padding: 0;
                background: transparent;
                font-weight: normal;
                padding: 0 10rpx;
                margin-left: 16rpx;
                &:after {
                    border-radius: 4rpx;
                }
            }
            .actived {
                color: $uni-text-color-inverse;
                background: var(--primary-color);
            }
        }
        .main_box {
            height: calc(100% - 70rpx);
        }
    }
    .pie_box {
        width: 100%;
        height: 392rpx;
        box-sizing: border-box;
        background: $uni-bg-color;
        border-radius: 16rpx;
        padding-top: 32rpx;
        .top_box {
            font-size: 28rpx;
            font-weight: bold;
            color: #000;
            padding: 0 30rpx;
        }
        .pie_main_box {
            height: calc(100% - 60rpx);
            display: flex;
            .left_box {
                height: calc(100% - 60rpx);
                width: calc(100% - 280rpx);
            }
            .right_box {
                display: flex;
                width: 280rpx;
                flex-direction: column;
                justify-content: center;
                .item_box {
                    color: #8b8b8b;
                    font-size: 20rpx;
                    margin: 14rpx 0;
                    .point {
                        width: 16rpx;
                        height: 16rpx;
                        border-radius: 50%;
                        margin-right: 4rpx;
                        display: inline-block;
                        vertical-align: middle;
                        margin-left: -26rpx;
                    }
                }
            }
        }
        .empty_box {
            color: #ccc;
            text-align: center;
            margin-top: 120rpx;
        }
    }
    .popup_container {
        background: $uni-bg-color;
        width: 600rpx;
        box-sizing: border-box;
        padding: 22rpx 38rpx 34rpx 38rpx;
        border-radius: 16rpx;
        .title {
            display: flex;
            justify-content: space-between;
            margin-bottom: 14rpx;
            .t_left {
                font-size: 36rpx;
                color: #313131;
            }
        }
        .content_box {
            font-size: 28rpx;
            color: #8b8b8b;
            line-height: 44rpx;
            .time {
                text-align: right;
                font-size: 20rpx;
                color: #828282;
            }
        }
        .footer_box {
            display: flex;
            margin-top: 24rpx;
            .btn {
                font-size: 28rpx;
                width: 144rpx;
                height: 48rpx;
                border-radius: 24rpx;
                background: #d8d8d8;
                color: #8b8b8b;
                line-height: 48rpx;
                text-align: center;
            }
            .primary {
                color: $uni-text-color-inverse;
                background: #10c683;
            }
        }
    }
}
</style>
