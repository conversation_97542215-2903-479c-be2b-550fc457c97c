<template>
    <z-paging ref="paging" class="container_box">
        <template #top>
            <uni-nav-bar :showMenuButtonWidth="true" color="#fff" backgroundColor="var(--primary-color)" fixed statusBar :border="false" left-icon="left" title="确认缴纳" @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" />
        </template>
        <view class="main_box">
            <view class="main">
                <view class="title">{{ state.remark }}</view>
                <image style="width: 100%; height: 560rpx" :src="state.qrcodeUrl"></image>
            </view>
        </view>
        <template #bottom>
            <view class="foot">
                <button class="btn" @click="handleDownload">下载保存图片</button>
            </view>
        </template>
    </z-paging>
</template>
<script setup>
const state = reactive({})

function saveImageToPhotosAlbum(tempFilePath) {
    // #ifdef MP-WEIXIN
    uni.getSetting({
        success: (res) => {
            // 检查是否已授权相册权限
            if (!res.authSetting["scope.writePhotosAlbum"]) {
                // 未授权，请求授权
                uni.authorize({
                    scope: "scope.writePhotosAlbum",
                    success: () => {
                        downloadAndSaveImage(tempFilePath)
                    },
                    fail: () => {
                        uni.showModal({
                            title: "提示",
                            content: "需要您授权保存图片到相册",
                            showCancel: false,
                            success: (res) => {
                                if (res.confirm) {
                                    uni.openSetting() // 打开设置页面让用户手动开启
                                }
                            }
                        })
                    }
                })
            } else {
                // 已授权，直接保存
                downloadAndSaveImage(tempFilePath)
            }
        }
    })
    // #endif

    // #ifndef MP-WEIXIN
    uni.showToast({
        title: "此功能仅支持微信小程序",
        icon: "none"
    })
    // #endif
}

// 下载并保存图片
function downloadAndSaveImage(tempFilePath) {
    uni.downloadFile({
        url: tempFilePath,
        success: (res) => {
            if (res.statusCode === 200) {
                uni.saveImageToPhotosAlbum({
                    filePath: res.tempFilePath,
                    success: () => {
                        uni.showToast({
                            title: "保存成功",
                            icon: "success"
                        })
                    },
                    fail: () => {
                        uni.showToast({
                            title: "保存失败",
                            icon: "none"
                        })
                    }
                })
            }
        },
        fail: (err) => {
            console.error("下载图片失败:", err)
            uni.showToast({
                title: "下载图片失败",
                icon: "none"
            })
        }
    })
}

const handleDownload = async () => {
    try {
        // #ifdef H5
        // const a = document.createElement('a')
        // a.href = state.qrcodeUrl
        // a.download = '二维码' // 设置下载文件名
        // document.body.appendChild(a)
        // a.click()
        // document.body.removeChild(a)
        uni.setClipboardData({
            data: state.qrcodeUrl
        })
        uni.showModal({
            title: "提示",
            content: "图片链接已复制请在浏览器打开下载",
            showCancel: false,
            confirmColor: "#00D190"
        })
        // #endif

        // #ifdef MP-WEIXIN
        saveImageToPhotosAlbum(state.qrcodeUrl)
        // #endif
    } catch (error) {
        console.error(error)
    }
}

const back = () => {
    uni.navigateBack({
        delta: 1
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        state[key] = decodeURIComponent(options[key])
    })
})
</script>
<style lang="scss" scoped>
.container_box {
    background: var(--primary-color);
    .main_box {
        padding: 30rpx;
        .main {
            background: #fff;
            padding: 40rpx 44rpx;
            padding: 64rpx;
            border-radius: 16rpx;
            .title {
                color: #f5222d;
                font-size: 26rpx;
                margin-bottom: 30rpx;
                text-align: center;
            }
        }
    }
    .foot {
        padding: 30rpx;
        padding-bottom: 60rpx;
        .btn {
            height: 92rpx;
            border-radius: 46rpx;
            color: #fff;
            background: var(--primary-color);
            border: 2rpx solid #ffffff;
        }
    }
}
</style>
