<template>
    <view class="select_admin_box">
        <z-paging>
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" fixed statusBar @clickLeft="back" leftText="取消" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
                    <view class="top_nav_bar">
                        <view class="top_text">寝室长</view>
                    </view>
                </uni-nav-bar>
            </template>
            <view class="main_box">
                <select-list :list="leaderInfo.list" select-flag="radio" @itemClick="itemClick">
                    <template #default="{ item: { name, avatar } }">
                        <view class="tree_item_box">
                            <view class="left_box">
                                <img class="img" v-if="avatar" :src="avatar" />
                                <view v-else class="chart_at">{{ name.charAt(0) }}</view>
                                <text class="text">{{ name }}</text>
                            </view>
                        </view>
                    </template>
                </select-list>
            </view>
            <template #bottom>
                <view class="footer_box">
                    <button type="default" class="yd_btn_primary" @click="save">保存</button>
                </view>
            </template>
        </z-paging>
    </view>
</template>
<script setup>
import { reactive } from "vue"
import selectList from "../../components/selectList.vue"
import { onLoad } from "@dcloudio/uni-app"
import Http from "@/utils/http"

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    leaderInfo.id = options.id
    getStudent(options.id)
})

let leaderInfo = reactive({
    list: [],
    id: "",
    data: {}
})

const itemClick = (item) => {
    leaderInfo.data = item
}

async function getStudent(id) {
    const { data } = await Http.get("/app/dormitory/managerManage/obtainCheckInStudent", { id })
    leaderInfo.list = data
}

const save = async () => {
    if (!Object.keys(leaderInfo.data).length) {
        uni.showToast({
            title: "请选择人员",
            icon: "none"
        })
        return
    }
    const params = {
        id: leaderInfo.id,
        roomAdministrator: [leaderInfo.data.id]
    }
    await Http.post("/app/dormitory/managerManage/updateRoomInfo", params)
    uni.showToast({
        title: "保存成功",
        icon: "none"
    })
    const paramsData = {
        code: "roomAdministrator",
        text: leaderInfo.data.name
    }
    uni.$emit("editDorm", paramsData)
    back()
}

const back = () => {
    uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.select_admin_box {
    background-color: $uni-bg-color-grey;
    :deep(.uni-navbar__content) {
        border-bottom-color: transparent !important;
    }
    .top_nav_bar {
        text-align: center;
        margin: auto;
        .top_text {
            font-size: 32rpx;
            font-weight: 600;
        }
    }
    .main_box {
        background-color: #fff;
        padding-bottom: 150rpx;
        margin-top: 20rpx;
        .tree_item_box {
            width: 100%;
            display: flex;
            justify-content: space-between;
            font-size: 32rpx;
            color: #333333;
            .left_box {
                display: flex;
                align-items: center;
                .img {
                    width: 60rpx;
                    height: 60rpx;
                    border-radius: 50%;
                    overflow: hidden;
                }
                .chart_at {
                    display: inline-block;
                    width: 60rpx;
                    height: 60rpx;
                    text-align: center;
                    line-height: 60rpx;
                    color: #fff;
                    font-size: 32rpx;
                    border-radius: 50%;
                    background-color: #4566d5;
                    font-weight: 600;
                }
                .text {
                    font-size: 28rpx;
                    color: #333;
                    margin-left: 24rpx;
                }
            }
            .text_right {
                font-size: 28rpx;
                color: #999999;
                margin: auto 0;
            }
        }
    }
    .footer_box {
        box-sizing: border-box;
        padding: 30rpx;
        background-color: #fff;
        .yd_btn_primary {
            background-color: #4566d5;
            color: #fff;
        }
        button {
            font-size: 32rpx;
            margin-left: 0;
            margin-right: 0;
        }
    }
}
</style>
