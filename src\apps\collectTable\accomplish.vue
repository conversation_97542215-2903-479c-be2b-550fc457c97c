<!-- accomplish 完成 -->
<template>
    <div class="accomplish">
        <uni-nav-bar :showMenuButtonWidth="true" class="nav" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="完成" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <div class="accomplish_content">
            <image class="img" src="https://file.1d1j.cn/cloud-mobile/components/accomplish_slices.png" />
            <p class="tips">收集创建完成</p>
            <button class="mini-btn" type="primary" plain="true" size="mini" style="border-color: var(--primary-color); color: var(--primary-color)" @click="onClickLeft">回到首页</button>
        </div>
    </div>
</template>

<script setup>
// 返回
function clickLeft() {
    uni.navigateBack(1)
}
const onClickLeft = () => {
    navigateTo({
        url: "/apps/collectTable/index"
    })
}
</script>

<style scoped lang="scss">
@import "./style.scss";

.accomplish {
    background-color: #f6f6f6;
    height: 100vh;
    .accomplish_content {
        text-align: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        .img {
            width: 360rpx;
            height: 408rpx;
        }

        .tips {
            margin: 0 auto 100rpx;
            font-size: 30px;
            text-align: center;
        }
    }
}
</style>
