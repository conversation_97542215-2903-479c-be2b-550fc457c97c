<template>
    <div class="today_ranking">
        <z-paging ref="paging" v-model="state.rankingList" @query="queryList">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="今日得分排行" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
            </template>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
            <div v-if="state.rankingList?.length" class="ranking_list">
                <div
                    class="item"
                    v-for="(item, index) in state.rankingList"
                    :key="item.toPersonId"
                    :style="{
                        background: myBackground(index + 1)
                    }"
                >
                    <div class="left">
                        <div class="medal">
                            <image class="medal_img" v-if="index + 1 == 1" src="@nginx/workbench/evalActivity/teacher/one_ranking.png" alt />
                            <image v-else-if="index + 1 == 2" class="medal_img" src="@nginx/workbench/evalActivity/teacher/two_ranking.png" alt />
                            <image v-else-if="index + 1 == 3" class="medal_img" src="@nginx/workbench/evalActivity/teacher/three_ranking.png" alt />
                            <div class="index" v-else>{{ index + 1 }}</div>
                        </div>
                        <div class="avatar" v-if="item.avatar">
                            <image mode="aspectFill" class="avatar_img" :src="item.avatar" alt="" />
                        </div>
                        <div class="name ellipsis">{{ item.toPersonName }}</div>
                        <div class="class_dept ellipsis">（{{ item.orgName }}）</div>
                    </div>
                    <div class="right">
                        <div class="num">{{ item.maxScore }}分</div>
                    </div>
                </div>
            </div>
            <view v-if="!state.rankingList?.length && pageLoading" class="loading">
                <uv-loading-icon :show="pageLoading" text="加载中..." :vertical="true"></uv-loading-icon>
            </view>
        </z-paging>
    </div>
</template>

<script setup>
const pageLoading = ref(false)
const evalTypeId = ref("")
const paging = ref(null)
const identity = ref("0") // 默认身份为学生
const state = reactive({
    rankingList: []
})

const myBackground = computed(() => {
    return (index) => {
        const color = {
            1: "#FEF6DB",
            2: "#E9F1FF",
            3: "#FFF1E9"
        }
        if ([1, 2, 3].includes(index)) {
            return color[index]
        } else {
            return "$uni-bg-color-grey"
        }
    }
})

// 调用List数据
function queryList(pageNo, pageSize) {
    const params = {
        pageNo,
        pageSize,
        evalTypeId: evalTypeId.value,
        identity: identity.value
    }
    pageLoading.value = true

    http.post("/app/evalDayRulePerson/dayScoreMaxCountPage ", params)
        .then(({ data }) => {
            paging.value.complete(data.list)
        })
        .finally(() => {
            pageLoading.value = false
        })
}

function clickLeft() {
    uni.navigateBack()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    evalTypeId.value = options.evalTypeId || ""

    identity.value = options.identity
})
</script>

<style lang="scss" scoped>
.today_ranking {
    min-height: 100vh;
    background: $uni-bg-color-grey;

    .ranking_list {
        margin-top: 20rpx;
        padding: 30rpx;
        min-height: 100rpx;
        background: $uni-bg-color;

        .item {
            height: 60rpx;
            border-radius: 50rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20rpx 30rpx;
            margin-bottom: 30rpx;

            .left {
                align-items: center;
                flex: 1;
                display: flex;

                .medal {
                    width: 48rpx;
                    height: 60rpx;
                    margin-right: 30rpx;

                    .medal_img {
                        width: 48rpx;
                        height: 60rpx;
                    }

                    .index {
                        width: 48rpx;
                        line-height: 60rpx;
                        font-size: 36rpx;
                        color: $uni-text-color-grey;
                        text-align: center;
                    }
                }

                .avatar {
                    width: 40rpx;
                    height: 40rpx;
                    background: var(--primary-color);
                    border-radius: 50%;
                    text-align: center;
                    margin-right: 20rpx;

                    .avatar_img {
                        width: 40rpx;
                        height: 40rpx;
                        border-radius: 50%;
                    }
                }

                .name {
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                    min-width: 20%;
                }

                .class_dept {
                    font-size: 28rpx;
                    color: $uni-text-color-grey;
                    line-height: 40rpx;
                }
            }

            .right {
                font-weight: 400;
                font-size: 28rpx;
                color: #666666;
                line-height: 40rpx;
            }
        }
    }
    .loading {
        margin-top: 300rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
