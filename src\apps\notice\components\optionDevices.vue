<template>
    <view class="notification-scope">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="班牌" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" />
        <view class="reset-list">
            <uni-section v-for="items in state.propsForm.deviceList" :title="items.showName" :key="items.key" @click="handlerSection(items)">
                <uni-list>
                    <uni-list-item v-for="item in items.children" :key="item.brandId" :title="item.showName" />
                </uni-list>
            </uni-section>
            <yd-empty class="yd-empty" v-if="!state.propsForm.deviceList.length" text="暂无数据" />
        </view>
    </view>
</template>

<script setup>
import { reactive } from "vue"
const eventChannel = getCurrentInstance().proxy.getOpenerEventChannel()
const state = reactive({
    deviceList: [],
    propsForm: {
        deviceList: [
            {
                name: "",
                showName: "暂无",
                id: "",
                key: ""
            },
            {
                name: "班牌",
                showName: "班牌",
                id: "2",
                key: "banPai"
                // },
                // {
                //     name: "借还书机",
                //     showName: "借还书机",
                //     id: "6",
                // key: 'returnMachine'
            }
        ]
    }
})
const handlerSection = (item) => {
    // navigateTo({
    //     url: '/apps/notice/announcement/create',
    //     query: {
    //         key: item.key,
    //         name: item.name
    //     }
    // })
    eventChannel.emit("selectMember", item)
    uni.navigateBack()
}

const clickLeft = () => {
    uni.navigateBack()
}
onLoad((options) => {
    // state.propsForm = options
})
</script>

<style lang="scss" scoped>
.yd-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* #ifdef MP-WEIXIN */
    transform: translate(-50%, 100%);
    /* #endif */
}

.notification-scope {
    height: 100vh;
    background: $uni-bg-color-grey;

    .reset-list {
        margin: 10rpx 0;
        overflow: hidden auto;
        height: calc(100vh - 100rpx);

        :deep(.uni-section-header) {
            .distraction {
                font-size: 28rpx;
                font-weight: 600;
            }
        }
    }
}
</style>
