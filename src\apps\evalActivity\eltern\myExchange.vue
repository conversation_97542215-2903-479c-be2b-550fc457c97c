<template>
    <div class="my_exchange">
        <z-paging ref="paging" v-model="state.exchangeList" @query="queryList">
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="我的兑换" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <!-- 筛选 -->
                <div class="sift_box">
                    <div class="date">
                        <uni-datetime-picker :border="false" :clearIcon="false" type="daterange" placeholder="选择时间" v-model="state.scoreStartEndTime" @change="calendarsConfirm" />
                    </div>
                </div>
            </template>

            <!-- 列表 -->
            <div v-if="state.exchangeList?.length" class="exchange_list">
                <view class="exchange_item" v-for="item in state.exchangeList" :key="item.id">
                    <span class="title ellipsis">{{ `${item.personName}（${item.groupNameList?.join("、") || ""}）` }}</span>
                    <div class="text">
                        <div class="commodity ellipsis">
                            <div class="label" v-if="item.status == 1">已退还</div>
                            <span class="ellipsis commodity_name">
                                {{ item.commodityName || "-" }}
                            </span>
                        </div>
                        <span class="score">-{{ item.commodityScore || 0 }}</span>
                    </div>
                </view>
            </div>
        </z-paging>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const paging = ref(null)
const state = reactive({
    paramsIntegral: {},
    scoreStartEndTime: [],
    exchangeList: []
})

// 调用List数据
function queryList(pageNo, pageSize) {
    const [exchangeStartTime, exchangeEndTime] = state.scoreStartEndTime
    const params = {
        exchangeStartTime,
        exchangeEndTime,
        ...state.paramsIntegral,
        pageNo,
        pageSize
    }
    uni.showLoading({
        title: "加载中..."
    })
    http.post("/app/evalExchange/pageEvalExchangeRecord", params)
        .then(({ data }) => {
            paging.value?.complete(data.list)
        })
        .finally(() => {
            uni.hideLoading()
        })
}

function clickLeft() {
    uni.navigateBack()
}

function calendarsConfirm(date) {
    state.scoreStartEndTime = date
    paging.value?.reload()
}
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.paramsIntegral = {
        identity: 0,
        ...options
    }
})
</script>

<style lang="scss" scoped>
.my_exchange {
    height: 100vh;
    background: $uni-bg-color-grey;

    .sift_box {
        background: $uni-bg-color;
        padding: 10rpx;

        .date {
            width: 52%;
            display: flex;
            align-items: center;

            &::after {
                content: "";
                display: block;
                border: 10rpx solid transparent;
                border-top: 10rpx solid var(--primary-color);
                border-bottom-width: 1px;
                margin-left: 10rpx;
            }

            :deep(.uni-icons) {
                display: none;
            }
        }
    }

    .exchange_list {
        margin-top: 20rpx;
        min-height: 100rpx;
        background: $uni-bg-color;
        padding: 0rpx 30rpx;

        .exchange_item {
            display: flex;
            flex-direction: column;
            border-bottom: 1rpx solid $uni-border-color;

            .title {
                padding: 0;
                padding-top: 30rpx;
                font-weight: 600;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
            }

            .text {
                padding: 20rpx 0rpx 30rpx 0rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #666666;
                line-height: 40rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .commodity {
                    display: flex;
                    align-items: center;
                    padding: 0;

                    .label {
                        padding: 0rpx 4rpx;
                        text-align: center;
                        height: 36rpx;
                        background: #ff6868;
                        border-radius: 6rpx;
                        font-weight: 500;
                        font-size: 20rpx;
                        color: $uni-bg-color;
                        line-height: 36rpx;
                        margin-right: 4rpx;
                    }

                    .commodity_name {
                        padding: 0;
                    }
                }

                .score {
                    font-weight: 600;
                    font-size: 30rpx;
                    color: var(--primary-color);
                    line-height: 42rpx;
                }
            }
        }
    }
}
</style>
<style lang="scss">
/* #ifdef MP-WEIXIN */
.my_exchange {
    :deep(.uni-date) {
        flex: none !important;
        width: auto !important;
    }
    :deep(.uni-date-x) {
        flex: none !important;
        /* justify-content: flex-end !important; */
    }
}
/* #endif */
</style>
