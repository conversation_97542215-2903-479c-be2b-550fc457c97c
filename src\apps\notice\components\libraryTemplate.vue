<template>
    <view class="library_template">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="state.title" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>

        <view class="library_template_content">
            <div class="demo-uni-row">
                <uni-row>
                    <uni-col :span="12" v-for="item in subsectionList" :key="item.key">
                        <text @click="subsectionChange(item.key)" class="demo-uni-col" :class="{ active: item.key == state.current }">{{ item.name }}</text>
                    </uni-col>
                </uni-row>
            </div>

            <uni-card :is-shadow="false" is-full v-for="item in state.templateList" :key="item.id" extra="额外信息">
                <template v-slot:title>
                    <uni-list>
                        <uni-list-item :title="item.name" rightText="查看更多" link @click="handlerMoreTemplates(item)" />
                    </uni-list>
                </template>
                <uni-row :gutter="[24]">
                    <uni-col :span="12" v-for="it in item.children" :key="it.key">
                        <view @click="copyAnnouncement(it)">
                            <image class="list-item-img" :src="it.coverImg"> </image>
                            <text class="list-item-text">{{ it.title }}</text>
                        </view>
                    </uni-col>
                </uni-row>
            </uni-card>
        </view>
    </view>
</template>

<script setup>
import { nextTick } from "vue"

const eventChannel = getCurrentInstance().proxy.getOpenerEventChannel()

const subsectionList = [
    { name: "图库", key: 1 },
    { name: "模版", key: 2 }
]
const state = reactive({
    title: "发布模板",
    current: 1,
    templateList: []
})
const subsectionChange = (index) => {
    state.current = index
    initTemplate()
}
const initTemplate = () => {
    const params = {
        messTypeId: state.current,
        messType: 1,
        limit: 2
    }
    http.post("/app/mobile/mess/template/messLableTemplateLimit", params).then(({ data }) => {
        state.templateList = data
    })
}
// 更多模板
const handlerMoreTemplates = ({ name, id }) => {
    navigateTo({
        url: "/apps/notice/components/moreTemplates",
        query: { title: name, id }
    })
}

const clickLeft = () => {
    uni.navigateBack()
}

// 模版
const copyAnnouncement = (item) => {
    const { title, coverImg, id, messTypeId } = item
    navigateTo({
        url: messTypeId == 1 ? "/apps/notice/announcement/create" : "/apps/notice/native/index",
        query: {
            title,
            isTemplate: true,
            coverImg,
            tempId: id,
            contentType: messTypeId
        }
    })
}
onLoad(() => {
    nextTick(() => {
        eventChannel.emitCache?.forEach((v) => {
            if (v.eventName == "libraryTemplate") {
                state.title = `发布${v.args[0].title}`
            }
        })
        initTemplate()
    })
})
</script>

<style lang="scss" scoped>
.library_template {
    background-color: $uni-bg-color-grey;
    height: 100vh;
    overflow: hidden auto;

    .library_template_content {
        .demo-uni-row {
            background-color: $uni-bg-color;
            margin: 20rpx 0;
            height: 80rpx;
            line-height: 80rpx;
            font-size: 26rpx;

            :deep(.uni-col) {
                text-align: center;
            }
        }

        .demo-uni-col {
            padding-bottom: 20rpx;
            text-align: center;

            &.active {
                color: var(--primary-color);
                border-bottom: 2px solid var(--primary-color);
            }
        }

        .list-item-img {
            width: 334rpx;
            height: 222rpx;
            border-radius: 8rpx;
        }

        .list-item-text {
            font-size: 26rpx;
            color: #666666ff;
        }
    }
}
</style>
