<template>
    <view class="attendance_weekly">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" title="考勤周报" :border="false" @clickLeft="clickLeft" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <!-- 筛选条件 -->
        <select-filter ref="filterRef" @changeDate="changeDate">
            <template #left>
                <!-- 选择班级 -->
                <view class="select_classes" v-if="identityType == 'teacher'">
                    <uni-data-picker v-model="classesId" :localdata="classList" @change="onChangeClass" :map="{ text: 'showName', value: 'id' }" v-slot:default="{ data, error }" popup-title="请选择班级">
                        <view v-if="error" class="error">
                            <text>{{ error }}</text>
                        </view>
                        <view v-else-if="data.length" class="selected">
                            {{ data[data.length - 1]?.text }}
                        </view>
                        <view v-else>
                            <text>请选择</text>
                        </view>
                    </uni-data-picker>
                </view>
                <!-- 选择学生 -->
                <view class="select_student" @click="selectStudentRef.open()"> {{ studentObj.studentName || "选择学生" }} </view>
            </template>
        </select-filter>
        <view class="content_box">
            <!-- 小结 -->
            <view class="page_error">
                <text class="date_picker_text"> {{ queryDate.startDate }} 至 {{ queryDate.endDate }} 小结</text>
                <view class="error">
                    <image class="error_icon" src="@nginx/workbench/weeklyPublication/weekly_num.png" mode="scaleToFill" />
                    <text>
                        本班学生考勤共
                        <text class="num">{{ info.error || 0 }}</text>
                        次异常
                    </text>
                </view>
            </view>
            <!-- 学生-考勤统计 -->
            <view class="statistics student_statistics" v-if="identityType != 'teacher'">
                <view class="title">考勤统计</view>
                <student-attendance :data="info" :queryDate="queryDate" ref="studentAttendanceRef" />
            </view>

            <!-- 老师-考勤统计卡片 -->
            <view class="statistics" v-if="identityType == 'teacher'">
                <view class="title">考勤统计</view>
                <attendance-card :info="info" />
            </view>

            <!-- 老师-考勤类型图表 -->
            <type-charts :data="info" v-if="identityType == 'teacher'" />
        </view>
        <!-- 学生列表 -->
        <yd-select-popup v-if="identityType == '/apps/traffic/index'" ref="selectStudentRef" title="请选择学生" :list="studentList" @closePopup="closeStudent" :fieldNames="{ value: 'studentId', label: 'studentName' }" :selectId="[studentObj.studentId]" />

        <!-- 引导组件 -->
        <yd-guidance ref="guide" :step="steps"></yd-guidance>

        <!-- 提示弹框 -->
        <view class="confirm_popup">
            <yd-popup ref="confirmRef" :titleflag="false" @confirm="dialogConfirm" confirmText="我知道了" :canceFlag="false">
                <view class="welcome">
                    <image src="@nginx/workbench/weeklyPublication/welcome_img.png" class="welcome_img" mode="widthFix" />
                    <view class="tip_box">
                        <text class="tip_title">欢迎使用周报</text>
                        <text class="tip_content">以周为纬度查看你权限下所有班级学生的出入校及课堂考勤率，及时掌握学生考勤总体情况</text>
                    </view>
                </view>
            </yd-popup>
        </view>
    </view>
</template>

<script setup>
import StudentAttendance from "../components/studentAttendance.vue"
import TypeCharts from "../components/typeCharts.vue"
import AttendanceCard from "../components/attendanceCard.vue"
import SelectFilter from "../components/selectFilter.vue"
import useStore from "@/store"

import { getLastChildId, identityType } from "../data"

const { user, system } = useStore()
const guide = ref(null)
const confirmRef = ref(null)
const filterRef = ref(null)
const studentAttendanceRef = ref(null)
const selectStudentRef = ref(null)
const classList = ref([])
const classesId = ref("")
const studentList = ref([])
const studentObj = ref({})
const queryDate = ref({})
const info = ref({})

const identityQuery = computed(() => {
    return identityType.value == "teacher" ? { classesId: classesId.value } : { studentId: studentObj.value?.studentId || null }
})

const steps = {
    name: "weeklyGuide",
    guideList: [
        {
            el: ".filter_left",
            tips: `在这里可切换${identityType.value == "teacher" ? "班级" : "学生"}`,
            content: `${identityType.value == "teacher" ? "您权限下所有班级都在这里" : "您在校的所有孩子都在这里"}`,
            next: "下一步"
        },
        {
            el: ".date_picker",
            tips: "在这里可切换选择日期",
            content: "需选择起始时间和结束时间",
            next: "我知道了"
        }
    ]
}

async function getInfo() {
    const query = { ...queryDate.value, ...identityQuery.value }
    await http.post("/app/attendance/studentAttendance", query).then((res) => {
        res.data.inOutData.errorCount = 0
        res.data.classroomData.errorCount = 0
        for (const i in res.data.inOutData) {
            res.data.inOutData.errorCount += res.data.inOutData[i]
        }
        for (const i in res.data.classroomData) {
            res.data.classroomData.errorCount += res.data.classroomData[i]
        }
        info.value = res.data
    })
}

// 点击选择班级
function onChangeClass(res) {
    const selectList = res.detail.value
    classesId.value = selectList[selectList.length - 1].value
    getInfo()
}

function changeDate(date) {
    queryDate.value = date
    getInfo()
}
// 选择学生
function closeStudent(val) {
    if (!val && val.studentId == studentObj.value.studentId) return
    studentObj.value = val
    getInfo()
}
// 获取班级
async function getClassList() {
    await http.get("/app/timetable/getClassList").then(async (res) => {
        classList.value = res.data
        if (res.data && res.data.length) {
            classesId.value = await getLastChildId(res.data[0])
        }
    })
}

const clickLeft = () => {
    uni.navigateBack()
}

function showMyGuide() {
    // 打开引导页之前把缓存去掉
    uni.removeStorage({
        key: "weeklyGuide",
        success: function (res) {
            guide.value.showGuide = true
        }
    })
}

function dialogConfirm() {
    system.setAppData({ sys: "weeklyPublication", data: { guide: "hide" } })
    showMyGuide()
}

onMounted(async () => {
    guide.value.showGuide = false
    if (identityType.value == "teacher") {
        await getClassList()
    } else if (identityType.value == "eltern") {
        studentList.value = user.studentInfo || []
        studentObj.value = user.studentInfo[0] || {}
    }
    queryDate.value = await filterRef.value?.getDate()
    await getInfo()
    if (identityType.value != "teacher") {
        nextTick(() => {
            studentAttendanceRef.value?.setData()
        })
    }
    if (!system.apps?.weeklyPublication?.guide) {
        confirmRef.value?.open()
    }
})
</script>

<style lang="scss" scoped>
.attendance_weekly {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .select_classes {
        :deep(.selected) {
            display: flex;
        }
    }
    .content_box {
        padding: 30rpx;
        .page_error {
            .date_picker_text {
                font-weight: 500;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
            }
            .error {
                margin-top: 20rpx;
                padding: 30rpx 24rpx;
                background: #ffffff;
                box-shadow: 0rpx 8rpx 8rpx 0rpx #dcf5ee80;
                border-radius: 20rpx;
                display: flex;
                align-items: center;

                font-weight: 400;
                font-size: 26rpx;
                color: #1e1e1e;
                line-height: 36rpx;
                .num {
                    color: var(--primary-color);
                }
                .error_icon {
                    margin-right: 16rpx;
                    width: 36rpx;
                    height: 36rpx;
                }
            }
        }

        .statistics {
            padding-top: 40rpx;
            .title {
                padding-bottom: 20rpx;
                font-weight: 500;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
            }
        }
    }
    .confirm_popup {
        :deep(.confirm_popup_content) {
            padding: 0rpx !important;
        }
    }
    .welcome {
        .welcome_img {
            width: 80vw;
        }
        .tip_box {
            display: flex;
            flex-direction: column;
            padding: 40rpx;
            justify-content: center;
            align-items: center;
            .tip_title {
                font-weight: 500;
                font-size: 34rpx;
                color: #333333;
                line-height: 48rpx;
            }
            .tip_content {
                margin-top: 30rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #999999;
                line-height: 40rpx;
            }
        }
    }
    :deep(.guide-step-tips) {
        background: #fff !important;
        .text {
            font-weight: 500;
            font-size: 34rpx;
            color: #333333;
            line-height: 48rpx;
        }
        .tool-btn {
            font-weight: 400;
            font-size: 28rpx;
            color: #999999;
            line-height: 40rpx;
            .next {
                color: var(--primary-color) !important;
            }
        }
    }
    :deep(.arrow) {
        background: #fff !important;
    }
    :deep(.guide-box) {
        padding: 0 4rpx;
    }
    :deep(.guide-box::before) {
        border: none !important;
    }
}
</style>
