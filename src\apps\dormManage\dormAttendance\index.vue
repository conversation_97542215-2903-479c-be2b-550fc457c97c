<template>
    <div class="dorm_attendance">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
            <view class="top_nav_bar" @click="openPopup('selPopX')">
                <view class="top_text">{{ dormSource.buildingName }}</view>
                <image class="image" src="https://alicdn.1d1j.cn/announcement/20230726/20daa8cd92c245cda0c06f648bc9e6f8.png" alt="" />
            </view>
        </uni-nav-bar>
        <div class="head">
            <RadioGroup :list="dormSource.list" v-model:value="dormSource.value" @handleClick="selectClick" />
            <div class="right">
                <span class="text">
                    <picker mode="date" :value="dormSource.time" @change="timeChange" v-if="dormSource.value === 1">
                        <span>{{ dormSource.timeValue === dayjs().format("YYYY.MM.DD") ? "今天" : dormSource.timeValue }}</span>
                    </picker>
                    <span v-else @click="openPopup('selectPopupRef')">{{ dormSource.timeValue }}</span>
                </span>
                <image class="image" src="https://alicdn.1d1j.cn/announcement/20230726/20daa8cd92c245cda0c06f648bc9e6f8.png" alt="" />
            </div>
        </div>
        <div class="count" :class="dormSource.value === 1 ? 'minHeight' : ''">
            <div class="select">
                <div class="left" v-if="dormSource.dormName">
                    <span class="text" @click="openPopup('selPopO')">{{ dormSource.dormName }}</span>
                    <img class="image" src="https://alicdn.1d1j.cn/announcement/20230726/20daa8cd92c245cda0c06f648bc9e6f8.png" alt="" />
                </div>
                <div class="right" v-if="dormSource.attendName">
                    <span class="text" @click="openPopup('selPopT')">{{ dormSource.attendName }}</span>
                    <img class="image" src="https://alicdn.1d1j.cn/announcement/20230726/20daa8cd92c245cda0c06f648bc9e6f8.png" alt="" />
                </div>
            </div>
            <div class="tubiao">
                <Swiper :swiperData="dormSource.swiperData" v-if="dormSource.value === 1"></Swiper>
                <div class="status_count" v-else>
                    <div class="item" v-for="(item, index) in statusList" :key="index" :style="`${'color: ' + color[item.idx] + ';background:' + background[item.idx]}`">
                        <span class="frequency">{{ dormSource.swiperData[item.status] }}</span>
                        <span>{{ text[item.idx] }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="chamber">
            <div class="query" v-if="dormSource.floorName">
                <span class="text" @click="openPopup('selPopF')">{{ dormSource.floorName }}</span>
                <img class="image" src="https://alicdn.1d1j.cn/announcement/20230726/20daa8cd92c245cda0c06f648bc9e6f8.png" alt="" />
            </div>
            <div class="chamber_list">
                <uni-list-item v-for="(item, index) in dataList" :key="index" clickable @click="toDetail(item)">
                    <template v-slot:header>
                        <div class="chamber_main">
                            <div class="left_text">寝室号：{{ item.roomNum }} ({{ item.peppleNum }}人间)</div>
                            <span v-if="item.absenteeismNum" class="text_tag">{{ item.absenteeismNum }}次未归寝</span>
                        </div>
                    </template>
                    <template v-slot:footer>
                        <uni-icons type="forward" size="16" color="#000000" />
                    </template>
                </uni-list-item>
                <uni-load-more iconType="auto" :status="status" />
            </div>
        </div>
        <yd-select-popup ref="selectPopupRef" :title="dormSource.value === 2 ? '选择周次' : '选择月次'" :list="timeList" @closePopup="closePopup" :fieldNames="{ value: 'id', label: 'name' }" />
        <yd-select-popup ref="selPopO" title="宿舍考勤" :list="dormSource.selectListO" @closePopup="(val) => seleClosePop(val, 1)" :fieldNames="{ value: 'id', label: 'name' }" />
        <yd-select-popup ref="selPopT" title="选择考勤类型" :list="dormSource.selectListT" @closePopup="(val) => seleClosePop(val, 2)" :fieldNames="{ value: 'id', label: 'name' }" />
        <yd-select-popup ref="selPopF" title="选择楼层" :list="dormSource.selectListF" @closePopup="(val) => seleClosePop(val, 3)" :fieldNames="{ value: 'id', label: 'name' }" />
        <yd-select-popup ref="selPopX" title="选择楼栋" :list="dormSource.selectListX" @closePopup="(val) => seleClosePop(val, 4)" :fieldNames="{ value: 'id', label: 'name' }" />
    </div>
</template>

<script setup>
import RadioGroup from "../components/radioGroup.vue"
import Swiper from "../components/swiper.vue"
import dayjs from "dayjs"
import useQueryList from "../hooks/useQueryList.js"
import { onReachBottom, onLoad } from "@dcloudio/uni-app"
import { getMonthWeekList, getWeekOfMonth, getMonthSAndE } from "@/utils/getDate"

const { weekOfMonth } = getWeekOfMonth()
const weekList = getMonthWeekList()

const text = ["晚归/次", "请假/次", "未归寝/次", "未打卡/次"]
const color = ["#FC941F", "#3896EA", "#F5222D", "#5534F0"]
const background = ["#FFF4E9", "#E9F5FF", "#FFEBEC", "#EBE7FF"]

const { getList, dataList, pageNo, status } = useQueryList()

let dormSource = reactive({
    list: [
        { name: "日", value: 1 },
        { name: "周", value: 2 },
        { name: "月", value: 3 }
    ],
    swiperData: {},
    value: 1,
    startDate: dayjs().format("YYYY-MM-DD"),
    endDate: dayjs().format("YYYY-MM-DD"),
    timeValue: dayjs().format("YYYY.MM.DD"),
    time: dayjs().format("YYYY.MM.DD"),
    attendanceTimeId: "",
    buildingId: "",
    buildingName: "",
    floor: "",
    dormName: "",
    attendName: "",
    floorName: "全部楼层",
    selectListO: [],
    selectListT: [],
    selectListF: [],
    selectListX: [],
    allSelect: []
})

onLoad(async (params) => {
    Object.assign(dormSource, params)
    dormSource.buildingId = params.buildingId || ""
    await getBuildList()

    const data = dormSource.selectListX.find((i) => i.id == dormSource.buildingId)
    dormSource.selectListF = [{ name: "全部楼层", value: "" }].concat(data.children.map((i) => ({ name: `${i.name}层`, value: i.id })))
    getEventList()
})

const statusList = [
    {
        status: "beLateNum",
        idx: 0
    },
    {
        status: "leaveNum",
        idx: 1
    },
    {
        status: "absenteeismNum",
        idx: 2
    },
    {
        status: "notSignInNum",
        idx: 3
    }
]

let timeList = computed(() => {
    let arr = []
    if (dormSource.value === 2) {
        arr = getMonthWeekList()
    } else if (dormSource.value === 3) {
        arr = getMonthSAndE()
    }
    return ref(arr).value
})

// 获取宿舍列表
const getPage = (params) => {
    return http.post("/app/dormitory/attendance/manage/dormitoryDataPage", params)
}

// 获取楼栋
const getBuildList = () => {
    return new Promise(async (resolve, reject) => {
        const { data } = await http.get("/app/dormitory/buildingCommon/tree", {})
        dormSource.selectListX = data || []
        dormSource.buildingId = dormSource.selectListX[0].id
        dormSource.buildingName = dormSource.selectListX[0].name
        resolve(data)
    })
}

// 获取考勤统计
const getDateStatistics = async () => {
    const params = {
        buildingId: dormSource.buildingId,
        startDate: dormSource.startDate,
        endDate: dormSource.endDate,
        attendanceTimeId: dormSource.attendanceTimeId
    }
    const { data = {} } = await http.post("/app/dormitory/attendance/manage/dateStatistics", params)
    dormSource.swiperData = data
}

// 获取考勤事件
const getEventList = async () => {
    dormSource.attendanceTimeId = ""
    const params = {
        buildingId: dormSource.buildingId,
        startDate: dormSource.startDate,
        endDate: dormSource.endDate,
        floor: dormSource.floor
    }
    const { data } = await http.post("/app/dormitory/attendance/manage/getEventList", params)
    dormSource.allSelect = data
    dormSource.selectListO = data.map((i, index) => ({
        ...i,
        name: i.attendanceName,
        index,
        value: i.attendanceId
    }))
    dormSource.selectListT =
        (data[0] &&
            data[0].timeList.map((i) => ({
                ...i,
                name: i.attendanceTimeName,
                value: i.attendanceTimeId
            }))) ||
        []
    dormSource.attendanceTimeId = (dormSource.selectListT[0] && dormSource.selectListT[0].attendanceTimeId) || ""
    dormSource.dormName = (dormSource.selectListO[0] && dormSource.selectListO[0].name) || ""
    dormSource.attendName = (dormSource.selectListT[0] && dormSource.selectListT[0].name) || ""
    if (!dormSource.attendanceTimeId) return
    getDateStatistics()
    getList(getPage, {
        ...params,
        attendanceTimeId: dormSource.attendanceTimeId
    })
}

// 按钮组切换
function selectClick(item) {
    if (item.value === 1) {
        dormSource.timeValue = dayjs().format("YYYY.MM.DD")
        dormSource.time = dayjs().format("YYYY-MM-DD")
        dormSource.startDate = dayjs().format("YYYY-MM-DD")
        dormSource.endDate = dayjs().format("YYYY-MM-DD")
    } else if (item.value === 2) {
        changeTimeVal(weekList[weekOfMonth - 1])
    } else {
        dormSource.timeValue = dayjs().format("YYYY.MM")
        dormSource.startDate = dayjs().startOf("month").format("YYYY-MM-DD")
        dormSource.endDate = dayjs().endOf("month").format("YYYY-MM-DD")
    }
    pageNo.value = 1
    dataList.value = []
    getEventList()
}

// 时间展示处理
function changeTimeVal(timeObj) {
    let startTime = dayjs(timeObj.startDate).format("YYYY.MM.DD")
    let endTime = dayjs(timeObj.endDate).format("YYYY.MM.DD").slice(5)
    dormSource.timeValue = `${startTime + "-" + endTime}` + " " + timeObj.name
    dormSource.startDate = timeObj.startDate
    dormSource.endDate = timeObj.endDate
}

// 时间选择框
const timeChange = (val) => {
    dormSource.timeValue = dayjs(val.detail.value).format("YYYY.MM.DD")
    dormSource.time = val.detail.value
    dormSource.startDate = val.detail.value
    dormSource.endDate = val.detail.value
    getEventList()
    dormSource.floor = ""
    dormSource.floorName = "全部楼层"
}

// 选择底部弹窗打开事件
const selectPopupRef = ref(null)
const selPopO = ref(null)
const selPopT = ref(null)
const selPopF = ref(null)
const selPopX = ref(null)
const popup = {
    selectPopupRef,
    selPopO,
    selPopT,
    selPopF,
    selPopX
}
const openPopup = (val) => {
    popup[val].value.open()
}

// 时间选择关闭弹窗
const closePopup = (val) => {
    if (!val) return
    if (dormSource.value === 2) {
        changeTimeVal(val)
    } else {
        dormSource.timeValue = dayjs(val.startDate).format("YYYY.MM")
        dormSource.startDate = val.startDate
        dormSource.endDate = val.endDate
    }
    pageNo.value = 1
    dataList.value = []
    getEventList()
    dormSource.floor = ""
    dormSource.floorName = "全部楼层"
}

const seleClosePop = (val, flag) => {
    if (!val) return
    pageNo.value = 1
    dataList.value = []
    switch (flag) {
        case 1:
            dormSource.selectListT = dormSource.allSelect[val.index].timeList.map((i) => ({
                ...i,
                name: i.attendanceTimeName,
                value: i.attendanceTimeId
            }))
            dormSource.attendanceTimeId = dormSource.selectListT[0].attendanceTimeId || ""
            dormSource.dormName = val.name
            break
        case 2:
            dormSource.attendanceTimeId = val.attendanceTimeId
            dormSource.attendName = val.name
            break
        case 3:
            dormSource.floor = val.value
            dormSource.floorName = val.name
            break
        case 4:
            dormSource.selectListF = [{ name: "全部楼层", value: "" }].concat(val.children.map((i) => ({ name: `${i.name}层`, value: i.id })))
            dormSource.buildingName = val.name
            dormSource.buildingId = val.id
            break
    }
    if (flag === 4) {
        getEventList()
    } else {
        const params = {
            buildingId: dormSource.buildingId,
            startDate: dormSource.startDate,
            endDate: dormSource.endDate,
            attendanceTimeId: dormSource.attendanceTimeId,
            floor: dormSource.floor
        }
        if (!dormSource.attendanceTimeId) return
        getDateStatistics()
        getList(getPage, params)
    }
}

const toDetail = (item) => {
    navigateTo({
        url: "/apps/dormManage/dormAttendance/info",
        query: item
    })
}

onReachBottom(() => {
    const params = {
        buildingId: dormSource.buildingId,
        startDate: dormSource.startDate,
        endDate: dormSource.endDate,
        attendanceTimeId: dormSource.attendanceTimeId,
        floor: dormSource.floor
    }
    getList(getPage, params)
})

const back = () => {
    uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.dorm_attendance {
    min-height: 94vh;
    background: $uni-bg-color-grey;
    .top_nav_bar {
        flex: 1;
        display: flex;
        text-align: center;
        font-size: 32rpx;
        font-weight: 600;
        align-items: center;
        justify-content: center;
        .image {
            height: 28rpx;
            width: 28rpx;
        }
    }
    :deep(.uni-navbar__content) {
        border-bottom-color: transparent !important;
    }
    .head {
        background-color: #ffffff;
        color: #333333;
        height: 92rpx;
        border-bottom: 1rpx solid #d8d8d8;
        border-top: 1rpx solid #d8d8d8;
        padding: 0rpx 30rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .right {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            .text {
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
            }
            .image {
                height: 28rpx;
                width: 28rpx;
            }
        }
    }

    .count {
        padding: 28rpx 15rpx;
        background-color: #ffffff;

        .select {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .left,
            .right {
                display: flex;
                align-items: center;
                .text {
                    font-size: 28rpx;
                    font-family:
                        PingFangSC-Regular,
                        PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                }
                .image {
                    height: 28rpx;
                    width: 28rpx;
                }
            }
        }
        .tubiao {
            .status_count {
                margin-top: 30rpx;
                display: flex;
                justify-content: space-around;
                .item {
                    width: 160rpx;
                    height: 164rpx;
                    background: #e3faf3;
                    border-radius: 20rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                    line-height: 40rpx;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    .frequency {
                        font-size: 40rpx;
                        line-height: 58rpx;
                        margin-bottom: 16rpx;
                    }
                }
            }
        }
    }
    .minHeight {
        /* #ifdef MP-WEIXIN */
        min-height: 420rpx;
        /* #endif */
    }
    .chamber {
        background-color: #ffffff;
        margin-top: 20rpx;
        min-height: 100rpx;
        .query {
            min-height: 100rpx;
            display: flex;
            align-items: center;
            padding: 0rpx 30rpx;
            .text {
                font-size: 28rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
            }
            .image {
                height: 28rpx;
                width: 28rpx;
            }
        }
        .chamber_list {
            padding: 0rpx 30rpx;
            :deep(.uni-list-item__container) {
                padding: 30rpx 0rpx;
            }
            .chamber_main {
                display: flex;
                align-items: center;
                .left_text {
                    color: #333333;
                    font-size: 28rpx;
                    font-weight: 600;
                }
                .text_tag {
                    display: inline-block;
                    background-color: #ffebec;
                    color: #f5222d;
                    border: 1rpx solid #f5222d;
                    font-size: 24rpx;
                    border-radius: 6rpx;
                    padding: 4rpx 16rpx;
                    margin-left: 16rpx;
                }
            }
        }
    }
}
</style>

<style lang="scss">
/* #ifdef H5 */
:deep(.circular-container) {
    margin: auto;
}
:deep(.yd_circular_box) {
    z-index: 999;
    width: 480rpx;
    height: 150rpx;
    position: absolute;
    bottom: 0rpx;
    left: 0rpx;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    flex-direction: column;
    .top_text {
        position: relative;
        text-align: right;
        width: 250rpx;
        padding-right: 30rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        margin-bottom: 20rpx;
        &::after {
            content: "";
            width: 0;
            height: 0;
            display: inline-block;
            border: 14rpx solid transparent;
            border-top-color: #4566d5;
            position: absolute;
            right: 0rpx;
            top: 15rpx;
        }
    }
    .left {
        color: #4566d5;
    }
}
/* #endif */

.circular-container {
    margin: auto;
}
.yd_circular_box {
    z-index: 999;
    width: 480rpx;
    height: 150rpx;
    position: absolute;
    bottom: 0rpx;
    left: 0rpx;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    flex-direction: column;
    .top_text {
        position: relative;
        text-align: right;
        width: 250rpx;
        padding-right: 30rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        margin-bottom: 20rpx;
        &::after {
            content: "";
            width: 0;
            height: 0;
            display: inline-block;
            border: 14rpx solid transparent;
            border-top-color: #4566d5;
            position: absolute;
            right: 0rpx;
            top: 15rpx;
        }
    }
    .left {
        color: #4566d5;
    }
}
</style>
