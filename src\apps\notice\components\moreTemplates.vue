<template>
    <view class="more_template">
        <z-paging ref="paging" v-model="state.templateList" @query="initTemplate">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="state.title" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
                <view class="more_template_content">
                    <uni-easyinput class="search-easyinput" :inputBorder="false" v-model="state.form.keywords" both trim prefixIcon="search" placeholder="请输入要搜索的内容" @change="hadlerEasyinput" @clear="hadlerEasyinput"></uni-easyinput>
                </view>
            </template>
            <uni-card :is-shadow="false" is-full>
                <uni-row :gutter="[24]">
                    <uni-col :span="12" v-for="item in state.templateList" :key="item.id">
                        <view @click="copyAnnouncement(item)">
                            <image class="list-item-img" :src="item.coverImg"> </image>
                            <text class="list-item-text">{{ item.title }}</text>
                        </view>
                    </uni-col>
                </uni-row>
            </uni-card>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
const state = reactive({
    title: "发布模板",
    templateList: [],
    form: {
        messLableId: "",
        keywords: ""
    }
})
const paging = ref(null)

const initTemplate = (pageNo, pageSize) => {
    const params = {
        ...state.form,
        pageNo,
        pageSize
    }
    http.post("/app/mobile/mess/template/page", params).then(({ data }) => {
        paging.value.complete(data.list)
    })
}
const hadlerEasyinput = () => {
    paging.value?.reload()
}

const clickLeft = () => {
    uni.navigateBack()
}

// 复制模版
const copyAnnouncement = (item) => {
    const { title, coverImg } = item
    navigateTo({
        url: `/apps/notice/announcement/create`,
        query: {
            title,
            isTemplate: true,
            coverImg
        }
    })
}
onLoad((item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    const { title, id } = item
    state.title = title
    state.form.messLableId = id
    paging.value?.reload()
})
</script>

<style lang="scss" scoped>
.more_template {
    background-color: $uni-bg-color-grey;
    min-height: 100vh;

    .more_template_content {
        .search-easyinput {
            padding: 20rpx 0;
            background-color: $uni-bg-color;

            :deep(.uni-easyinput__content) {
                width: 90%;
                margin: 0 auto;
                border-radius: 30rpx;
                background-color: #f9faf9ff !important;
            }
        }
    }

    :deep(.uni-col) {
        .list-item-img {
            width: 334rpx;
            height: 222rpx;
            border-radius: 8rpx;
        }

        .list-item-text {
            font-size: 26rpx;
            color: #666666ff;
        }
    }
}
</style>
