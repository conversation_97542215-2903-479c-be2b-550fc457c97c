/*
 * @Description:
 * @Date: 2024-11-08 16:19:06
 * @LastEditors: lhq
 * @LastEditTime: 2024-11-12 18:47:15
 * @FilePath: \code\cloud-mobile\src\main.js
 */
import { createSSRApp } from "vue"
import App from "./App.vue"
import * as <PERSON><PERSON> from "pinia"
import { createPersistedState } from "pinia-plugin-persistedstate"
import "@/styles/index.scss"
import "@/styles/font.scss"

import useStore from "@/store"
if (!Array.prototype.at) {
	Array.prototype.at = function (index) {
		index = Math.trunc(index) || 0
		if (index < 0) index += this.length
		if (index < 0 || index >= this.length) return undefined
		return this[index]
	}
}
export function createApp() {
	const app = createSSRApp(App)
	const pinia = Pinia.createPinia()
	pinia.use(
		createPersistedState({
			storage: {
				getItem: (key) => {
					return uni.getStorageSync(key)
				},
				setItem: (key, value) => {
					uni.setStorageSync(key, value)
				}
			}
		})
	)
	app.use(pinia)

	function updateRole(el, binding) {
		const { user } = useStore()
		el.style.display = user.identityInfo.roleCode === binding.value ? "block" : "none"
	}
	app.directive("role", {
		mounted(el, binding) {
			updateRole(el, binding)
		},
		updated(el, binding) {
			updateRole(el, binding)
		}
	})

	return {
		app
	}
}
