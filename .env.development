VITE_APP_NAME = "dev"

# 静态文件路径
VITE_BASE_STATIC = https://file.1d1j.cn/cloud-mobile

# 预览查看文件
VITE_BASE_FILE_PREVIEW = https://crmfileview.yyide.com

# 图书馆前端域名（library-miniprogram）
# VITE_BASE_LIBRARY = http://*************:8036
VITE_BASE_LIBRARY = https://library-8036.yyide.vip
# VITE_BASE_LIBRARY = http://************:5173

# uniapp前端H5域名（yide-applet）
# VITE_BASE_APPLET = http://************:5174
VITE_BASE_APPLET = http://*************:8013

# OA审批前端域名（oa-h5）
VITE_BASE_OAH5 = https://158-8889.yyide.vip
# VITE_BASE_OAH5 = https://approveh5uat.yyide.com
# VITE_BASE_OAH5 = http://************:8889


# 公众号前端域名（web-app）
VITE_BASE_WEBAPP = https://158-8009.yyide.vip
# VITE_BASE_WEBAPP = https://mclouduat.yyide.com
# VITE_BASE_WEBAPP = https://mcloud.yyide.com


# 测试环境
VITE_BASE_API = https://test-158-9528.yyide.vip  # 穿透域名

# VITE_BASE_API =  https://local-api.1yide.com
VITE_BASE_API = https://apiuat.1yide.com
# VITE_BASE_API = http://*************:9528
# 赖
# VITE_BASE_API =   http://************:9528

# 分享地址
VITE_BASE_SHARE = http://*************:8035

# 预发布环境
# VITE_BASE_API =  https://apiuat.yyide.com

# 正式环境
# VITE_BASE_API = https://api.yyide.com

# 本地部署环境
# VITE_BASE_API = http://************:9528

# 预览url
VITE_BASE_PREVIEW = https://crmfileview.yyide.com/onlinePreview

VITE_BASE_PREVIEW = http://************:8012/onlinePreview
