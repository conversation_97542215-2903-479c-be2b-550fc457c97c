/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Demo: typeof import('./src/components/yd-selector/demo.vue')['default']
    PickerColor: typeof import('./src/components/yd-sign-input/pickerColor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    UMask: typeof import('./src/components/yd-sign-input/u-mask/u-mask.vue')['default']
    YdEmpty: typeof import('./src/components/yd-empty/index.vue')['default']
    YdFace: typeof import('./src/components/yd-face/index.vue')['default']
    YdGuidance: typeof import('./src/components/yd-guidance/index.vue')['default']
    YdPageView: typeof import('./src/components/yd-page-view/index.vue')['default']
    YdPopup: typeof import('./src/components/yd-popup/index.vue')['default']
    YdSelector: typeof import('./src/components/yd-selector/index.vue')['default']
    YdSelectPopup: typeof import('./src/components/yd-select-popup/index.vue')['default']
    YdSignInput: typeof import('./src/components/yd-sign-input/index.vue')['default']
    YdSteps: typeof import('./src/components/yd-steps/index.vue')['default']
    YdTabBar: typeof import('./src/components/yd-tabBar/index.vue')['default']
    YdTinymce: typeof import('./src/components/yd-tinymce/index.vue')['default']
  }
}
