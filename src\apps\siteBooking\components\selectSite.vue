<template>
    <!-- 选择场地 -->
    <view>
        <uni-popup ref="selectSitePopup" type="bottom" :is-mask-click="false" :safe-area="false">
            <view class="select_site_page">
                <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false">
                    <template #top>
                        <uni-nav-bar :showMenuButtonWidth="true" :border="false" @clickLeft="close" left-icon="left" title="选择场地" :leftWidth="navBarLeftWidth(100)" :rightWidth="navBarRightWidth(100)">
                            <!-- #ifdef H5 || H5-WEIXIN-->
                            <template #right>
                                <view
                                    class="sift_icon"
                                    :class="{
                                        active_sift_icon: isSift
                                    }"
                                    @click="selectSiftFormRef.open()"
                                >
                                    <image class="sift_image" src="@nginx/workbench/siteBooking/sift_icon.png" mode="scaleToFill" />
                                    <text>筛选</text>
                                </view>
                            </template>
                            <!-- #endif -->
                            <!-- #ifdef APP-PLUS-->
                            <template #right>
                                <view
                                    class="sift_icon"
                                    :class="{
                                        active_sift_icon: isSift
                                    }"
                                    @click="selectSiftFormRef.open()"
                                >
                                    <image class="sift_image" src="@nginx/workbench/siteBooking/sift_icon.png" mode="scaleToFill" />
                                    <text>筛选</text>
                                </view>
                            </template>
                            <!-- #endif -->
                        </uni-nav-bar>
                        <!-- 选择日期范围 -->
                        <view class="date_range">
                            <view class="date_range_list">
                                <view
                                    class="date_range_item"
                                    v-for="(item, index) in weekList"
                                    :key="index"
                                    @click="handleWeekDay(item)"
                                    :class="{
                                        active_class: selectDay.date == item.date
                                    }"
                                >
                                    <text class="week">{{ item.week }}</text>
                                    <text class="date">{{ item.day }}</text>
                                </view>
                            </view>
                            <view class="calendar_icon" @click="selectCalendar">
                                <image mode="aspectFill" src="@nginx/workbench/siteBooking/calendar_icon.png" class="image_calendar" alt="" />
                            </view>
                        </view>
                    </template>
                    <view class="building_list">
                        <view class="building_item" v-for="item in dataList" :key="item.id" @click="selectBuilding(item)">
                            <!-- 场地名称 -->
                            <view class="building_title_box">
                                <text class="title">{{ item.siteName }}</text>
                                <text
                                    class="status"
                                    :style="{
                                        color: statusColor[item.status]
                                    }"
                                    >{{ statusText[item.status] }}</text
                                >
                            </view>
                            <!-- 场地图片信息 -->
                            <view class="building_img_box">
                                <image class="building_img" :src="item.url || '@nginx/workbench/siteBooking/building_def_img.png'" mode="scaleToFill" />
                                <view class="building_info">
                                    <text class="info">
                                        场地类型：
                                        <text class="value">{{ item.siteTypeName }}</text>
                                    </text>
                                    <text class="info">
                                        容纳人数：
                                        <text class="value">{{ item.peppleNum }}人</text>
                                    </text>
                                </view>
                            </view>
                            <!-- 时间段 -->
                            <view class="building_step">
                                <view class="step_box">
                                    <view v-for="(item, index) in item._timeItem" :key="index" class="clock_box">
                                        <view class="num">{{ index + 8 }}</view>
                                        <view
                                            class="item"
                                            v-for="(it2, in2) in item"
                                            :key="in2"
                                            :style="{
                                                background: it2.color == 'red' ? '#FD4F45' : it2.color
                                            }"
                                        ></view>
                                    </view>
                                </view>
                            </view>

                            <!-- 场地校区设备 -->
                            <view class="campus_device">
                                <text class="value">校区：{{ `${item.buildingName}${item.roomNum}` }}</text>
                                <text class="value">设备：{{ item.deviceNames || "-" }}</text>
                            </view>
                        </view>
                    </view>
                    <template #empty>
                        <yd-empty text="暂无数据" />
                    </template>
                </z-paging>
            </view>
        </uni-popup>
        <uv-calendars ref="calendarsRef" @confirm="changeDate" color="var(--primary-color)" confirmColor="var(--primary-color)" />
        <SelectSiftForm ref="selectSiftFormRef" @confirm="confirmSift" />
    </view>
</template>

<script setup>
import SelectSiftForm from "./selectSiftForm.vue"
import dayjs from "dayjs"
import { getWeekStartEnd, getCurrentDayInfo, getDatesInRange, createTimeList, setColoe } from "../data"

const emit = defineEmits(["confirm"])
const paging = ref(null)
const selectSiftFormRef = ref(null)
const calendarsRef = ref(null)
const selectSitePopup = ref(null)
const weekList = ref([])
const selectDay = ref({})
const dataList = ref([])
const siteBookingTypeId = ref(null)

const statusColor = {
    0: "var(--primary-color)",
    1: "#fd4f45",
    2: "#cccccc"
}
const statusText = {
    0: "可预约",
    1: "已约满",
    2: "封禁"
}

const siteForm = ref({})

// 判断是否有筛选条件
const isSift = computed(() => {
    if (Object.keys(siteForm.value).length > 0) {
        const { buildingId, maxPeppleNum, minPeppleNum, siteTypeIds } = siteForm.value
        if (!buildingId && !maxPeppleNum && !minPeppleNum && siteTypeIds.length <= 0) {
            return false
        }
        return true
    }
    return false
})

function queryList(pageNo, pageSize) {
    const parame = {
        pageNo,
        pageSize,
        endTime: selectDay.value.date,
        startTime: selectDay.value.date,
        siteBookingTypeId: siteBookingTypeId.value,
        dateType: "2",
        ...siteForm.value
    }
    http.post("/app/siteBooking/pageSchoolSiteBooking", parame).then((res) => {
        initPage(res.data.list)
    })
}

// 初始化数据
function initPage(list) {
    // inApprovalDateList-审批中集合
    // noBookingDateList-不可预约集合
    // timetableDateList-课程集合
    // yesBookingDateList-已预约集合
    const arrData = list.map((item, index) => {
        const { yesBookingDateList, noBookingDateList, timetableDateList } = item.bookingDate || {}
        return {
            ...item,
            _timeItem: createTimeList(),
            _all: [...yesBookingDateList, ...noBookingDateList, ...timetableDateList]
        }
    })
    arrData.forEach((i) => {
        i._timeItem.forEach((j) => {
            i._all.forEach((item) => {
                const { startTime, endTime } = item
                let start = startTime.replace(/-/g, "/")
                let end = endTime.replace(/-/g, "/")
                j.forEach((k) => {
                    if (!k.flag) {
                        k.color = setColoe(start, end, k.h, k.m)
                    }
                    k.flag = k.color == "red" ? true : false
                })
            })
        })
    })
    paging.value.complete(arrData || false)
}

// 选择日期
function handleWeekDay(item) {
    selectDay.value = item
    paging.value.reload()
}

// 打开日历弹框
function selectCalendar() {
    calendarsRef.value.open()
}

// 选择日历弹框的数据
function changeDate(item) {
    if (!item.fulldate) return
    weekList.value = getDatesInRange(getWeekStartEnd(dayjs(item.fulldate)))
    selectDay.value = getCurrentDayInfo(dayjs(item.fulldate))
    paging.value.reload()
}

function confirmSift(form) {
    selectSiftFormRef.value.close()
    siteForm.value = form
    paging.value.reload()
}

function selectBuilding(item) {
    emit("confirm", item.id, item)
}
// 打开选择组件
const open = async (typeId) => {
    selectSitePopup.value.open()
    siteBookingTypeId.value = typeId
    selectDay.value = getCurrentDayInfo(dayjs())
    weekList.value = getDatesInRange(getWeekStartEnd(dayjs()))
    setTimeout(() => {
        paging.value?.reload()
    }, 200)
}

// 关闭弹框
const close = () => {
    selectSitePopup.value.close()
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.select_site_page {
    height: 100vh;
    width: 100vw;
    background: $uni-bg-color-grey;

    .sift_icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
        text-align: center;

        .sift_image {
            filter: grayscale(100%);
            width: 40rpx;
            height: 40rpx;
        }
    }

    .active_sift_icon {
        color: var(--primary-color);

        .sift_image {
            filter: none;
        }
    }

    .date_range {
        background: $uni-bg-color;
        padding: 30rpx;
        /* #ifdef MP-WEIXIN */
        padding: calc(var(--status-bar-height) + 60rpx) 30rpx 30rpx 30rpx;
        /* #endif */
        display: flex;
        justify-content: space-between;
        align-items: center;

        .date_range_list {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex: 1;

            .date_range_item {
                width: 76rpx;
                height: 72rpx;
                background: var(--primary-bg-color);
                border-radius: 10rpx;
                border: 2rpx solid #dcf5ee;
                display: flex;
                align-items: center;
                flex-direction: column;
                justify-content: space-between;
                padding: 8rpx 0;
                margin-right: 12rpx;

                .week {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #333333;
                    line-height: 40rpx;
                }

                .date {
                    font-weight: 500;
                    font-size: 20rpx;
                    color: #999999;
                    line-height: 28rpx;
                }
            }

            .active_class {
                background: var(--primary-color);
                border-radius: 10rpx;
                border: 1rpx solid var(--primary-color);

                .week,
                .date {
                    color: #ffffff;
                }
            }
        }

        .calendar_icon {
            width: 48rpx;
            height: 48rpx;
            flex: 1;
            .image_calendar {
                width: 48rpx;
                height: 48rpx;
            }
        }
    }

    .building_list {
        padding: 30rpx;

        .building_item {
            background: $uni-bg-color;
            padding: 30rpx;
            margin-bottom: 30rpx;
            box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
            border-radius: 20rpx;

            .building_title_box {
                display: flex;
                justify-content: space-between;
                padding-bottom: 30rpx;
                border-bottom: 1rpx solid $uni-border-color;

                .title {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: #333333;
                    line-height: 42rpx;
                }

                .status {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: var(--primary-color);
                    line-height: 40rpx;
                }
            }

            .building_img_box {
                display: flex;
                padding: 30rpx 0;

                .building_img {
                    width: 180rpx;
                    height: 120rpx;
                    border-radius: 10rpx;
                }

                .building_info {
                    margin-left: 20rpx;
                    display: flex;
                    flex-direction: column;
                    padding: 10rpx 0;
                    justify-content: space-between;

                    .info {
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #999999;
                        line-height: 40rpx;

                        .value {
                            color: $uni-text-color;
                        }
                    }
                }
            }

            .building_step {
                .step_box {
                    display: flex;
                    flex-wrap: wrap;
                    margin: 36rpx 0 10rpx 0;

                    .clock_box {
                        position: relative;
                        box-sizing: border-box;
                        height: 30rpx;
                        width: 70rpx;
                        border: 1rpx solid $uni-border-color;
                        display: flex;

                        &:nth-child(-n + 8) {
                            margin-bottom: 60rpx;
                        }

                        .num {
                            position: absolute;
                            top: -46rpx;
                            font-weight: 400;
                            font-size: 28rpx;
                            color: #999999;
                            line-height: 40rpx;
                        }

                        .item {
                            display: flex;
                            width: 17.5rpx;
                            height: 100%;
                            box-sizing: border-box;

                            &:last-child {
                                border-right: none;
                            }
                        }
                    }
                }
            }

            .campus_device {
                display: flex;
                flex-direction: column;

                .value {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #999999;
                    line-height: 40rpx;
                    margin: 10rpx 0;
                }
            }
        }
    }
}
</style>
