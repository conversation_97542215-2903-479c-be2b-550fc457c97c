<template>
    <view class="container_box">
        <z-paging ref="paging" v-model="state.voteList" @query="queryList" @onRefresh="onRefresh">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="投票统计" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <view class="top_box">
                    <view class="tab_box">
                        <DropDown :menu="menu" @clickItem="clickItem" ref="dropDownRef"> </DropDown>
                    </view>
                </view>
            </template>
            <view class="main_box">
                <view class="vote-item" v-for="item in state.voteList" :key="item.id" @click="handleClick(item)">
                    <!-- 投票活动图片 -->
                    <view class="vote-image-container">
                        <image class="vote-image" :src="item.url || '@nginx/vote/defaultVote.png'" mode="aspectFill" />
                    </view>

                    <!-- 投票活动信息 -->
                    <view class="vote-info">
                        <view class="vote-info_topBox">
                            <!-- 动态状态标签 -->
                            <view class="status-tag" :class="getStatusClass(item.status)">
                                <text class="status-text">{{ getStatusText(item.status) }}</text>
                            </view>
                            <view class="vote-title">{{ item.title }}</view>
                        </view>

                        <!-- uni-countdown倒计时组件 -->
                        <view class="countdown-container" v-if="item.status === 1">
                            <view class="time-icon"> <image src="@nginx/vote/timeicon2.png" mode="aspectFill" /></view>
                            <view class="countdown-wrapper">
                                <text class="countdown-label">距离结束还剩：</text>
                                <Countdown :end-time="item.endTime" :show-day="true" :show-hour="true" :show-minute="true" :show-second="false" number-color="#333" label-color="#333" @timeup="onTimeUp(item)" />
                            </view>
                        </view>
                        <view class="countdown-container" v-else>
                            <view class="countdown-wrapper">
                                <text class="countdown-label">活动时间：</text>
                                <text class="countdown-label">{{ item.startTime.substring(0, 16).replace(/-/g, ".") }}-{{ item.endTime.substring(0, 16).replace(/-/g, ".") }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <template #bottom>
                <view class="footer">
                    <view class="item" @click="goVote">
                        <view>
                            <uni-icons fontFamily="antiBullying" :size="20">{{ "&#xe615;" }}</uni-icons>
                        </view>
                        <view class="view">投票</view>
                    </view>
                    <view class="item active">
                        <view>
                            <uni-icons color="var(--primary-color)" fontFamily="antiBullying" :size="20">{{ "&#xe615;" }}</uni-icons>
                        </view>
                        <view class="view">统计</view>
                    </view>
                    <view class="item" @click="goVoteCreate">
                        <view>
                            <uni-icons fontFamily="antiBullying" :size="20">{{ "&#xe615;" }}</uni-icons>
                        </view>
                        <view class="view">发起</view>
                    </view>
                </view>
            </template>
            <template #empty>
                <slot name="empty">
                    <yd-empty text="暂无数据" />
                </slot>
            </template>
        </z-paging>
    </view>
</template>
<script setup>
import Countdown from "../comp/Countdown.vue"
import DropDown from "../comp/dropDown.vue"
const state = reactive({
    current: 0,
    voteList: [],
    // 筛选参数
    filterParams: {
        type: null, // 投票类型：1单选 2多选 3二选一 4评选
        status: null // 投票状态：0未开始 1进行中 2已结束 3已暂停
    }
})

const menu = ref({
    type: {
        label: "全部",
        child: [
            { label: "全部", value: "all" },
            { label: "单选投票", value: 1 },
            { label: "多选投票", value: 2 },
            { label: "二选一PK投票", value: 3 },
            { label: "评选投票活动", value: 4 }
        ]
    },
    status: {
        label: "全部",
        child: [
            { label: "全部", value: "all" },
            { label: "未开始", value: 0 },
            { label: "进行中", value: 1 },
            { label: "已结束", value: 2 },
            { label: "已暂停", value: 3 }
        ]
    }
})

/**
 * 下拉菜单点击事件
 */
const dropDownRef = ref(null)
const clickItem = (item) => {
    // 更新筛选参数
    if (item.name === "type") {
        state.filterParams.type = item.value
    } else if (item.name === "status") {
        state.filterParams.status = item.value
    }

    // 重新加载数据
    queryList(1, 10)
}

const getStatusText = (status) => {
    const statusMap = {
        0: "未开始",
        1: "进行中",
        2: "已结束",
        3: "已暂停"
    }
    return statusMap[status] || "未知状态"
}

const getStatusClass = (status) => {
    const classMap = {
        0: "status-not-started",
        1: "status-ongoing",
        2: "status-ended",
        3: "status-paused"
    }
    return classMap[status] || "status-unknown"
}

const paging = ref(null)

const queryList = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize,
        isHave: false
    }
    // 添加筛选参数（只有非all值才添加）
    if (state.filterParams.type !== "all") {
        params.type = state.filterParams.type
    }
    if (state.filterParams.status !== "all") {
        params.status = state.filterParams.status
    }
    http.post("/app/vote/page", params)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

function clickLeft() {
    uni.reLaunch({ url: "/pages/workbench/index" })
}

const handleClick = (item) => {
    uni.navigateTo({
        url: `/apps/vote/statistics/details?voteId=${item.id}`
    })
}

const onRefresh = () => {}

const goVoteCreate = () => {
    uni.navigateTo({
        url: `/apps/vote/voteCreate/index`
    })
}

const goVote = () => {
    uni.navigateTo({
        url: `/apps/vote/teacher/index`
    })
}
</script>
<style lang="scss" scoped>
.container_box {
    min-height: calc(100vh - 160rpx);
    background: $uni-bg-color-grey;
    padding: 24rpx 30rpx;
    .top_box {
        background: $uni-bg-color;
        padding: 24rpx;

        .tab_box {
            :deep(.segmented-control__text) {
                color: #b1b0b2;
            }
        }
    }
    .main_box {
        padding: 16rpx 24rpx;
    }
    .footer {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        height: 120rpx;
        background: $uni-bg-color;
        box-shadow: 0rpx 4rpx 12rpx 4rpx rgba(197, 197, 197, 0.5);
        font-size: 20rpx;
        .item {
            view-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100rpx;
            text-align: center;
            .view {
                margin-top: 8rpx;
            }
        }
        .active {
            color: var(--primary-color);
        }
    }
}

// 每个投票项的样式
.vote-item {
    margin-bottom: 30rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);

    // 投票图片容器
    .vote-image-container {
        position: relative;
        width: 100%;
        height: 320rpx;

        .vote-image {
            width: 100%;
            height: 100%;
        }
    }

    // 投票信息区域
    .vote-info {
        padding: 24rpx;

        .vote-info_topBox {
            display: flex;
            align-items: center;
            .vote-title {
                font-weight: 600;
                font-size: 30rpx;
                color: #333333;
                padding-left: 10rpx;
                // 超出部分显示省略号
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            // 状态标签
            .status-tag {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 4rpx 10rpx;
                border-radius: 8rpx;
                flex-shrink: 0;
                .status-text {
                    font-weight: 500;
                    font-size: 20rpx;
                    color: #ffffff;
                }

                // 不同状态的颜色
                &.status-not-started {
                    background-color: #ffc327; // 黄色 - 未开始
                }

                &.status-ongoing {
                    background-color: var(--primary-color); // 绿色 - 进行中
                }

                &.status-ended {
                    background-color: #595959; // 灰色 - 已结束
                }

                &.status-paused {
                    background-color: #595959; // 灰色 - 已暂停
                }

                &.status-unknown {
                    background-color: #595959; // 灰色 - 未知状态
                }
            }
        }

        // 倒计时容器样式
        .countdown-container {
            display: flex;
            align-items: center;
            .time-icon {
                width: 36rpx;
                height: 36rpx;
                margin-right: 6rpx;
                flex-shrink: 0;
                image {
                    width: 100%;
                    height: 100%;
                }
            }

            .countdown-wrapper {
                display: flex;
                align-items: center;
                flex: 1;

                .countdown-label {
                    font-size: 26rpx;
                    color: #666;
                    margin-right: 8rpx;
                }
            }
        }
    }
}
</style>
