<template>
    <view>
        <z-paging ref="paging" :auto="false" v-model="pageList" class="page_box" @query="queryList">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="首页" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
                <lgd-tab v-model="tabActive" :tabValue="tabVal" underlineColor="var(--primary-color)" text-color="var(--primary-color)" :key="tabValue" @change="tabActiveChange" />
                <!-- 筛选 -->
                <div class="filter-box">
                    <view class="cell-wrap">
                        <view class="cell">
                            <text class="text">{{ tabActive == 1 ? "完成时间" : "截止时间" }}</text>
                            <view class="right" @click="openTimePicker">
                                <span>{{ query.time || "请选择" }}</span>
                                <img class="image" src="@nginx/workbench/patrol/icon-triangle-bottom.svg" />
                            </view>
                        </view>
                    </view>
                </div>
                <!-- 是否只看今天 -->
                <view class="only-today">
                    <view class="left">
                        <text class="text">仅看今天</text>
                        <span class="time">({{ todayStr() }})</span>
                    </view>
                    <view class="right">
                        <switch :checked="query.isToday" color="var(--primary-color)" style="transform: scale(0.7)" @change="isTodayChange" />
                    </view>
                </view>
            </template>
            <view v-if="listLoading" class="loading_tip">
                <uv-loading-icon :show="listLoading" :vertical="true" text="加载中..."></uv-loading-icon>
            </view>
            <view class="list-wrap" v-if="pageList && pageList.length > 0">
                <view v-for="c in pageList" :key="c.id" class="cell" @click="handleJumpToTaskDetail('task-detail', { id: c.id })">
                    <view class="title">
                        <status-badge :status="c.status"></status-badge>
                        <text style="padding-left: 8px" class="ellipsis">{{ c.dayProjectName }}</text>
                    </view>
                    <view class="other-info" v-if="tabActive != 1">
                        <text>时间: {{ timeFilter(c.startTime) }} - {{ timeFilter(c.endTime, c.isSameDay) }}</text>
                        <text>已完成: {{ c.successCounts || 0 }} / {{ c.counts || 0 }}</text>
                    </view>
                    <view class="other-info" v-else>
                        <text>完成时间: {{ timeFilter(c.finishTime) }}</text>
                        <text>已完成: {{ c.successCounts || 0 }} / {{ c.counts || 0 }}</text>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <calendar v-model:value="show" @change="timeChange" :isRange="false" monthChangeColor="#000" yearChangeColor="#000" color="#000" @input="timeClose" activeColor="#fff" activeBgColor="rgb(17, 198, 133)"></calendar>
    </view>
</template>

<script setup>
import { ref } from "vue"
import statusBadge from "./components/status-badge.vue"
import calendar from "./l-calendar/components/l-calendar/l-calendar.vue"
import lgdTab from "@/subModules/components/lgd-tab/components/lgd-tab/lgd-tab.vue"

const tabVal = ref(["待巡查", "已完成", "已失效"])
const days = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"]
const paging = ref(null)
const listLoading = ref(false)
const show = ref(false)
const pageList = ref([])
let tabActive = ref(0)
const tabValue = computed(() => {
    return JSON.stringify(tabVal.value)
})
const query = reactive({
    status: 0,
    isToday: false,
    patrolTypeId: ""
})
const openTimePicker = () => {
    show.value = true
}
const timeChange = (value) => {
    query.time = value.result
    show.value = false
    paging.value?.reload()
}
const timeClose = () => {
    show.value = false
}
const taskCount = () => {
    http.post("/app/patrol/task/task/count", { patrolTypeId: query.patrolTypeId }).then((res) => {
        tabVal.value = ["待巡查", "已完成", "已失效"]
        if (res.data.loseCount > 0) {
            tabVal.value[2] = tabVal.value[2] + `(${res.data.loseCount})`
        }
        if (res.data.waitCount > 0) {
            tabVal.value[0] = tabVal.value[0] + `(${res.data.waitCount})`
        }
    })
}

const isTodayChange = (e) => {
    query.isToday = e.detail.value
    paging.value?.reload()
}
const tabActiveChange = (e) => {
    if (e != query.status) {
        query.status = e
        query.time = null
        paging.value?.reload()
    }
}
const numFilter = (num) => {
    if (num < 10) {
        return "0" + num
    } else {
        return num
    }
}
const todayStr = () => {
    let date = new Date()
    // date = date.replace(/\-/g, "/");
    let day = date.getDay()
    let week = days[day]
    return `${numFilter(date.getMonth() + 1)}/${numFilter(date.getDate())} ${week}`
}
const timeFilter = (time, isSameDay) => {
    if (!time) {
        return "无"
    }
    let date1 = time.replace(/\-/g, "/")
    let date = new Date(date1)
    var o = {
        "M+": numFilter(date.getMonth() + 1), //月份
        "d+": numFilter(date.getDate()), //日
        "h+": numFilter(date.getHours()), //小时
        "m+": numFilter(date.getMinutes()), //分
        "s+": numFilter(date.getSeconds()), //秒
        "q+": numFilter(Math.floor((date.getMonth() + 3) / 3)), //季度
        S: date.getMilliseconds() //毫秒
    }
    if (isSameDay) {
        return `${o["h+"]}:${o["m+"]}`
    }
    return `${o["M+"]}/${o["d+"]} ${o["h+"]}:${o["m+"]}`
}
const isSameDay = (t, t2) => {
    return new Date(t.replace(/\-/g, "/")).toDateString() === new Date(t2.replace(/\-/g, "/")).toDateString()
}

const queryList = (pageNo, pageSize) => {
    listLoading.value = true
    http.post("/app/patrol/task/page", { ...query, pageNo, pageSize })
        .then((res) => {
            res.data.list.forEach((i) => {
                i.isSameDay = isSameDay(i.startTime, i.endTime)
            })
            paging.value?.complete(res.data.list)
        })
        .finally(() => {
            listLoading.value = false
        })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    query.patrolTypeId = options.code
    taskCount()
    nextTick(() => {
        paging.value?.reload()
    })
})
const handleJumpToTaskDetail = (url, params) => {
    navigateTo({
        url: url,
        query: params
    })
}
</script>

<style lang="scss" scoped>
.page_box {
    background: $uni-bg-color-grey;
}
.text {
    font-weight: 600;
    color: #333333;
}
.filter-box {
    background: $uni-bg-color;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
    padding: 0 30rpx;
    margin-top: 20rpx;

    :deep(.uni-collapse-item__title) {
        .uni-collapse-item__title-box {
            padding: 0;
        }

        .uni-collapse-item__title-arrow {
            margin-right: 0;

            .uni-icons {
                color: var(--primary-color) !important;
            }
        }
    }

    .cell-wrap {
        .cell {
            height: 100rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .image {
                width: 28rpx;
                height: 28rpx;
            }

            .p {
                font-size: 28rpx;
                font-weight: 600;
                color: #333333;
            }
        }
    }
}

.only-today {
    height: 100rpx;
    background: $uni-bg-color;
    margin-top: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 30rpx;

    .left {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .p {
        height: 40rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
    }

    .time {
        font-size: 28rpx;
        font-weight: 400;
        color: #666666;
    }
}
.loading_tip {
    margin-top: 300rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}
.list-wrap {
    margin-top: 30rpx;
    padding: 30rpx;
    background: $uni-bg-color-grey;

    .cell {
        min-height: 166rpx;
        background: $uni-bg-color;
        box-shadow: 0px 16rpx 16rpx 0px rgba(220, 245, 238, 0.5);
        border-radius: 20rpx;
        box-sizing: border-box;
        padding: 30rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-bottom: 30rpx;

        .title {
            display: flex;
            align-items: center;

            .p {
                font-size: 30rpx;
                font-weight: 600;
                color: #333333;
                margin: 0;
            }
        }

        .other-info {
            font-size: 24rpx;
            font-weight: 400;
            padding-left: 30rpx;
            color: #666666;
            display: flex;
            justify-content: space-between;
        }
    }
}
</style>
