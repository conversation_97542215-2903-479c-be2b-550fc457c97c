<template>
    <view class='attachment'>
        <view class="file-picker">
            <!-- #ifndef MP-WEIXIN -->
            <yd-uni-file-picker v-model="state.tableData" :limit="10" file-mediatype="all" :auto-upload="false"
                @select="fileSelect">
                <button class="file-btn" type="primary" plain="true" size="mini">
                    <uni-icons type="plusempty" size="14" color="#00b781"></uni-icons>
                    新增附件
                </button>
            </yd-uni-file-picker>
            <!-- #endif -->

            <!-- #ifdef MP-WEIXIN -->
            <view @click="chooseFileForMP">
                <button class="file-btn" type="primary" plain="true" size="mini">
                    <uni-icons type="plusempty" size="14" color="#00b781"></uni-icons>新增附件</button>
            </view>
            <!-- #endif -->
        </view>

        <view class="file-list">
            <uni-table ref="table" :loading="loading" type="" emptyText="暂无更多数据">
                <uni-tr>
                    <uni-th align="left" :width="140">附件名称</uni-th>
                    <uni-th align="right">操作</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, index) in state.tableData" :key="index">
                    <uni-td :width="140" style="word-break: break-all;" align="left">
                        <!-- #ifndef MP-WEIXIN -->
                        <text style="word-break: break-all;width: 140px;">
                            {{ item.name }}
                        </text>
                        <!-- #endif -->
                        <!-- #ifdef MP-WEIXIN -->
                        <text style="word-break: break-all;width: 140px;">
                            正文预览
                        </text>
                        <!-- #endif -->
                    </uni-td>
                    <uni-td :width="120" align="right">
                        <text class="look btn" @click="handleLook(item)">查看</text>
                        <!-- #ifndef MP-WEIXIN -->
                        <text class="download btn" @click="handleDownload(item)">下载</text>
                        <!-- #endif -->
                        <text class="delete btn" @click="handleDelete(item)">删除</text>
                    </uni-td>
                </uni-tr>
            </uni-table>
        </view>
    </view>
</template>

<script setup>
import useStore from "@/store"
import YdUniFilePicker from '../../components/yd-uni-file-picker/uni-file-picker.vue'
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"

const { officialDocs } = useStore()
const _documentFileList = computed(() => officialDocs.submitFormData.documentFileList)
const _formListData = computed(() => officialDocs.getDocsParams)
const state = reactive({
    id: '',
    tableData: [],
    loading: false,
    dataList: [],
})

// 下载附件
const handleDownload = (item) => {
    if (["yide-ios-app", "yide-android-app", "yide-Harmony-app"].includes(checkPlatform())) {
        const { name, url, extension } = item
        const params = { downloadName: `${name}.${extension}`, url }
        sendAppEvent("downloadFile", params)
    } else if (checkPlatform() == "wx-miniprogram") {
        uni.showToast({ title: "请点击右上角用浏览器打开下载" })
    } else {
        const fileName = item.name;
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = item.url;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        state.isShowdownload = false
    }
}

// 查看附件
const handleLook = (item) => {
    const codeUrL = encodeURIComponent(Base64.encode(item.url))
    const previewUrl = `${import.meta.env.VITE_BASE_FILE_PREVIEW}/onlinePreview?url=${codeUrL}`
    navigateTo({
        url: '/apps/officialDocs/viewAttachment',
        query: {
            previewUrls: previewUrl
        }
    })

}
// 删除附件
const handleDelete = (item) => {
    uni.showModal({
        content: "确定要删除么？删除后不可恢复！",
        cancelText: "取消",
        confirmText: "确定",
        confirmColor: "#00B781",
        cancelColor: "#00B781",

        success: (res) => {
            if (res.confirm) {
                const _tableData = []
                const fileList = []
                state.tableData.forEach((v) => {
                    if (v.id != item.id) {
                        _tableData.push(v)
                        fileList.push(v.id)
                    }
                })
                state.tableData = _tableData
                officialDocs.setSubmitFormListKey('documentFileList', fileList)
            }
        },
    });
}
const fileSelect = ({ tempFiles }, item) => {
    try {
        tempFiles.forEach((v) => {
            const { path, name, uuid, size } = v
            uni.showToast({
                title: '上传中...',
                icon: "loading"
            })
            http._uploadFile("/cloud/official-doc/file/uploadBatch", path, { folderType: "app" }).then(({ data }) => {
                state.tableData = [...state.tableData, ...data]
                let fileList = []
                if (_documentFileList.value?.length > 0) {
                    fileList = _documentFileList.value.concat([data[0].id])
                } else {
                    fileList.push(data[0].id)
                }
                officialDocs.setSubmitFormListKey('documentFileList', fileList)
                uni.hideToast()
            })
        })
    } catch (err) {
        console.log(err)
    }
}

// 小程序文件选择方法
const chooseFileForMP = () => {
    // 使用uni.chooseMessageFile兼容小程序 从聊天记录中选择文件。
    uni.showActionSheet({
        itemList: ["从相册选择图片", "从聊天记录选择文件"],
        success: (res) => {
            if (res.tapIndex === 0) {
                // 选择图片
                uni.chooseImage({
                    count: 10 - state.tableData.length,
                    success: (res) => {
                        fileSelect({ tempFiles: res.tempFiles })
                    }
                })
            } else if (res.tapIndex === 1) {
                // 从聊天记录选择
                uni.chooseMessageFile({
                    count: 10 - state.tableData.length,
                    success: (res) => {
                        fileSelect({ tempFiles: res.tempFiles })
                    }
                })
            }
        }
    })
}
const initPage = async () => {
    const params = {
        officialDocumentId: _formListData.value.id
    }
    // #ifndef MP-WEIXIN
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    // #endif
    // #ifdef MP-WEIXIN
    wx.showToast({
        title: '加载中...',
        icon: "loading"
    })
    // #endif

    state.loading = true
    try {
        const { data } = await http.post("/cloud/official-doc/document-file/list", params)
        // #ifndef MP-WEIXIN
        uni.hideToast()
        // #endif
        // #ifdef MP-WEIXIN
        wx.hideToast()
        // #endif
        state.tableData = data
        state.loading = false
    } catch (error) {
        console.error('加载附件列表失败:', error)
        state.loading = false
    } finally {
        state.loading = false
    }
}
onMounted(() => {
    if (_formListData.value.status == "NI_GAO") {
        initPage()
    }
})
</script>

<style lang='scss' scoped>
.attachment {
    padding: 20rpx;
    overflow: hidden auto;

    .file-picker {
        margin-bottom: 10rpx;

        .file-btn {
            color: #00b781;
            border-color: #00b781;

        }

        :deep(.uni-file-picker__lists) {
            display: none;
        }
    }

    .file-list {
        margin-bottom: 168rpx;
    }

    .btn {
        color: #00b781;
        margin: 0 5rpx;

        &.delete {
            color: #fd4f45;
        }
    }

}
</style>