<template>
    <div class="form_page">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="新增相册" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <div class="form_box">
            <div class="form_item">
                <uni-easyinput :clearable="false" :maxlength="10" :inputBorder="false" :trim="true" v-model="form.name" placeholder="请输入相册名称" primaryColor="var(--primary-color)">
                    <template #right>
                        <view class="input_count">{{ form.name?.length || 0 }}/10</view>
                    </template>
                </uni-easyinput>
            </div>
            <div class="switch_item">
                <div class="title">班牌显示该相册</div>
                <switch color="var(--primary-color)" style="transform: scale(0.7)" @change="switchChange" />
            </div>
        </div>
        <button class="button_class" @click="submitCreate" :disabled="loading" :loading="loading">完成创建</button>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const loading = ref(false)
const form = ref({
    isShow: false
})

function clickLeft() {
    uni.navigateBack()
}

function switchChange(e) {
    form.value.isShow = e.detail.value
}

function submitCreate() {
    if (!form.value.name) {
        uni.showToast({ title: "相册名称不能为空", icon: "none" })
        return
    }
    loading.value = true
    http.post("/brand/album/classify/create", form.value)
        .then((res) => {
            uni.showToast({ title: res.message, icon: "none" })
            uni.navigateBack()
        })
        .finally(() => {
            loading.value = false
        })
}

onLoad((option) => {
    form.value = { ...form.value, ...option }
})
</script>

<style lang="scss" scoped>
.page_title_box {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: 500;
    font-size: 32rpx;
    color: $uni-text-color;
    line-height: 88rpx;
    text-align: center;
}
.form_page {
    background: $uni-bg-color-grey;
    min-height: 100vh;
}
.form_box {
    .form_item {
        background: $uni-bg-color;
        padding: 30rpx;
        margin-bottom: 20rpx;

        :deep(.uni-easyinput__content) {
            background-color: $uni-bg-color-grey !important;
            border-radius: 10rpx;
            text-align: left;
        }
        .input_count {
            display: inline-block;
            padding-right: 20rpx;
            color: $uni-text-color-grey;
            font-size: 28rpx;
            right: 20rpx;
            bottom: 10rpx;
        }
    }
    .switch_item {
        background: $uni-bg-color;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10rpx 30rpx;
        .title {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }
    }
}

.button_class {
    background: var(--primary-color);
    margin: 60rpx 30rpx;
    color: $uni-text-color-inverse;
}
</style>
