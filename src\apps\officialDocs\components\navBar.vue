<template>
    <view class="nav-bar">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :left-text="leftText" :border="false" @clickLeft="back" :title="props.title" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
    </view>
</template>

<script setup>
const props = defineProps({
    // 返回事件
    clickLeft: Function,
    title: {
        type: String,
        default: ""
    },
    leftText: {
        type: String,
        default: ""
    }
})

const back = () => {
    if (props.clickLeft) {
        return props.clickLeft()
    }
    uni.navigateBack({
        delta: 1
    })
}
</script>

<style lang="scss" scoped>
.nav-bar {
    background-color: $uni-bg-color;

    .bar_right {
        color: $uni-color-primary;
    }
}
</style>
