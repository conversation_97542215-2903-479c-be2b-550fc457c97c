<template>
    <view class="punishment">
        <z-paging ref="paging" v-model="state.dataList" @query="getVideoList" :auto="false">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="处分档案管理" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
            </template>
            <view class="content">
                <view class="list-itme" v-for="item in state.dataList" :key="item.id">
                    <view class="list-itme-hande">
                        <img class="img" src="@nginx/workHall/student/sanctions.png" alt="" />
                        <text>{{ item.eventTypeName }}</text>
                        -
                        <text>{{ item.actionTypeName }}</text>
                    </view>
                    <view class="list-itme-content">
                        <view class="content-time">{{ item.createTime }}</view>
                        <view class="content-btn">
                            <view class="content-text" :class="{ active: item.status == 2 }" @click="onConfirm(item)">
                                {{ item.status == 1 ? "撤销处分" : "已撤销" }}
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
const paging = ref(null)
const state = reactive({
    dataList: []
})
// 老师
async function getVideoList(pageNo, pageSize) {
    await http
        .post("/app/disciplinary/action/page", {
            pageNo,
            pageSize
        })
        .then(({ data }) => {
            paging.value.complete(data.list)
        })
        .catch(() => {
            paging.value.complete(false)
        })
}
function onConfirm(item) {
    if (item.status == 2) return
    uni.navigateTo({
        url: `/apps/sanctions/revoke?id=${item.id}`
    })
}
onShow(() => {
    // init()
    if (paging.value) {
        paging.value?.reload()
    } else {
        getVideoList(1, 10)
    }
})
onLoad(() => {})
</script>

<style lang="scss" scoped>
.punishment {
    background: #f9faf9;
    height: 100vh;
    .content {
        background: #fff;
        margin: 10px 15px;
        border-radius: 10px;
        .list-itme {
            border-bottom: 1px solid #d9d9d9;
            margin: 10px 15px;
            padding: 17px 0;
            .list-itme-hande {
                display: flex;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
                .img {
                    width: 22px;
                    height: 22px;
                    border-radius: 50%;
                    margin-right: 10px;
                }
            }
            .content-time {
                font-weight: 400;
                font-size: 14px;
                color: #999999;
                margin: 10px 22px 10px 34px;
            }
            .content-btn {
                display: flex;
                justify-content: flex-end;
                .content-text {
                    font-size: 14px;
                    min-width: 86rpx;
                    padding: 4rpx 10rpx;
                    height: 32rpx;
                    line-height: 32rpx;
                    text-align: center;
                    background: #ffffff;
                    border-radius: 18rpx;
                    border: 1rpx solid var(--primary-color);
                    color: var(--primary-color);
                    &.active {
                        background: #d7d7d7;
                        color: #ffffff;
                        border: 1rpx solid #d7d7d7;
                    }
                }
            }
        }
    }
}
</style>
