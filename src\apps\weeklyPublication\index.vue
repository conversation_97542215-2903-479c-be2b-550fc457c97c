<template>
    <view class="weekly_publication">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="周报" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="weekly_list">
            <view v-for="item in weeklyTypeList" :key="item.type">
                <view
                    @click="routerPush(item)"
                    class="weekly_item"
                    v-if="!item.isHide"
                    :style="{
                        background: item.bg
                    }"
                >
                    <view class="left">
                        <image class="type_icon" :src="item.image" mode="scaleToFill" />
                        <text class="title">{{ item.title }}</text>
                    </view>
                    <uni-icons type="right" size="24" color="#999999"></uni-icons>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
const weeklyTypeList = ref([
    {
        title: "考勤周报",
        type: "attendance",
        bg: "#ECFFF3",
        image: "@nginx/workbench/weeklyPublication/weekly_attendance.png"
    },
    {
        title: "图书馆周报",
        type: "library",
        bg: "#F2F9FF",
        image: "@nginx/workbench/weeklyPublication/weekly_library.png",
        isHide: true
    }
])

function getLibShow() {
    http.post("/app/library/libraryUserVerify").then((res) => {
        weeklyTypeList.value[1].isHide = res.code != 0 || res.data != true
    })
}

function routerPush(item) {
    const path = {
        library: "libraryWeekly",
        attendance: "attendanceWeekly"
    }
    navigateTo({
        url: `/apps/weeklyPublication/${path[item.type]}/index`
    })
}

onMounted(() => {
    // getLibShow()
})
</script>

<style lang="scss" scoped>
.weekly_publication {
    min-height: 100vh;
    .weekly_list {
        padding: 30rpx;
        .weekly_item {
            height: 136rpx;
            border-radius: 20rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40rpx;
            padding: 30rpx;

            .left {
                display: flex;
                align-items: center;
                .type_icon {
                    width: 136rpx;
                    height: 136rpx;
                }
                .title {
                    font-weight: 500;
                    font-size: 36rpx;
                    color: $uni-text-color;
                    line-height: 50rpx;
                }
            }
        }
    }
}
</style>
