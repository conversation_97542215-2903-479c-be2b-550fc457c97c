<template>
    <yd-page-view ref="page" class="dorm_info_container" title="首页" :hideLeft="true" :hideBottom="false" :tabBarCurrent="1">
        <template #top>
            <!-- #ifdef APP-PLUS || H5 -->
            <uni-nav-bar :showMenuButtonWidth="true" fixed statusBar :rightWidth="0" :leftWidth="0">
            <!-- #endif -->
			<!-- #ifdef MP-WEIXIN -->
            <uni-nav-bar :showMenuButtonWidth="true" fixed statusBar :leftWidth="0" :rightWidth="86">
        	<!-- #endif -->
                <view class="search">
                    <uv-input placeholder="搜索学生姓名" prefixIcon="search" shape="circle" prefixIconStyle="font-size: 22px;color: #909399" @focus="focus"></uv-input>
                </view>
            </uni-nav-bar>
        </template>
        <view class="dorm_info_container">
            <view class="top">
                <zb-tab
                    :activeStyle="{
                        fontWeight: 'bold',
                        color: '#4566D5'
                    }"
                    lineColor="#4566D5"
                    lineWidth="40rpx"
                    height="88rpx"
                    :shrink="true"
                    :data="state.tabList"
                    v-model="state.tabKey"
                    @change="tabChange"
                ></zb-tab>
            </view>
            <view class="main" v-if="state.peopleAlreadyCheckIn">
                <view class="box">
                    <view class="top">
                        <view v-for="(item, index) in colorList" :key="index" class="item_box">
                            <view :class="['type_box', classList[item.type]]"></view>
                            <text>{{ item.name }}</text>
                        </view>
                    </view>
                    <view class="mian_box">
                        <view :class="['dorm_item', classList[item.status]]" v-for="(item, index) in state.dormList" :key="index" @click="handleClick(item)">
                            <view class="top_text">{{ item.roomNum }}</view>
                            <view class="btm_text">{{ item.status === 1 ? `剩${item.idleBeds}床位` : changeType[item.status] }}</view>
                        </view>
                    </view>
                </view>
            </view>
            <view v-else class="empty_box">
                <img src="https://alicdn.1d1j.cn/1531914651808833538/default/c6ff918b95b14b97b8a332c05414047c.png" />
                <view class="text_c">该寝室还没有一个人入住哦</view>
                <button @click="gotoAllot">一键智能分寝</button>
            </view>
        </view>
    </yd-page-view>
</template>

<script setup>
import zbTab from "./zb-tab/components/zb-tab/zb-tab.vue"
const { system } = store()
const buildingId = computed(() => system.apps.dormManage.buildingId)

const state = reactive({
    tabKey: null,
    tabList: [],
    dormList: [],
    peopleAlreadyCheckIn: true
})

const classList = {
    2: "type_0",
    1: "type_1",
    0: "type_2",
    3: "type_3"
}

const changeType = {
    2: "满员",
    0: "空房",
    3: "禁用"
}

const colorList = [
    { name: "满员", type: 2 },
    { name: "未满", type: 1 },
    { name: "空房", type: 0 },
    { name: "禁用", type: 3 }
]

// 获取寝室总览
const getManage = async () => {
    const {
        data: { peopleAlreadyCheckIn, eachFloor }
    } = await http.post("/app/dormitory/managerManage/query", {
        buildingId: buildingId.value
    })

    state.peopleAlreadyCheckIn = peopleAlreadyCheckIn
    state.tabList = eachFloor.map((i, index) => ({ ...i, value: index }))
    state.tabKey = 0
    state.dormList = state.tabList[0].eachRoom
}
getManage()

const tabChange = (item) => {
    state.dormList = item.eachRoom
}

const handleClick = (item) => {
    navigateTo({
        url: "/apps/dormManage/dormInfo/dormInfo/index",
        query: item
    })
}

const gotoAllot = () => {
    navigateTo({
        url: "/apps/dormManage/allotRoom/index"
    })
}

const focus = () => {
    const params = {
        needLivingStayInformation: true
    }
    navigateTo({
        url: "/apps/dormManage/searchView/index",
        query: params
    })
}
</script>
<style lang="scss" scoped>
.dorm_info_container {
    :deep(.uni-navbar__content) {
        border-bottom-color: transparent !important;
    }
    .search {
        background: #fff;
        width: 100%;
        margin: auto 0;
        :deep(.uv-input--circle) {
            background-color: $uni-bg-color-grey;
        }

        :deep(.uv-input__content) {
            height: 33rpx;
        }
    }
    .main {
        background-color: $uni-bg-color-grey;
        padding: 28rpx;
        .box {
            background-color: #fff;
            border-radius: 20rpx;
            padding: 34rpx 28rpx;
            .top {
                display: flex;
                justify-content: space-between;
                font-size: 28rpx;
                color: #333;
                padding-bottom: 34rpx;
                .item_box {
                    display: flex;
                    align-items: center;
                    .type_box {
                        width: 40rpx;
                        height: 28rpx;
                        border-radius: 4rpx;
                        margin-right: 6rpx;
                    }
                }
            }
            .mian_box {
                display: flex;
                flex-wrap: wrap;

                .dorm_item {
                    width: 200rpx;
                    height: 100rpx;
                    border-radius: 20rpx;
                    padding: 14rpx 24rpx;
                    box-sizing: border-box;
                    margin: 0 18rpx 28rpx 0;
                    .top_text {
                        font-weight: 600;
                    }
                    .btm_text {
                        font-size: 24rpx;
                        // color: #4566d5;
                    }
                    &:nth-of-type(3n) {
                        margin-right: 0;
                    }
                }
            }

            .type_0 {
                background: #ffa645;
                color: #fff !important;
            }
            .type_1 {
                border: 1rpx solid #4566d5;
                background: #ffffff;
                .btm_text {
                    color: #4566d5;
                }
            }
            .type_2 {
                background: #1ec3a1;
                color: #fff !important;
            }
            .type_3 {
                background: #d9d9d9;
                color: #fff !important;
                pointer-events: none;
            }
        }
    }
    .empty_box {
        background-color: $uni-bg-color-grey;
        min-height: calc(100vh - 300rpx);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .img {
            width: 360rpx;
            height: 210rpx;
        }
        .text_c {
            color: #8c8c8c;
            font-size: 28rpx;
            padding: 30rpx 0 80rpx 0;
        }
        button {
            border-color: #4566d5;
            color: #4566d5;
            background-color: #fff;
            border: 1rpx solid #4566d5;
            width: 330rpx;
        }
    }
}
</style>
