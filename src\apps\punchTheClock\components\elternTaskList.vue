<template>
    <div class="my_task">
        <z-paging ref="paging" :auto="false" v-model="pageList" @query="queryList">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed :border="false" left-icon="left" @clickLeft="routerBack" title="我的任务" :leftWidth="navBarLeftWidth(80)" :rightWidth="navBarRightWidth(80)">
                    <template v-slot:right>
                        <!-- #ifdef H5 || H5-WEIXIN-->
                        <div class="bar_right" @click="changeStudent" v-if="state.selectList.length > 1">
                            <text class="l_text">{{ state.studentName }}</text>
                            <img class="img" src="@nginx/workbench/punchTheClock/switch.png" />
                        </div>
                        <!-- #endif -->
                        <!-- #ifdef APP-PLUS -->
                        <div class="bar_right" @click="changeStudent" v-if="state.selectList.length > 1">
                            <text class="l_text">{{ state.studentName }}</text>
                            <img class="img" src="@nginx/workbench/punchTheClock/switch.png" />
                        </div>
                        <!-- #endif -->
                    </template>
                </uni-nav-bar>
                <!-- #ifdef MP-WEIXIN -->
                <div class="bar_right" @click="changeStudent" v-if="state.selectList.length > 1">
                    <text class="l_text">{{ state.studentName }}</text>
                    <img class="img" src="@nginx/workbench/punchTheClock/switch.png" />
                </div>
                <!-- #endif -->
                <uv-tabs :current="1" :scrollable="false" lineColor="var(--primary-color)" :activeStyle="{ color: 'var(--primary-color)' }" :inactiveStyle="{ color: '#999999' }" :list="list" @click="tabsClick"></uv-tabs>
            </template>
            <div class="task_page" v-if="pageList && pageList.length > 0">
                <div class="task_item" @click="taskDetails(item)" v-for="item in pageList" :key="item.id">
                    <div class="left">
                        <div class="title two_ellipsis">
                            <div v-if="item.subjectIdList[0] != -1" class="tag">
                                {{ item.subjectList.join() }}
                            </div>
                            <span class="t"> {{ item.taskName }}</span>
                        </div>
                        <span class="date">截止时间 {{ item.publishEnd }}</span>
                    </div>
                    <div class="right" v-if="query.status != 'not_start'">
                        <div
                            class="circle"
                            :style="{
                                height: query.status == 'end' ? '140rpx' : '110rpx',
                                width: query.status == 'end' ? '140rpx' : '110rpx',
                                border: query.status == 'end' ? 'none' : '16rpx solid',
                                borderColor: item.todayHaveSign ? '#88e2c2FF' : '#11c68533',
                                background: query.status == 'in_progress' ? '#fff' : query.status !== 'end' ? 'var(--primary-color)' : 'var(--primary-bg-color)'
                            }"
                        >
                            <span v-if="query.status == 'in_progress'">{{ item.todayHaveSign ? "已打卡" : "未打卡" }}</span>
                            <span v-else>打卡次数</span>
                            <span> {{ item.alreadySignCount }}/{{ item.totalSignCount }} </span>
                        </div>
                    </div>
                </div>
            </div>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>

        <yd-select-popup ref="selectPopupRef" title="请选择学生" :list="state.selectList" @closePopup="closePopup" :fieldNames="{ value: 'studentId', label: 'studentName' }" :selectId="[query.studentId]" />
    </div>
</template>

<script setup>
import useStore from "@/store"

const { user } = useStore()
const paging = ref(null)
const query = ref({
    status: "in_progress"
})
const pageList = ref([])
const selectPopupRef = ref(null)

const state = reactive({
    selectList: [],
    studentName: ""
})

const list = ref([
    {
        name: "未开始",
        code: "not_start"
    },
    {
        name: "进行中",
        code: "in_progress"
    },
    {
        name: "已结束",
        code: "end"
    }
])

// 调用List数据
function queryList(pageNo, pageSize) {
    const params = {
        ...query.value,
        pageNo,
        pageSize
    }
    http.post("/attweb/attendanceSignTask/app/myTask", params).then(({ data }) => {
        paging.value?.complete(data.list)
    })
}

const closePopup = (val) => {
    if (!val || query.value.studentId == val.studentId) return
    state.studentName = val.studentName
    query.value.studentId = val.studentId
    paging.value?.reload()
}

// 选择学生
function changeStudent() {
    selectPopupRef.value.open()
}

function taskDetails(item) {
    navigateTo({
        url: "/apps/punchTheClock/taskDetails",
        query: {
            type: "parent",
            parameter: JSON.stringify(item),
            studentId: query.value.studentId
        }
    })
}

function tabsClick(item) {
    query.value.status = item.code
    paging.value?.reload()
}

// 上划加载
onReachBottom(() => {
    getList()
})

onShow(() => {
    if (query.value.studentId) {
        paging.value?.reload()
    }
})

onLoad(() => {
    state.selectList = user.studentInfo
    state.studentName = user.studentInfo[0]?.studentName
    query.value.studentId = user.studentInfo[0]?.studentId
    nextTick(() => {
        if (query.value.studentId) {
            paging.value?.reload()
        }
    })
})
</script>

<style lang="scss" scoped>
.my_task {
    :deep(.uni-navbar__header-btns-right) {
        overflow: visible;
    }
    .bar_right {
        font-weight: 400;
        font-size: 28rpx;
        color: var(--primary-color);
        line-height: 40rpx;
        display: flex;
        // #ifdef MP-WEIXIN
        padding: 10rpx 30rpx;
        background: $uni-bg-color-grey;
        // #endif

        .l_text {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            text-align: right;
        }
        .img {
            width: 44rpx;
            height: 44rpx;
            flex-shrink: 0;
        }
    }
    .task_page {
        padding: 20rpx 30rpx;
        min-height: calc(100vh - 320rpx - var(--status-bar-height));
        //  #ifdef H5 || H5-WEIXIN
        background: $uni-bg-color-grey;
        min-height: calc(100vh - 220rpx);
        //  #endif
        //  #ifdef APP-PLUS
        background: $uni-bg-color-grey;
        min-height: calc(100vh - 220rpx);
        //  #endif
        .task_item {
            padding: 30rpx;
            min-height: 106rpx;
            background: $uni-bg-color;
            box-shadow: 0rpx 16rpx 16rpx 0rpx #dcf5ee80;
            border-radius: 20rpx;
            display: flex;
            justify-content: space-between;
            margin-bottom: 18rpx;
            .left {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                .title {
                    .tag {
                        display: inline-block;
                        max-width: 82rpx;
                        border-radius: 4rpx;
                        border: 1rpx solid var(--primary-color);
                        font-weight: 400;
                        font-size: 20rpx;
                        color: var(--primary-color);
                        line-height: 30rpx;
                        text-align: center;
                        vertical-align: middle;
                        margin-bottom: -4rpx;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        padding: 0 10rpx;
                    }
                    overflow-wrap: break-word;
                    word-break: break-all;
                    white-space: normal;
                    overflow: hidden;
                    .t {
                        padding-left: 10rpx;
                        line-height: 42rpx;
                        font-weight: 600;
                        font-size: 30rpx;
                        color: $uni-text-color;
                    }
                }
                .date {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #666666;
                    line-height: 34rpx;
                }
            }
            .right {
                margin-left: 20rpx;
                .circle {
                    width: 110rpx;
                    height: 110rpx;
                    background: $uni-bg-color;
                    border: 16rpx solid #11c68533; /* 圆环的宽度和颜色 */
                    border-radius: 50%;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                    font-weight: 500;
                    font-size: 26rpx;
                    color: var(--primary-color);
                    line-height: 34rpx;
                }
            }
        }
    }
    .add {
        position: fixed;
        bottom: 44rpx;
        right: 16rpx;
        width: 140rpx;
        height: 140rpx;
    }
}
</style>
