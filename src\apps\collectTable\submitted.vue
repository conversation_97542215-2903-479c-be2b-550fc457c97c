<template>
    <div class="accomplish">
        <uni-nav-bar :showMenuButtonWidth="true" class="nav" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="完成" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <div class="accomplish_content">
            <image class="img" src="https://file.1d1j.cn/cloud-mobile/components/submitted.png" />
            <p class="tips">上报已提交</p>
            <button class="mini-btn" type="primary" plain="true" size="mini" style="border-color: var(--primary-color); color: var(--primary-color)" @click="onClickLeft">查看提交</button>
        </div>
    </div>
</template>

<script setup>
const formId = ref({})
// 返回
function clickLeft() {
    uni.navigateBack(1)
}
const onClickLeft = () => {
    navigateTo({
        url: "/apps/collectTable/fillIn/createForm",
        query: { ...formId.value, isEdit: true }
    })
}

onLoad((item) => {
    formId.value = item
})
</script>

<style scoped lang="scss">
.accomplish {
    background-color: #f6f6f6;
    height: 100vh;

    .accomplish_content {
        text-align: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        .img {
            width: 100rpx;
            height: 100rpx;
        }

        .tips {
            margin: 0 auto 50rpx;
            font-size: 16px;
            text-align: center;
        }
    }
}
</style>
