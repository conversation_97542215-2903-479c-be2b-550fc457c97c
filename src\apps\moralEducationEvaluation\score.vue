<template>
    <div class="score">
        <view class="head">
            <uni-nav-bar :showMenuButtonWidth="true" left-icon="left" :border="false" statusBar fixed title="评分" @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        </view>
        <div class="select_class">
            <uni-data-picker v-model="state.classId" :localdata="gotoScoreObj.scoreTargetList" @change="onChangeClass" :map="{ text: 'targetName', value: 'targetId' }" v-slot:default="{ data, error }" popup-title="请选择班级">
                <view v-if="error" class="error">
                    <text>{{ error }}</text>
                </view>
                <view v-else-if="data.length" class="selected">
                    <div class="triangle_class">
                        <view v-for="(item, index) in data" :key="index" class="selected-item">
                            <text>{{ item.text }}</text>
                        </view>
                        <span class="triangle_down"></span>
                    </div>
                </view>
                <view v-else>
                    <div class="triangle_class">
                        <text>请选择</text>
                        <span class="triangle_down"></span>
                    </div>
                </view>
            </uni-data-picker>
            <view class="right_class" @click="scoringRulesFn">
                <img class="image" src="@nginx/workbench/moralEducationEvaluation/scoringRules.png" alt="" />
                <span class="text">评分规则</span>
            </view>
        </div>
        <div class="comparison_info">
            <div class="title">{{ state.title }}</div>
            <div class="cycle">
                <span>评分周期：</span>
                <span style="color: #333333">{{ state.cycle }}</span>
            </div>
        </div>
        <div class="form_class">
            <scoring-form :scoreList="state.scoreList" @submitForm="submitFormFn" :loading="state.scoringLoading" />
        </div>
        <look-rules :isShow="state.isShow" :scoreList="state.scoreList" @closePopup="closeFn" />
    </div>
</template>

<script setup>
import ScoringForm from "./components/scoringForm.vue"
import LookRules from "./components/lookRules.vue"
import { onShow, onLoad } from "@dcloudio/uni-app"

let gotoScoreObj = ref({})
const pageParams = ref({})

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    pageParams.value = options
})
const state = reactive({
    scoringLoading: false,
    isShow: false,
    scoreList: [],
    title: "",
    cycle: "",
    toObj: {
        toObjName: "",
        toObjId: ""
    },
    classId: ""
})
function scoringRulesFn() {
    state.isShow = true
}

function closeFn() {
    state.isShow = false
}

function getRulesFn() {
    http.post("/app/moralEducationActivityMobile/scoreDetail", {
        activityId: gotoScoreObj.activityId
    }).then((res) => {
        state.scoreList = res.data?.regulationList || []
        state.title = res.data?.title || ""
        state.cycle = res.data?.cycle || ""
    })
}

// 点击选择班级
function onChangeClass({ detail: { value } }) {
    state.toObj = {
        toObjName: value[0]?.text || "",
        toObjId: value[0]?.value || ""
    }
}

function back() {
    uni.navigateBack()
}

let scoreArr = []
function formData(list) {
    list.forEach((item) => {
        if (!item.scoreStandardId) {
            return formData(item.indicatorDetailList)
        } else {
            scoreArr.push({
                standardId: item.scoreStandardId,
                remark: item.remarked,
                scoreValue: item.initializeScore
            })
        }
    })
    return scoreArr
}

function clickLeft() {
    uni.navigateBack()
}

function gotoScoreFn(arr) {
    const { activityId, fromGroupId, fromPersonId, schoolId, cycleId } = gotoScoreObj
    let params = {
        detailList: arr,
        ...state.toObj,
        activityId,
        fromGroupId,
        fromPersonId,
        schoolId,
        cycleId
    }
    delete params.scoreTargetList
    state.scoringLoading = true
    http.post("/app/moralEducationActivityMobile/gotoScore", params)
        .then((res) => {
            getRulesFn()
            uni.showToast({
                title: res.message,
                duration: 2000,
                icon: "none"
            })
            clickLeft()
        })
        .finally(() => {
            state.scoringLoading = false
        })
}

function submitFormFn(list) {
    scoreArr = []
    const arr = formData(list)
    gotoScoreFn(arr)
}

function getDataFn(cycleId, title) {
    // type 评价类型(1-班级德育 2-宿舍德育)
    http.post("/app/moralEducationActivityMobile/competition", { type: 1 }).then((res) => {
        if (res?.data) {
            gotoScoreObj = res.data
            state.classId = res.data.scoreTargetList[0]?.targetId || ""
            state.toObj.toObjName = gotoScoreObj.scoreTargetList ? gotoScoreObj.scoreTargetList[0]?.targetName : ""
            state.toObj.toObjId = res.data.scoreTargetList ? gotoScoreObj.scoreTargetList[0]?.targetId : ""
            getRulesFn()
        }
    })
}

onShow(() => {
    getDataFn()
})
</script>

<style lang="scss" scoped>
.score {
    min-height: 100vh;
    max-height: 100vh;
    overflow-y: auto;
    background: $uni-bg-color-grey;

    // 头部
    .head {
        width: 100%;
        background: $uni-bg-color;
        z-index: 5;
    }

    .comparison_info {
        height: 162rpx;
        background: $uni-bg-color;
        display: flex;
        flex-direction: column;
        padding: 0rpx 30rpx;
        justify-content: space-evenly;

        .title {
            font-size: 30rpx;
            font-weight: 500;
            color: $uni-text-color;
            line-height: 42rpx;
        }
        .cycle {
            font-size: 28rpx;
            font-weight: 400;
            color: $uni-text-color-grey;
            line-height: 40rpx;
        }
    }
    .form_class {
        margin-top: 20rpx;
        padding-bottom: 200rpx;
    }
    // 底部
}

.select_class {
    background: $uni-bg-color;
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    :deep(.selected) {
        display: flex;
    }
    .right_class {
        display: flex;
        justify-content: flex-end;
        height: 100%;
        align-items: center;

        .text {
            font-size: 28rpx;
            font-weight: 400;
            color: var(--primary-color);
            line-height: 40rpx;
        }

        .image {
            width: 44rpx;
            height: 44rpx;
        }
    }
}
.triangle_class {
    display: flex;
    align-items: center;

    .triangle_down {
        width: 0;
        height: 0;
        overflow: hidden;
        font-size: 0;
        line-height: 0;
        border-width: 10rpx;
        margin-top: 10rpx;
        margin-left: 10rpx;
        border-style: solid dashed dashed dashed;
        border-color: var(--primary-color) transparent transparent transparent;
    }
}
</style>
