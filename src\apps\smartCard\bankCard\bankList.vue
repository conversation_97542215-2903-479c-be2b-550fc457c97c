<template>
  <view class="bank-list-page">
    <!-- z-paging组件，开启下拉刷新，不开启上拉加载 -->
    <z-paging ref="pagingRef" v-model="bankCardList" :fixed="true" :auto="true" :refresher-enabled="true"
      :refresher-threshold="80" :loading-more-enabled="false" @query="queryBankCardList" @refresherrefresh="onRefresh">
      <template #top>
        <NavBar title="银行卡绑定/解绑" :clickLeft="clickLeft" />
      </template>
      <!-- 银行卡列表 -->

      <view class="bank-card-list">
        <view class="bank-card-item" v-for="item in bankCardList" :key="item.id" @click="handleCardClick(item)">
          <!-- 银行卡片 -->
          <view class="bank-card" :style="{
            backgroundImage: `url(${getBankBackground(item.bankCode)})`,
          }">
            <!-- 银行信息区域 -->
            <view class="bank-info">
              <image class="bank-icon" :src="getBankIcon(item.bankCode)" mode="aspectFit" />
              <view class="bank-header">
                <view class="bank-name">{{ item.bankName }}</view>
                <view class="card-type">{{ item.cardType || "储蓄卡" }}</view>
              </view>
            </view>

            <!-- 卡号信息区域 -->
            <view class="card-info">
              <view class="card-number">
                <text class="card-prefix"></text>
                <text class="card-last-four">
                  •••• •••• •••• {{ item.accountLastFour }}</text>
              </view>
              <view class="card-actions">
                <view class="unbind-btn" @click.stop="handleUnbind(item.relatedId)">
                  解绑
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->

      </view>
      <template #empty>
        <view v-if="!loading" class="empty-state">
          <yd-empty text="暂无绑定的银行卡" />
          <text class="empty-tip">请点击下方按钮添加银行卡</text>
        </view>
      </template>
      <!-- 底部固定区域：操作按钮 -->
      <template #bottom>
        <view class="bottom-section">
          <view class="button-row">
            <view class="action-btn primary" @click="handleAddBank">
              添加银行卡
            </view>
          </view>
        </view>
      </template>
    </z-paging>

  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import NavBar from "../components/navBar.vue"
import bankConfig from "../components/bankConfig.js"
import useStore from "@/store"
const { useSmartCard } = useStore()
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)

// 响应式数据
const pagingRef = ref(null);
const bankCardList = ref([]);
const loading = ref(false);

// 根据银行代码获取银行背景图
const getBankBackground = (bankCode) => {
  const config = bankConfig[bankCode] || bankConfig["DEFAULT"];
  return config.background;
};
// 根据银行代码获取银行图标
const getBankIcon = (bankCode) => {
  const config = bankConfig[bankCode] || bankConfig["DEFAULT"];
  return config.icon;
};
// 查询银行卡列表
const queryBankCardList = async (pageNo, pageSize) => {
  try {
    loading.value = true;
    if (pageNo === 1) {
      uni.showLoading({
        title: "加载中...",
        mask: true,
      });
      const params = { personId: _returnParams.value.personId, pageNo, pageSize }
      const { data } = await http.post("/unicard/app/bocom/bank-card/list-sign-card", params)

      uni.hideLoading();

      pagingRef.value?.complete(data);
    } else {
      // 非第一页直接返回空数组
      pagingRef.value?.complete([]);
    }
  } catch (error) {
    console.error("获取银行卡列表失败:", error);
    uni.showToast({
      title: "获取银行卡列表失败",
      icon: "none",
    });
    pagingRef.value?.complete(false);
  } finally {
    loading.value = false;
  }
};

/**
 * 下拉刷新处理
 */
const onRefresh = () => {
  queryBankCardList(1, 10);
};

/**
 * 银行卡点击处理
 * @param {object} item - 银行卡信息
 */
const handleCardClick = (item) => {
  console.log("点击银行卡:", item);
  // 可以在这里添加查看银行卡详情的逻辑
};

/**
 * 解绑银行卡
 * @param {object} item - 银行卡信息
 */
const handleUnbind = (relatedId) => {
  useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/bankCard/bankList'])
   useSmartCard.setReturnParams({
    ..._returnParams.value,
    relatedId
  })
  uni.navigateTo({
    url: '/apps/smartCard/bankCard/unbind'
  })
};
/**
 * 添加银行卡
 */
const handleAddBank = () => {
  // 跳转到添加银行卡页面
  useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/bankCard/bankList'])
  // useSmartCard.setReturnParams({
  //   ..._returnParams.value,
  //   crossRoute: '/apps/smartCard/bankCard/bankList'
  // })
  uni.navigateTo({
    url: '/apps/smartCard/bankCard/addBankCard'
  })
};

// 页面挂载时的初始化
onMounted(() => {
  console.log("银行卡列表页面已挂载");
});

/**
 * 返回处理
 */
function clickLeft() {
  // 获取最后一个路由
  const _route = _returnRoute.value.pop()
  navigateTo({ url: _route })
}
</script>

<style lang="scss" scoped>
.bank-list-page {
  height: 100vh;
  background-color: #f7f7f7;
}

.bank-card-list {
  padding: 32rpx 32rpx 0;
}

.bank-card-item {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 银行卡片样式 */
.bank-card {
  height: 160rpx;
  border-radius: 16rpx;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

  /* 添加渐变遮罩以确保文字可读性 */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(0, 0, 0, 0.1) 0%,
        rgba(0, 0, 0, 0.3) 100%);
    z-index: 1;
  }

  /* 确保内容在遮罩之上 */
  .bank-info,
  .card-info {
    position: relative;
    z-index: 2;
  }
}

/* 银行信息区域 */
.bank-info {
  display: flex;
  font-weight: 400;
  color: #FFFFFF;

  .bank-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 16rpx;
    border-radius: 8rpx;
    padding: 8rpx;
  }

  .bank-name {
    font-size: 32rpx;
  }

  .card-type {
    font-weight: 400;
  }
}


/* 卡号信息区域 */
.card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;


  .card-number {
    display: flex;
    align-items: center;
    margin-top: 10rpx;

    .card-prefix {
      width: 70rpx;
    }

    .card-last-four {
      color: #ffffff;
      font-size: 36rpx;
      font-weight: 600;
      font-family: "Courier New", monospace;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    }
  }

  .card-actions {
    .unbind-btn {
      background-color: rgba(255, 255, 255, 0.5);
      color: #ffffff;
      font-size: 24rpx;
      padding: 12rpx 24rpx;
      border-radius: 8rpx;
      // border: 1rpx solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10rpx);
      transition: all 0.3s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
      }
    }
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;

  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 32rpx;
    opacity: 0.6;
  }

  .empty-text {
    color: #999999;
    font-size: 32rpx;
    margin-bottom: 16rpx;
  }

  .empty-tip {
    color: #cccccc;
    font-size: 28rpx;
  }
}

/* 底部固定区域样式 */
.bottom-section {
  padding: 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;

  .button-row {
    display: flex;
    gap: 24rpx;

    .action-btn {
      flex: 1;
      height: 92rpx;

      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      font-weight: 400;
      font-size: 32rpx;
      color: #ffffff;

      &.primary {
        background: #11c685;
        border-radius: 10rpx;

        color: #ffffff;

        &:active {
          transform: scale(0.98);
        }
      }
    }
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .bank-card {
    height: 200rpx;
    padding: 24rpx;

    .bank-info .bank-header {
      .bank-icon {
        width: 40rpx;
        height: 40rpx;
      }

      .bank-name {
        font-size: 28rpx;
      }
    }

    .card-info .card-number {
      .card-dots {
        font-size: 24rpx;
      }

      .card-last-four {
        font-size: 32rpx;
      }
    }
  }
}
</style>
