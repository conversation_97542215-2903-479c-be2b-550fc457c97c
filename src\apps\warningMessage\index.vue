<template>
    <div class="early_warning">
        <z-paging ref="paging" :auto="false" v-model="dataList" @query="queryList">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="预警消息" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <div class="select_time">
                    <span class="title">日期时间</span>
                    <div class="time">
                        <div class="time_input" @click="clickTimePicker(true)" :class="{ placeholder: !searchState.startTime }">
                            {{ searchState.startTime || "开始时间" }}
                        </div>
                        <text class="zhi">至</text>
                        <div class="time_input" @click="clickTimePicker(false)" :class="{ placeholder: !searchState.endTime }">
                            {{ searchState.endTime || "结束时间" }}
                        </div>

                        <uni-icons type="clear" size="20" color="#ccc" style="margin-left: 10px" @click="clear" v-if="clearShow"></uni-icons>
                    </div>
                </div>
            </template>
            <template #empty>
                <yd-empty />
            </template>

            <div class="page_content">
                <div class="item" v-for="item in dataList" :key="item.id" @click="gotoInfo(item)">
                    <div class="title">
                        {{ eventTypeText[item.eventType] }}
                    </div>
                    <div class="item_id">事件ID：{{ item.eventId }}</div>
                    <div class="item_time">
                        <div class="content">事件发生时间：{{ item.happenTime }}</div>
                        <uni-icons type="right" size="18"></uni-icons>
                    </div>
                </div>
            </div>
        </z-paging>
        <uv-datetime-picker ref="datetimePicker" v-model="currentDate" mode="datetime" @confirm="confirm" confirmColor="var(--primary-color)"> </uv-datetime-picker>
    </div>
</template>

<script setup>
import dayjs from "dayjs"

const paging = ref(null)
const dataList = ref([])
const clearShow = ref(false)
const currentDate = ref(Number(new Date()))
const datetimePicker = ref(null)
const isStart = ref(true)
const searchState = ref({
    startTime: "",
    endTime: ""
})

const eventTypeText = {
    192515: "火点检测",
    192517: "温度报警",
    131605: "倒地",
    131596: "剧烈运动",
    131664: "人数异常",
    131593: "人员聚集",
    131590: "徘徊侦测",
    131586: "进入区域",
    131587: "离开区域",
    131585: "越界侦测",
    327937: "子系统布防",
    327938: "撤防",
    327941: "消警",
    327939: "防区旁路",
    327940: "旁路恢复",
    327681: "报警",
    327687: "紧急报警",
    131588: "区域入侵",
    131702: "人员超限"
}

watch(
    () => [searchState.value.startTime, searchState.value.endTime],
    (val) => {
        if (val && val[0] && val[1]) {
            const data1 = val[0]?.valueOf()
            const data2 = val[1]?.valueOf()
            console.log(data1 > data2)
            if (data1 > data2) {
                clear()
                uni.showToast({
                    title: "结束时间不得小于开始时间",
                    icon: "none"
                })
            } else {
                paging.value.reload()
            }
        }
        clearShow.value = val && (val[0] || val[1]) ? true : false
    },
    {
        immediate: true
    }
)

function gotoInfo(item) {
    navigateTo({
        url: "/apps/warningMessage/info",
        query: {
            id: item.id
        }
    })
}

const queryList = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize,
        ...searchState.value
    }
    http.post("/app/hk/message/page", params)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

function clear() {
    searchState.value.startTime = null
    searchState.value.endTime = null
    paging.value.reload()
}
function confirm(e) {
    const value = e.value
    if (value) {
        if (isStart.value) {
            searchState.value.startTime = dayjs(value).format("YYYY-MM-DD HH:mm")
        } else {
            searchState.value.endTime = dayjs(value).format("YYYY-MM-DD HH:mm")
        }
    } else {
        uni.showToast({
            title: "请选择时间",
            icon: "none"
        })
    }
}

function clickTimePicker(type) {
    isStart.value = type
    datetimePicker.value.open()
    console.log("nihao")
}

onMounted(() => {
    paging.value.reload()
})
</script>
<style lang="scss" scoped>
.early_warning {
    min-height: 100vh;
    background-color: $uni-bg-color-grey;
}
.page_content {
    padding: 20rpx;
    background-color: $uni-bg-color-grey;
}
.select_time {
    min-height: 124rpx;
    background: #ffffff;
    padding: 24rpx 30rpx;
    .title {
        font-weight: 400;
        font-size: 28rpx;
        color: #000000a6;
        line-height: 40rpx;
        text-align: left;
    }
    .time {
        display: flex;
        margin-top: 20rpx;
        align-items: center;
        justify-content: space-between;
        .zhi {
            font-weight: 400;
            font-size: 28rpx;
            color: rgba(0, 0, 0, 0.65);
            line-height: 40rpx;
            padding: 0rpx 16rpx;
        }
        .time_input {
            height: 70rpx;
            border: 1rpx solid $uni-border-color;
            border-radius: 10rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            color: #333;
        }
        .placeholder {
            color: #999;
        }
    }
}
.item {
    margin-bottom: 30rpx;
    min-height: 190rpx;
    background: #ffffff;
    box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(220, 245, 238, 0.5);
    border-radius: 20rpx;
    padding: 34rpx 30rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .title {
        font-weight: 600;
        font-size: 30rpx;
        color: #333333;
        line-height: 42px;
    }
    .item_id,
    .item_time {
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
        line-height: 34rpx;
        text-align: left;
        font-style: normal;
    }
    .item_time {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .content {
            width: 90%;
        }
    }
}
</style>
