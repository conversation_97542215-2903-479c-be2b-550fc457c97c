<template>
    <div class="details_page">
        <div class="page_content">
            <z-paging ref="paging">
                <template #top>
                    <!-- #ifdef MP-WEIXIN  -->
                    <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="pageTitle" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                    <!-- #endif -->
                    <div class="tabs_box">
                        <!--  #ifdef H5-WEIXIN || H5 -->
                        <uni-icons class="icon_class" color="#333333" type="left" size="22" @click="clickLeft"></uni-icons>
                        <!-- #endif -->
                        <!--  #ifdef APP-PLUS -->
                        <uni-icons class="icon_class" color="#333333" type="left" size="22" @click="clickLeft"></uni-icons>
                        <!-- #endif -->
                        <div class="tabs">
                            <div class="item_tabs" v-for="(item, index) in tabsList" :key="index" @click="changeTabs(item)" :class="active == item.value ? 'active' : ''">
                                {{ item.label }}
                            </div>
                        </div>
                    </div>
                    <div class="select_student" v-if="showStudent">
                        <div class="student" @click="changeStudent" v-if="state.selectList.length > 1">
                            <span class="student_name">{{ state.studentName }}</span>
                            <image class="select_img" src="@nginx/components/siftIcon.png" />
                        </div>
                        <div class="transparent_box"></div>
                    </div>
                </template>
                <div
                    class="com_content"
                    :style="{
                        borderRadius: showStudent && state.selectList.length > 1 ? '0rpx 60rpx 0rpx 0rpx' : '60rpx 60rpx 0rpx 0rpx'
                    }"
                >
                    <loyalty-card v-if="active == 'scoreCardCount'" :parame="parame" />
                    <medal v-if="active == 'medalCount'" :parame="parame" :medalTypeList="state.medalTypeList" />
                    <integration v-if="active == 'totalScore'" :parame="parame" />
                </div>
            </z-paging>
        </div>
        <yd-select-popup ref="selectPopupRef" title="请选择学生" :list="state.selectList" @closePopup="closePopup" :fieldNames="{ value: 'studentId', label: 'studentName' }" :selectId="[parame.personId]" />
    </div>
</template>

<script setup>
import { nextTick } from "vue"
import useStore from "@/store"
import LoyaltyCard from "./components/loyaltyCard.vue"
import Medal from "./components/medal.vue"
import Integration from "./components/integration.vue"
import { onLoad } from "@dcloudio/uni-app"
import { computed } from "vue"

const { user } = useStore()
const active = ref("scoreCardCount")
const parame = ref({})
const pageTitle = ref("积分卡")
const selectPopupRef = ref(null)
const tabsList = ref([
    {
        label: "积分卡",
        value: "scoreCardCount"
    },
    {
        label: "勋章",
        value: "medalCount"
    },
    {
        label: "积分",
        value: "totalScore"
    }
])
const state = reactive({
    selectList: [],
    medalTypeList: [],
    studentName: "",
    studentId: ""
})

const showStudent = computed(() => {
    return parame.value.isShowStudent == "true"
})

function clickLeft() {
    uni.navigateBack()
}

function changeTabs(item) {
    active.value = item.value
    pageTitle.value = item.label
}

onLoad((options) => {
    nextTick(() => {
        Object.keys(options).forEach((key) => {
            options[key] = decodeURIComponent(options[key])
        })
        active.value = options.type
        const obj = tabsList.value.find((item) => options.type == item.value)
        pageTitle.value = obj.label
        parame.value = options
        state.selectList = user.studentInfo
        state.studentName = user.studentInfo[0]?.studentName
        parame.value.personId = options.personId || user.studentInfo[0]?.studentId
    })
})

// 选择学生
function changeStudent() {
    selectPopupRef.value.open()
}

const closePopup = (val) => {
    if (!val) return
    state.studentName = val.studentName
    parame.value.personId = val.studentId
}
</script>

<style lang="scss" scoped>
.details_page {
    width: 100%;
    min-height: 100vh;
    background: $uni-bg-color;

    .page_content {
        // #ifdef APP-PLUS
        padding-top: var(--status-bar-height);
        // #endif
        // #ifdef H5-WEIXIN || H5
        height: 100vh;
        max-height: 100vh;
        // #endif
        // #ifdef  APP-PLUS
        height: 100vh;
        max-height: 100vh;
        // #endif

        // #ifdef MP-WEIXIN
        height: calc(100vh - 88rpx);
        max-height: calc(100vh - 88rpx);
        // #endif
        position: relative;
        width: 100vw;
        background: url("@nginx/workbench/evalActivity/parent/details_page_bg.png") no-repeat;
        background-size: cover;
        background-position: center;
    }

    .tabs_box {
        padding-top: 40rpx;
        position: relative;
        margin-bottom: 30rpx;

        .icon_class {
            position: absolute;
            left: 30rpx;
            top: 50%;
        }

        .tabs {
            margin: auto;
            width: 444rpx;
            height: 76rpx;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 38rpx;
            display: flex;
            align-items: center;
            padding: 0rpx 8rpx;

            .item_tabs {
                font-weight: 500;
                font-size: 34rpx;
                color: var(--primary-color);
                line-height: 60rpx;
                text-align: center;
                border-radius: 30rpx;
                width: 148rpx;
                height: 60rpx;
                background: rgba(255, 255, 255, 0);
            }

            .active {
                background: var(--primary-color);
                color: $uni-bg-color;
            }
        }
    }

    .select_student {
        display: flex;
        height: 82rpx;
        margin-bottom: -10rpx;

        .student {
            width: 300rpx;
            background: url("@nginx/workbench/evalActivity/selectStudent.png") no-repeat;
            background-size: contain;
            height: 82rpx;
            padding: 10rpx 0rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            display: flex;
            justify-content: flex-start;
            padding: 20rpx 0 0 50rpx;

            .select_img {
                width: 44rpx;
                height: 44rpx;
                flex-shrink: 0;
            }
        }
        .transparent_box {
            flex: 1;
            height: 100%;
        }
    }
    .com_content {
        background: $uni-bg-color;
        border-radius: 60rpx 60rpx 0rpx 0rpx;
        width: 100%;
        // #ifdef H5-WEIXIN || H5
        height: calc(100vh - 286rpx);
        max-height: calc(100vh - 286rpx);
        // #endif
        // #ifdef APP-PLUS
        height: calc(100vh - 286rpx);
        max-height: calc(100vh - 286rpx);
        // #endif
        // #ifdef MP-WEIXIN
        height: calc(100vh - 450rpx);
        max-height: calc(100vh - 450rpx);
        // #endif
        overflow-y: auto;
        padding: 30rpx 0rpx;
        position: absolute;
    }
}
</style>
