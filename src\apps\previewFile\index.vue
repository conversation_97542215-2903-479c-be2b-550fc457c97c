<template>
    <view class="privacy_policy">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="预览" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <!-- #ifdef MP-WEIXIN || APP-PLUS -->
        <web-view :src="url" title="预览" width="100%" height="100%" allowfullscreen loading="lazy" style="border-radius: 11px; padding: 4px; background-color: #f9faf9" frameborder="0" sandbox="allow-modals allow-pointer-lock allow-web-share allow-orientation-lock allow-screen-wake-lock allow-presentation allow-encrypted-media allow-autoplay allow-forms allow-popups allow-downloads allow-storage-access-by-user-activation allow-clipboard-write" referrerpolicy="same-origin" tabindex="0"></web-view>
        <!-- #endif  -->
        <!-- #ifdef H5 || H5-WEIXIN -->
        <iframe :src="url" title="预览" width="100%" height="100%" allowfullscreen loading="lazy" style="border-radius: 11px; padding: 4px; background-color: #f9faf9" frameborder="0" sandbox="allow-modals allow-pointer-lock allow-web-share allow-orientation-lock allow-screen-wake-lock allow-presentation allow-encrypted-media allow-autoplay allow-forms allow-popups allow-downloads allow-storage-access-by-user-activation allow-clipboard-write" referrerpolicy="same-origin" tabindex="0"></iframe>
        <!-- #endif -->
    </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
const url = ref("")
function clickLeft() {
    uni.navigateBack()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    url.value = options.url
})
</script>

<style lang="scss" scoped>
.privacy_policy {
    height: 100vh;
    width: 100vw;
}
</style>
