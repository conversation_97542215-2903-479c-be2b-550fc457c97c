<template>
    <div>
        <view class="personnel_info">
            <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="个人信息" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
            <view class="personal_header">
                <image class="personal_img" :src="info.avatar || '@nginx/chat/identity_teacher.png'" alt="" />
                <text>{{ info.name }}</text>
            </view>
            <view class="info">
                <div class="item">
                    <div class="title">姓名</div>
                    <span>{{ info.name }}</span>
                </div>
                <div v-if="info.type === 'dept'">
                    <div class="item">
                        <div class="title">手机</div>
                        <div class="phone">
                            <span
                                >{{ phone }}<span class="show" @click="showPhone">{{ isShow ? "隐藏" : "显示" }}</span></span
                            >
                            <!-- TODO: 打电话的功能 -->
                            <!-- <image src="@nginx/chat/phone.png" class="phone_img" alt="" /> -->
                        </div>
                    </div>
                    <div class="item">
                        <div class="title">邮箱</div>
                        <span>{{ info.email || "无" }}</span>
                    </div>
                    <div class="item">
                        <div class="title">性别</div>
                        <span>{{ genderText[info.gender] }}</span>
                    </div>
                    <div class="item">
                        <div class="title">教授科目</div>
                        <span>{{ info.subjectName || "暂无" }}</span>
                    </div>
                </div>
                <div v-else>
                    <div class="item">
                        <div class="title">手机</div>
                        <div>{{ info.phone || "暂无该学生的手机号码" }}</div>
                    </div>
                    <div class="item flex_phone">
                        <div class="title">监护人</div>
                        <div class="phone_box">
                            <div class="phone" v-for="i in info.elternAddBookDTOList" :key="i.id">
                                <text>
                                    {{ i.myPhone }}
                                    <text class="show" @click="showItemPhone(i)">{{ i.isShow ? "隐藏" : "显示" }}</text>
                                </text>
                                <!-- TODO: 打电话的功能 -->
                                <!-- <image src="@nginx/chat/phone.png" class="phone_img" alt="" /> -->
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="title">班级</div>
                        <text>{{ info.className || "无" }}</text>
                    </div>
                    <div class="item">
                        <div class="title">性别</div>
                        <text>{{ genderText[info.gender] }}</text>
                    </div>
                    <div class="item">
                        <div class="title">家庭住址</div>
                        <text>{{ info.address || "暂无" }}</text>
                    </div>
                </div>
            </view>
        </view>
    </div>
</template>

<script setup>
import { hideMiddleFour } from "@/utils"
const info = ref({})
const phone = ref("")
const isShow = ref(true)

const genderText = {
    "": "未知",
    null: "未知",
    1: "男",
    0: "女"
}

function showPhone() {
    isShow.value = !isShow.value
    if (!isShow.value) {
        phone.value = hideMiddleFour(info.value.phone)
    } else {
        phone.value = info.value.phone
    }
}
function showItemPhone(i) {
    i.isShow = !i.isShow
    if (!i.isShow) {
        i.myPhone = hideMiddleFour(i.phone)
    } else {
        i.myPhone = i.phone
    }
}

function clickLeft() {
    uni.navigateBack()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    info.value = options
    if (info.value.type == "dept") {
        phone.value = info.value.phone
        showPhone()
    } else {
        info.value.elternAddBookDTOList = JSON.parse(options.elternAddBookDTOList)
        info.value.elternAddBookDTOList = info.value.elternAddBookDTOList.map((i) => {
            return {
                ...i,
                myPhone: hideMiddleFour(i.phone),
                isShow: false
            }
        })
    }
})
</script>

<style lang="scss" scoped>
.personnel_info {
    background: $uni-bg-color-grey;
    min-height: 100vh;
}
.personal_header {
    min-height: 180rpx;
    background: $uni-bg-color;
    display: flex;
    align-items: center;
    padding: 0rpx 30rpx;
    font-weight: 600;
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    .personal_img {
        width: 100rpx;
        height: 100rpx;
        margin-right: 20rpx;
        border-radius: 20rpx;
    }
}
.info {
    margin-top: 20rpx;
    display: flex;
    flex-direction: column;
    background: $uni-bg-color;
    min-height: 300rpx;
    padding: 30rpx;
    .item {
        padding: 30rpx 0rpx;
        display: flex;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
        align-items: center;
        .title {
            width: 150rpx;
        }
        .phone_box {
            display: flex;
            flex-direction: column;
            flex: 1;
        }
        .phone {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .phone_img {
                width: 60rpx;
                height: 60rpx;
            }
            .show {
                padding-left: 20rpx;
                color: var(--primary-color);
            }
        }
    }
    .flex_phone {
        align-items: flex-start;
        .title {
            margin-top: 8rpx;
        }
    }
}
</style>
