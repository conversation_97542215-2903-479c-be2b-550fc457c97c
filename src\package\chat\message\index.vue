<template>
    <div class="message_page">
        <z-paging ref="paging" @query="queryList" v-model="messageList">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="消息通知" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
                    <!-- #ifdef MP-WEIXIN -->
                    <view class="bar_title">
                        消息通知
                        <image @click="receivingManage" class="message_set_icon" src="@nginx/chat/message_set.png" alt="" />
                    </view>
                    <!-- #endif -->
                    <!-- #ifdef H5 || H5-WEIXIN -->
                    <template #right>
                        <image @click="receivingManage" class="message_set_icon" src="@nginx/chat/message_set.png" alt="" />
                    </template>
                    <!-- #endif -->
                </uni-nav-bar>
            </template>
            <div class="message_list">
                <div class="message_item" v-for="item in messageList" :key="item.id">
                    <div class="time" v-if="item.sendTime">{{ item.sendTime }}</div>
                    <div class="box">
                        <div class="title_box">
                            <image class="massge_icon" src="@nginx/chat/message_logo.png" alt="" />
                            <text class="title">{{ moduleTitle[item.moduleType] || "消息通知" }}</text>
                        </div>
                        <!-- 场地预约 -->
                        <div class="content_box" v-if="item.moduleType === 12">
                            <text v-if="item.firstData" class="content_title">{{ item.firstData }}</text>
                            <text class="content">
                                <div class="text" v-for="[key, value] of jsonP(item)" :key="key">
                                    {{ value }}
                                </div>
                            </text>
                        </div>
                        <div class="content_box" v-else>
                            <text v-if="item.title" class="content_title">{{ item.title }}</text>
                            <text class="content"> {{ item.content }} </text>
                        </div>
                        <!-- TODO: 查看详情跳转子应用相关的页面 -->
                        <div class="look_details" @click="detailsPage(item)" v-if="isHave.includes(item.moduleType)">
                            <text>查看详情</text>
                            <uni-icons type="right" size="18" color="var(--primary-color)"></uni-icons>
                        </div>
                    </div>
                </div>
            </div>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </div>
</template>

<script setup>
const paging = ref(null)
const messageList = ref([])

const moduleTitle = {
    1: "闸机推送",
    2: "考勤",
    3: "考勤周报",
    4: "请假",
    5: "通知公告",
    6: "失物招领",
    7: "作业",
    8: "人脸采集",
    9: "日程提醒",
    10: "会议提醒",
    11: "课后作业布置提醒",
    12: "场地预约通知",
    13: "收集表",
    14: "OA审批",
    15: "个人信息录入通知",
    16: "系统通知",
    17: "考试成绩通知",
    18: "活动报名状态变更通知",
    19: "宿舍考勤通知",
    20: "场地预约通知",
    21: "学生通行",
    22: "聊天",
    23: "访客"
}

function jsonP(item) {
    if (item && item.content) {
        return Object.entries(JSON.parse(item.content))
    } else {
        return []
    }
}

// 调用List数据
function queryList(pageNo, pageSize) {
    http.post("/app/v2/push/pageUserMessage", { pageNo, pageSize }).then(({ data }) => {
        paging.value.complete(data.list)
    })
}

// 可以跳转详情的路由
const isHave = [1, 5, 3, 7, 12, 13, 14, 15, 17, 20, 23]

/**
 * @description: 消息详情
 * @customParams 自定义参数
 * @moduleType 消息类型 1闸机推送/通行 2考勤 3考勤周报 4请假 5通知公告 6失物招领 7作业发布 8人脸采集 9日程提醒 10会议 11发布作业 12场地预约详情 13收集详情 14审批详情 15完善个人信息 16 系统消息 17成绩通知 18 活动报名 19 宿舍管理 20 场地预约 21 学生通行 22聊天 23 访客
 * @callId 详情ID
 * @return {*}
 */
function detailsPage(item) {
    const customParams = item && item.customParams ? JSON.parse(item.customParams) : {} // 自定义参数
    const urlObj = {
        1: "/apps/traffic/index", // 通行
        5: "/apps/notice/announcement/details", // 通知公告详情
        3: "/apps/weeklyPublication/index", // 周报
        7: "/apps/schoolAssignment/parent/detail", // 作业详情
        12: "/apps/siteBooking/bookingDetail", // 场地预约详情
        13: "/apps/collectTable/fillIn/createForm", // 收集表
        14: "/package/chat/todo/oaApproval", // OA审批
        15: "/package/my/myInfo/perfectMore", // 完善个人信息
        17: "/apps/scoreManage/scoreDetails", // 成绩详情
        20: "/apps/siteBooking/index", // 场地预约
        23: "/apps/visitorSystem/details" // 访客系统详情
    }
    const queryObj = {
        5: { id: item.callId, type: "receive", contentType: 0, messType: 1 },
        7: { id: item.callId }, // 作业详情
        12: { id: item.callId, examineStatus: 0, isExamine: true }, // 场地预约详情
        13: { id: item.callId, isEdit: false }, // 收集表
        14: { id: item.callId, status: 1 }, // OA审批
        17: { customParams: JSON.stringify(customParams) }
    }
    console.log(item, queryObj[item.moduleType], urlObj[item.moduleType])
    navigateTo({
        url: urlObj[item.moduleType],
        query: queryObj[item.moduleType] || {}
    })
}

function receivingManage() {
    navigateTo({
        url: "/package/chat/message/receivingManage"
    })
}
</script>

<style lang="scss" scoped>
.page_title {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-weight: 500;
    font-size: 34rpx;
    color: #000000;
    line-height: 88rpx;
    .message_set_icon {
        margin-left: 4rpx;
    }
}
.message_page {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .message_set_icon {
        width: 44rpx;
        height: 44rpx;
    }
    .message_list {
        padding: 30rpx;
        .message_item {
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            width: 100%;
            .time {
                font-weight: 400;
                margin-bottom: 10rpx;
                font-size: 24rpx;
                color: #b3b3b3;
                line-height: 34rpx;
                text-align: center;
            }
            .box {
                padding: 40rpx 30rpx;
                width: calc(100% - 80rpx);
                min-height: 100rpx;
                background: $uni-bg-color;
                border-radius: 20rpx;
                margin-bottom: 30rpx;

                .title_box {
                    display: flex;
                    align-items: center;
                    padding-bottom: 30rpx;
                    margin-bottom: 30rpx;
                    border-bottom: 1rpx solid $uni-border-color;
                    .massge_icon {
                        width: 44rpx;
                        height: 44rpx;
                        margin-right: 10rpx;
                    }
                    .title {
                        font-weight: 600;
                        font-size: 30rpx;
                        color: $uni-text-color;
                        line-height: 42rpx;
                    }
                }
                .content_box {
                    display: flex;
                    flex-direction: column;
                    .content_title {
                        font-weight: 600;
                        font-size: 30rpx;
                        color: $uni-text-color;
                        line-height: 42rpx;
                        margin-bottom: 30rpx;
                    }
                    .content {
                        font-weight: 400;
                        font-size: 26rpx;
                        color: $uni-text-color;
                        line-height: 36rpx;
                        .text {
                            margin-bottom: 12rpx;
                        }
                    }
                }
                .look_details {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    margin-top: 30rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: var(--primary-color);
                    line-height: 40rpx;
                    text-align: right;
                }
            }
        }
    }
}
.bar_title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: 500;
    font-size: 32rpx;
    color: $uni-text-color;
    line-height: 48rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    .message_set_icon {
        margin-left: 10rpx;
    }
}
</style>
