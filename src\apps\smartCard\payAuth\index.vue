<template>
    <div class="loading">授权中...</div>
</template>

<script setup>
import { onMounted } from 'vue';
// 获取浏览器参数
const getUrlParams = (isShare, _url) => {
    const params = {};
    const url = _url || window.location.search || window.location.hash;
    if (url.indexOf("?") > -1) {
        const str = url.split("?")[1];
        const arr = str.split("&");
        arr.forEach(item => {
            const [key, value] = item.split("=");
            params[key] = value;
        });
        return params;
    } else {
        return params;
    }
};
const getWeiXinCode = () => {
    const query = getUrlParams();
    if (query.code) {
        // 经过二次跳转 如果url上存在code
        navigateTo({
            url: '/apps/smartCard/recharge/orderPayment',
            query: {
                code: query.code,
            }
        })
    } else {
        // 如果没有code 说明是执行的第一次onMounted
        // 目的是把back_url 放在微信的state参上 记住业务逻辑等会回哪去
        const redirect_uri = encodeURIComponent(`${window.location.href}`)
        console.log({ redirect_uri });
        window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${query.appId}&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_base&state=123`;
    }
}
// window.location.href 调用会重新执行onMounted
// 第一次执行的目的是为了 获取路由参数 back_url
// 第二次执行是为了 直接去back_url
onMounted(() => {
    getWeiXinCode()
})
</script>

<style scoped>
.loading {
    font-size: 14px;
    color: #00B781;
}
</style>