<!-- 报名表格 -->
<template>
    <view class="registrationPage">
        <!-- 头部 -->
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="报名表" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <!-- 筛选 -->
        <view class="list-option">
            <drop-down :menu="menu" style="width: 100%" ref="dropDownRef" :isNameSlot="true">
                <template v-slot:sort>
                    <view class="sortBox">
                        <view class="sortBox_time">
                            <view class="sortBox_time_title">报名时间</view>

                            <view class="sortBox_time_icon" @click="sortBtncreateTime">
                                {{ state.createTimeorder ? "近到远" : "远到近" }}

                                <uv-icon v-if="state.createTimeorder" name="arrow-upward" color="var(--primary-color)" size="16"></uv-icon>

                                <uv-icon v-else name="arrow-downward" color="var(--primary-color)" size="16"></uv-icon>
                            </view>
                        </view>

                        <view class="sortBox_name">
                            <view class="sortBox_name_title">子项目名称</view>

                            <view class="sortBox_name_icon" @click="sortBtnsubName">
                                {{ state.subNameorder ? "小到大" : "大到小" }}

                                <uv-icon v-if="state.subNameorder" name="arrow-upward" color="var(--primary-color)" size="16"></uv-icon>

                                <uv-icon v-else name="arrow-downward" color="var(--primary-color)" size="16"></uv-icon>
                            </view>
                        </view>

                        <view class="btnBox">
                            <view class="restBox" @click="sortResBtn">
                                <text class="restBox_name">重置</text>
                            </view>

                            <view class="submitBox" @click="submitResBtn">
                                <text class="submitBox_name">确定</text>
                            </view>
                        </view>
                    </view>
                </template>
                <template v-slot:filtrate>
                    <view class="filtrateBox">
                        <view class="filtrateBox_name"> 姓名 </view>
                        <view class="filtrateBox_input">
                            <uv-input
                                :customStyle="{
                                    height: '80rpx',
                                    background: '#f6f6f6',
                                    borderRadius: '12rpx'
                                }"
                                placeholder="请输入"
                                border="none"
                                v-model="state.userName"
                            ></uv-input>
                        </view>

                        <view class="filtrateBox_name"> 活动子项 </view>
                        <view class="filtrateBox_input">
                            <uv-input
                                :customStyle="{
                                    height: '80rpx',
                                    background: '#f6f6f6',
                                    borderRadius: '12rpx'
                                }"
                                placeholder="请输入"
                                border="none"
                                v-model="state.subName"
                            ></uv-input>
                        </view>

                        <view class="btnBox" style="padding-top: 30rpx">
                            <view class="restBox" @click="restName">
                                <text class="restBox_name">重置</text>
                            </view>

                            <view class="submitBox" @click="submitName">
                                <text class="submitBox_name">确定</text>
                            </view>
                        </view>
                    </view>
                </template>
            </drop-down>
        </view>
        <!-- 滑动加载区域 -->
        <scroll-view v-if="state.activityCardList && state.activityCardList.length" class="scroll-Y" :scrollTop="state.scrollTop" :show-scrollbar="true" :lower-threshold="100" :scroll-y="true" :scroll-with-animation="true" @scrolltolower="handlerScrollBottom">
            <view class="achievement-item" v-for="item in state.activityCardList" :key="item.id">
                <view class="itemBox">
                    <view class="itemBox_item">
                        <view class="itemBox_title">报名对象</view>
                        <view class="itemBox_name">{{ item.userName }}</view>
                    </view>
                    <view class="itemBox_item">
                        <view class="itemBox_title">报名状态</view>
                        <view
                            class="itemBox_name"
                            :style="{
                                color: gettablestatus(item.status).color
                            }"
                            >{{ gettablestatus(item.status).text }}
                            <text v-if="item.remarks" class="remarks">（{{ item.remarks }} ）</text>
                        </view>
                    </view>
                    <view class="itemBox_item">
                        <view class="itemBox_title">所属组织</view>
                        <view class="itemBox_name">{{ item.orgNames }}</view>
                    </view>
                    <view class="itemBox_item">
                        <view class="itemBox_title">报名子项</view>
                        <view class="itemBox_name">{{ item.subName }}</view>
                    </view>
                    <view class="itemBox_item">
                        <view class="itemBox_title">报名时间</view>
                        <view class="itemBox_name">{{ item.createTime }}</view>
                    </view>
                </view>
            </view>

            <uni-load-more :showText="state.loadMore.showText" :contentText="state.loadMore.contentText" :status="state.loadMore.status" @clickLoadMore="state.scrollTop = 0" />
        </scroll-view>
        <yd-empty text="暂无数据" :isMargin="true" v-else />
    </view>
</template>
<script setup>
import DropDown from "./components/dropDown.vue"

const dropDownRef = ref(null)

const menu = ref({
    sort: {
        label: "排序"
    },
    filtrate: {
        label: "筛选"
    }
})

const tablestatus = {
    1: {
        text: "报名成功",
        color: "var(--primary-color)"
    },
    2: {
        text: "报名失败",
        color: "#FD4F45"
    },

    default: {
        text: "-",
        color: ""
    } // 默认值存储在 default 属性中
}

const state = ref({
    back: "",
    sortObj: {},
    subNameorder: true,
    createTimeorder: true,
    activityCardList: [],
    loadMore: {
        status: "more", // more	加载前, loading	加载中, no-more	没有更多数据
        showText: true, // 显示文本
        contentText: {
            contentdown: "点击加载更多"
        }
    },
    pagination: {
        pageNo: 1,
        pageSize: 500,
        total: 0
    },
    scrollTop: 0,
    saveparameter: {}
})

function mergeArrays(arr1, arr2) {
    let mergedArray = Array.from(new Set(arr1.concat(arr2).map(JSON.stringify))).map(JSON.parse)
    return mergedArray
}

function gettablestatus(key) {
    return tablestatus[key] || tablestatus.default
}

const getList = (optObj) => {
    const params = {
        activityId: state.value.saveparameter.activityId,
        ...state.value.pagination,
        userName: state.value.userName,
        subName: state.value.subName,
        ...optObj,
        studentId: state.value.saveparameter?.studentId || undefined
    }

    if (state.value.saveparameter?.userType === "1") {
        delete params.studentId
    }

    http.post("/app/activity/enrollUser/page", params).then(({ data }) => {
        const { list, pageNo, pageSize, total } = data
        state.value.pagination.pageNo = pageNo
        state.value.pagination.pageSize = pageSize
        state.value.pagination.total = total
        state.value.activityCardList = mergeArrays(state.value.activityCardList, list)
        if (pageNo * pageSize >= total) {
            state.value.loadMore.status = "no-more"
        } else {
            state.value.loadMore.status = "more"
        }
    })
}

// 重置数据
const resetList = (obj) => {
    state.value.activityCardList = []
    state.value.pagination.pageNo = 1
    state.value.pagination.total = 0
    getList(obj)
}

const restName = () => {
    state.value.userName = null
    state.value.subName = null
    const [_, ref1] = dropDownRef.value.getRefs()
    ref1.value.close()

    resetList()
    state.value.scrollTop = 0
}

const submitName = () => {
    const [_, ref1] = dropDownRef.value.getRefs()
    ref1.value.close()

    resetList()
    state.value.scrollTop = 0
}

// 滚动加载
const handlerScrollBottom = (e) => {
    state.value.scrollTop = e.target.offsetTop
    const { pageNo, pageSize, total } = state.value.pagination
    if (pageNo * pageSize <= total) {
        state.value.loadMore.status = "loading"
        state.value.pagination.pageNo++
        getList()
    }
}

const sortBtnsubName = () => {
    state.value.subNameorder = !state.value.subNameorder
    state.value.sortObj = {
        field: "subName",
        order: state.value.subNameorder ? "asc" : "desc"
    }
}

const sortBtncreateTime = () => {
    state.value.createTimeorder = !state.value.createTimeorder
    state.value.sortObj = {
        field: "createTime",
        order: state.value.createTimeorder ? "desc" : "asc"
    }
}

const sortResBtn = () => {
    state.value.subNameorder = true
    state.value.createTimeorder = true

    state.value.sortObj = {}

    const [_, ref1] = dropDownRef.value.getRefs()
    ref1.value.close()
    resetList()
    state.value.scrollTop = 0
}

const submitResBtn = () => {
    const [_, ref1] = dropDownRef.value.getRefs()
    ref1.value.close()
    resetList(state.value.sortObj)
    state.value.scrollTop = 0
}

onShow(() => {
    getList()
})

onLoad(async (options) => {
    console.log("报名表路由参数", options)
    state.value.saveparameter = options
    if (options.customParams) {
        const { studentId } = JSON.parse(options.customParams)
        state.value.saveparameter.studentId = studentId
    }
    if (options.source === "message") {
        state.value.back = `/${options.back}`
    }
})

function clickLeft() {
    uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.registrationPage {
    min-height: 100vh;
    background-color: $uni-bg-color-grey;
}
.title_box {
    width: 100%;
    text-align: center;
    line-height: 88rpx;
    font-size: 34rpx;
    font-weight: 500;
    color: $uni-text-color;
}

.scroll-Y {
    .achievement-item {
        margin: 24rpx 32rpx;
        padding: 24rpx;
        background-color: $uni-text-color-inverse;
        border-radius: 12rpx;
    }
}

.list-option {
    background-color: $uni-text-color-inverse;
    padding-left: 144rpx;
    padding-right: 144rpx;
}

.itemBox .itemBox_item:not(:last-child) {
    padding-bottom: 20rpx;
}

.itemBox_item {
    display: flex;
    align-items: center;
    .itemBox_title {
        padding-right: 100rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        flex-shrink: 0;
    }
    .itemBox_name {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
    }
}

.filtrateBox {
    padding: 0rpx 30rpx;

    .filtrateBox_name {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        padding-top: 16rpx;
        padding-bottom: 16rpx;
    }
}

.sortBox {
    padding: 0rpx 30rpx;
    .sortBox_time {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx 0rpx;
        border-bottom: 1rpx solid $uni-border-color;
        .sortBox_time_title {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
        }
        .sortBox_time_icon {
            display: flex;
            font-weight: 400;
            font-size: 28rpx;
            color: var(--primary-color);
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
        }
    }
    .sortBox_name {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx 0rpx;
        .sortBox_name_title {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
        }
        .sortBox_name_icon {
            display: flex;
            font-weight: 400;
            font-size: 28rpx;
            color: var(--primary-color);
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
        }
    }
}

.btnBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 30rpx;
    .restBox {
        display: flex;
        align-items: center;
        justify-content: center;

        height: 92rpx;
        background: $uni-bg-color;
        border-radius: 10rpx;
        border: 2rpx solid $uni-border-color;
        flex: 50%;
        margin-right: 15px;
        .restBox_name {
            font-weight: 400;
            font-size: 32rpx;
            color: #666666;
            line-height: 44rpx;
            text-align: center;
            font-style: normal;
        }
    }
    .submitBox {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 92rpx;
        background: var(--primary-color);
        border-radius: 10rpx;
        flex: 50%;
        .submitBox_name {
            font-weight: 400;
            font-size: 32rpx;
            color: $uni-text-color-inverse;
            line-height: 44rpx;
            text-align: center;
            font-style: normal;
        }
    }
}

.remarks {
    font-weight: 400;
    font-size: 28rpx;
    color: $uni-text-color;
}
</style>
