<template>
    <view class='view_attachment'>
        <NavBar title="查看附件" :clickLeft="clickLeft" />
        <view class="content">
            <uv-loading-icon class="uv_loading" />
            <!-- #ifdef MP-WEIXIN || APP-PLUS -->
            <web-view :src="url" title="预览" width="100%" height="100%" allowfullscreen loading="lazy"
                class="a4-pageiframe" frameborder="0"
                sandbox="allow-modals allow-pointer-lock allow-web-share allow-orientation-lock allow-screen-wake-lock allow-presentation allow-encrypted-media allow-autoplay allow-forms allow-popups allow-downloads allow-storage-access-by-user-activation allow-clipboard-write"
                referrerpolicy="same-origin" tabindex="0"></web-view>
            <!-- #endif  -->
            <!-- #ifdef H5 || H5-WEIXIN -->
            <iframe v-if="state.ifromPreviewUrls" :src="state.ifromPreviewUrls" frameborder="0"
                class="a4-pageiframe"></iframe>
            <!-- #endif -->
            <!-- <template v-if="!state.ifromPreviewUrls">
                <view class="qz-item" v-for="(item, idx) in state.previewUrls" :key="idx" :id="`page-${idx}`">
                    <img :src="item" alt="" class="a4-page">
                </view>
                <yd-empty class="empty" v-if="!state.previewUrls.length">暂无数据</yd-empty>
            </template> -->
        </view>
    </view>
</template>

<script setup>
import NavBar from "./components/navBar.vue"
const state = reactive({
    id: '',
    operate: "",
    type: '',
    showType: '',
    previewUrls: [],
    ifromPreviewUrls: '',
})
function clickLeft() {
    uni.navigateBack()
}
const initPage = async () => {
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    const { data } = await http.post("/cloud/official-doc/document/preview", { documentId: state.id })
    state.previewUrls = data.previewUrls
    uni.hideToast()
}


onLoad(async (item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.operate = item.operate || ''
    state.id = item.id
    state.type = item.type
    state.showType = item.showType
    if (item.previewUrls) {
        state.ifromPreviewUrls = item.previewUrls
    } else {
        initPage()
    }
})
</script>

<style lang='scss' scoped>
.view_attachment {
    .content {
        margin: 20rpx 0 140rpx;
        background-color: $uni-text-color-inverse;
        position: relative;

        .a4-page,
        .qz-item {
            width: 100vw;
        }

        .empty {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .uv_loading {
        position: absolute;
        top: 50%;
        left: 50%;
        margin-top: 50%;

    }

    .a4-pageiframe {
        width: 100vw;
        height: calc(100vh - 80rpx);
        position: absolute;
        top: 0;
        left: 0;
    }
}
</style>