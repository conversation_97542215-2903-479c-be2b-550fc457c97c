<!doctype html>
<html lang="ch">
    <head>
        <meta charset="UTF-8" />
        <meta name="version" content="<%=version%>" />
        <script>
            var coverSupport = "CSS" in window && typeof CSS.supports === "function" && (CSS.supports("top: env(a)") || CSS.supports("top: constant(a)"))
            document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ", viewport-fit=cover" : "") + '" />')
        </script>
        <link rel="stylesheet" href="https://file.1d1j.cn/cloud-mobile/iconfont/font_3184698_ljh90qv37d/iconfont.css" />
        <link rel="stylesheet" href="./src//styles/iconfont.css" />
        <title></title>

        <!-- 引入 css -->
        <link href="https://file.1d1j.cn/cloud-mobile/css/style.css" rel="stylesheet" />

        <!-- highlight.js  -->
        <link rel="preload" href="https://file.1d1j.cn/cloud-mobile/js/default.min.css" as="style" onload="this.rel='stylesheet'" />
        <script src="https://file.1d1j.cn/cloud-mobile/js/highlight.min.js"></script>
        <!-- katex -->
        <link rel="preload" href="https://file.1d1j.cn/cloud-mobile/css/katex.css" as="style" onload="this.rel='stylesheet'" />
        <script src="https://file.1d1j.cn/cloud-mobile/js/auto-render.min.js"></script>
        <script src="https://file.1d1j.cn/cloud-mobile/js/katex.mjs"></script>

        <!-- cropperjs -->
        <link href="https://file.1d1j.cn/cloud-mobile/css/cropper.min.css" rel="stylesheet" />
        <script src="https://file.1d1j.cn/cloud-mobile/js/cropper.min.js"></script>
        <!--preload-links-->
        <script src="https://file.1d1j.cn/cloud-mobile/js/base64.js"></script>
        <!--app-context-->
    </head>

    <body>
        <div id="app"><!--app-html--></div>
        <!-- 滑块验证 -->
        <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
        <script src="https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.3/index.js"></script>
        <script type="module" src="/src/main.js"></script>
    </body>
</html>
