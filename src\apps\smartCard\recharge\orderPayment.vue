<template>
    <!-- 订单支付 orderPayment-->
    <view class="order_payment">
        <NavBar title="订单支付" :clickLeft="clickLeft" />
        <view class="content">
            <view class="money-time">
                <view class="money">
                    <text style="font-size: 36rpx; margin-right: -10rpx">¥</text>
                    {{ payAmount(state.form.payAmount) }}
                </view>
                <view class="time-tips">
                    <text>支付剩余时间 {{ state.time }}</text>
                </view>
            </view>
            <view class="list-box">
                <uni-list-item title="订单信息" :border="false">
                    <template v-slot:footer>
                        <view>{{ state.infos }}</view>
                    </template>
                </uni-list-item>
            </view>
            <view class="pay-select" v-if="state.isBocomCard">
                <view class="label-box">快捷支付</view>
                <view class="list-box">
                    <radio-group @change="quickRadioChange($event)">
                        <view class="list-item border" v-for="item in state.bocomCardList" :key="item.id">
                            <uni-list-item :title="item.bankName" :border="false">
                                <template v-slot:header>
                                    <image class="slot-image" :src="bankConfig[item.bankCode].icon" mode="widthFix" />
                                </template>
                                <template v-slot:footer>
                                    <radio :value="JSON.stringify(item)" :disabled="!state.infos"
                                        :checked="item.id === state.activePayMethodId" color="#00b781" />
                                </template>
                            </uni-list-item>
                        </view>
                    </radio-group>
                    <view @click="handlerBankCard">
                        <uni-list-item title="添加银行卡" :border="false" showArrow />
                    </view>
                </view>
            </view>

            <view class="pay-select" v-if="state.payMethodList.length">
                <view class="label-box">更多支付</view>
                <view class="list-box">
                    <radio-group @change="radioChange($event)">
                        <view class="list-item border" v-for="item in state.payMethodList" :key="item.configTypeId">
                            <uni-list-item :title="item.payMethodName" :border="false">
                                <template v-slot:header>
                                    <image class="slot-image" :src="payIcon[item.payMethodId]" mode="widthFix"></image>
                                </template>
                                <template v-slot:footer>
                                    <radio :value="JSON.stringify(item)" :disabled="!state.infos"
                                        :checked="item.payMethodId === state.activePayMethodId" color="#00b781" />
                                </template>
                            </uni-list-item>
                        </view>
                    </radio-group>
                </view>
            </view>

            <yd-empty style="margin-top: 30%;" text="暂无可支付方式，请联系管理员开通"
                v-if="!state.payMethodList.length && !state.isBocomCard" />

        </view>
        <view class="footer">
            <button class="mini-btn" type="primary" :disabled="!state.activePayMethodId" :loading="state.loadingSave"
                @click="handleSave">确定支付</button>
        </view>

        <uni-popup ref="orderTipPopup" border-radius="10px" background-color="#fff" :is-mask-click="false">
            <view class="popup-content">
                <view class="header">
                    <text class="title">请确认微信支付是否已完成</text>
                    <uni-icons class="close" type="closeempty" size="20" @click="orderTipPopup.close()"></uni-icons>
                </view>
                <view class="body" @click="handerCompletedPay('payment')"> 已完成支付 </view>
                <view class="footer-btn" @click="handerCompletedPay('repay')"> 支付遇到问题，重新支付 </view>
            </view>
        </uni-popup>
        <img v-if="isGuideMap" src="https://file.1d1j.cn/cloud-mobile/smartCard/guideMap.png" alt="引导图"
            class="guide-map" @click="isGuideMap = false" />
        <!-- 支付密码验证弹窗 -->
        <PayPasswordVerify v-model:visible="showPasswordVerify" @success="onPasswordVerifySuccess" />

    </view>
</template>

<script setup>
import dayjs from "dayjs"
import useStore from "@/store"
import { checkPlatform } from "@/utils/sendAppEvent.js"
import { payAmount } from "../components/index.js"
import NavBar from "../components/navBar.vue"
import { isWechatBrowser } from "@/utils/wechat.js"
import PayPasswordVerify from "../components/PayPasswordVerify.vue";
import bankConfig from "../components/bankConfig.js"
const { useSmartCard } = useStore()
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)

const orderTipPopup = ref(null)
const isGuideMap = ref(false)
const showPasswordVerify = ref(false);

const payIcon = ["https://file.1d1j.cn/cloud-mobile/smartCard/alipay.png", "https://file.1d1j.cn/cloud-mobile/smartCard/weChat.png", "https://file.1d1j.cn/cloud-mobile/smartCard/intracloud.png"]

const state = reactive({
    payMethodList: [], // 微信支付
    bocomCardList: [], // 银行卡支付
    activePayMethodId: "",
    infos: "",
    form: {
        tradeNo: "",
        payAmount: null,
        payMethodId: 0,
        configTypeId: "",
        openid: "",
        payType: 4, // 4：h5支付 1:公众号支付 5：小程序支付
        paySource: "h5支付"
    },
    hour: "0",
    minute: "0",
    second: "0",
    loadingSave: false,
    tradeNo: "",
    h5Url: "",
    appId: "",
    studentId: "",
    time: '00:00:00',
    isBocomCard: true
})


// 去添加银行卡
const handlerBankCard = () => {
    useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/recharge/orderPayment'])
    uni.navigateTo({
        url: "/apps/smartCard/bankCard/addBankCard"
    })
}

function onBridgeReady(params) {
    window.WeixinJSBridge.invoke("getBrandWCPayRequest", params, function (res) {
        if (res.err_msg == "get_brand_wcpay_request:ok") {
            console.log("支付成功")
            orderTipPopup.value.open()
            uni.removeStorageSync('setForm')
        } else {
            console.log("支付失败")
        }
    }),
        function (res) {
            if (res.err_msg == "get_brand_wcpay_request:ok") {
                orderTipPopup.value.open()
                // 使用以上方式判断前端返回,微信团队郑重提示：
                //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠，商户需进一步调用后端查单确认支付结果。
            }
        }
}


// 支付完成后 再手动判定是否确认完成支付
const handerCompletedPay = (item) => {
    http.post("/unicard/app/pay-center/order-pay-query", { tradeNo: state.form.tradeNo || state.tradeNo }).then(({ data }) => {
        //  0.处理中 1.已支付 2.支付失败/未支付
        if ([0, 1].includes(data.payResStatus)) {
            uni.showToast({
                title: data.payResStatus ? "充值已成功" : "充值处理中",
                icon: "none",
                duration: 2000
            })
            orderTipPopup.value.close()
            uni.reLaunch({
                url: `/apps/smartCard/index`
            })
        } else {
            if (item === "repay") {
                handleSave()
            } else {
                uni.showToast({
                    title: "该订单未完成支付，请进行支付。",
                    icon: "none",
                    duration: 2000
                })
            }
        }
    })
}
// 确定支付按钮
const handleSave = () => {
    // 微信支付宝提交支付
    if (state.form.payMethodId == 1) {
        handleSaveWeixinPay()
    } else if (state.form.payMethodId == 3) {
        // 银行卡支付
        showPasswordVerify.value = true;
    }
}

// 支付密码验证成功回调
const onPasswordVerifySuccess = (password) => {
    const param = {
        ...state.form,
        passwordEncipher: encrypt(JSON.stringify(password))
    }
    uni.showLoading({
        title: "充值处理中...",
        mask: true,
    });
    http.post("/unicard/app/pay-center/pay-submit", param)
        .then(({ data }) => {
            const { payMethodId, payResStatus, tradeNo } = data
            if (payMethodId == 3 && payResStatus == 1) {
                uni.showToast({
                    title: "充值已成功",
                    icon: "none",
                    duration: 2000
                })
                uni.reLaunch({
                    url: `/apps/smartCard/index`
                })
            } else {
                state.tradeNo = tradeNo
                handerCompletedPay('payment')
            }
            uni.hideLoading();
        })
};
// 获取支付方式
const initPage = async () => {
    const params = { payType: "", personId: state.form.personId }
    state.payMethodList = []
    const { data } = await http.post("/unicard/app/pay-center/pay-method-list", params)
    state.isBocomCard = false
    data?.forEach(v => {
        // 微信支付
        if (v.payMethodId === 1) {
            state.payMethodList.push(v)
        }
        // 银行卡支付
        if (v.payMethodId === 3) {
            state.isBocomCard = true
            state.bocomCardList = v.bocomCardList.map(k => {
                return {
                    ...k, payMethodId: v.payMethodId,
                    configTypeId: v.configTypeId
                }
            })
        }
    });
    // 优先默认选中更多支付中的第一个吧
    if (state.payMethodList.length > 0) {
        state.activePayMethodId = state.payMethodList[0].payMethodId
        state.form.payMethodId = state.payMethodList[0].payMethodId
        state.form.configTypeId = state.payMethodList[0].configTypeId
        return
    }
    if (state.bocomCardList.length > 0) {
        state.activePayMethodId = state.bocomCardList[0].id
        state.form.payMethodId = state.bocomCardList[0].payMethodId
        state.form.configTypeId = state.bocomCardList[0].relatedId
    }
}
let countdownTimer = null

const startCountdown = (payEndTime) => {
    clearInterval(countdownTimer)
    countdownTimer = setInterval(() => {
        const endTime = dayjs(payEndTime)
        const now = dayjs()
        const diff = endTime.diff(now, 'second') // 剩余秒数

        if (diff > 0) {
            const hours = Math.floor(diff / 3600)
            const minutes = Math.floor((diff % 3600) / 60)
            const seconds = diff % 60
            state.time = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
        } else {
            state.time = '00:00:00'
            clearInterval(countdownTimer)
        }
    }, 1000)
}
// 充值-预下单
const rechargePrepay = async () => {
    const { personId, payAmount } = state.form
    const params = { personId, payAmount }
    const { data } = await http.post("/unicard/app/pay-center/recharge-prepay", params)
    const { infos, tradeNo, appId, payEndTime } = data
    state.form.tradeNo = tradeNo
    state.infos = infos
    state.appId = appId
    // 根据payEndTime计算剩余时间，进行倒计时。格式为：00:00:00
    if (payEndTime) {
        startCountdown(payEndTime)
    }
}
function getWxCode() {
    return new Promise((resolve, reject) => {
        uni.login({
            provider: 'weixin',
            success({ errMsg, code }) {
                if (code) {
                    resolve(code)

                } else {
                    uni.showToast({
                        title: `${errMsg}`,
                        icon: 'none'
                    })
                    console.log('获取微信code失败', errMsg, code)
                    reject(new Error("get weixin login code error"))
                }
            },
            fail(err) {
                console.log('获取微信code fail', err)
                reject(new Error(err))
            }
        });
    })
}
const authorize = async () => {
    const _openId = uni.getStorageSync('openId')
    // 如有_openId则直接支付 没有则跳授权
    if (_openId && _openId !== 'undefined') {
        state.form.openid = _openId
    } else {
        if (isWechatBrowser()) {
            // 公众号
            navigateTo({
                url: '/apps/smartCard/payAuth/index',
                query: {
                    configTypeId: state.form.configTypeId,
                    appId: state.appId,
                    back_url: encodeURIComponent(window.location.origin),
                },
            })
        }
    }
}
// 获取openId
const getOpenId = async (item) => {
    let _setquery = uni.getStorageSync('setForm')
    if (_setquery) {
        _setquery = JSON.parse(_setquery)
    }
    const { code, schoolId, appId, configTypeId } = item
    const params = {
        code,
        schoolId: _setquery?.schoolId || schoolId || '',
        appId: _setquery?.appId || appId || '',
        configTypeId: _setquery?.configTypeId || configTypeId || ''
    }
    await http.post("/unicard/callback/wechat/gzh/getAccessToken", params).then(({ data }) => {
        state.form.openid = data.openid
        state.form = _setquery
    })
};
const weixinPayType = async () => {
    // 是否有微信支付
    return new Promise(async (resolve, reject) => {
        if (state.payMethodList.length) {
            if (isWechatBrowser()) {
                //  检测是否在微信内置浏览器中
                state.form.payType = 1
                state.form.paySource = "公众号支付"
                uni.setStorageSync('setForm', JSON.stringify({ ...state.form, appId: state.appId, studentId: state.studentId }));
                // 如何有_openId则直接支付 没有则跳授权
                authorize()
                resolve(true)
                return
            }
            // #ifdef MP-WEIXIN
            if (state.form.configTypeId) {
                state.form.payType = 5
                state.form.paySource = "小程序支付"
                // const jsCode = await getWxCode()
                state.form.openid = await getWxCode()
                // await getOpenId({ code: jsCode, configTypeId: state.form.configTypeId, appId: state.appId, studentId: state.studentId })
                uni.setStorageSync('setForm', JSON.stringify({ ...state.form, appId: state.appId, studentId: state.studentId }));
                resolve(true)
            }
            // #endif
            resolve(true)
        } else {
            reject(false)
        }
    })

}
const init = async (route) => {
    const item = { ..._returnParams.value, ...route }
    useSmartCard.setReturnParams(item)

    let _setquery = uni.getStorageSync('setForm')
    // 有缓存拿缓存  无缓存拿参数
    if (_setquery) {
        _setquery = JSON.parse(_setquery)
        state.form = _setquery
        state.form.payAmount = _setquery.payAmount || item.payAmount || ""
        state.form.personId = _setquery.personId || item.personId || ""
        state.tradeNo = _setquery.tradeNo || item.tradeNo || ""
        state.activePayMethodId = _setquery.payMethodId || item.payMethodId || ""
        state.studentId = _setquery.studentId || item.studentId || ""
        await initPage()
        await rechargePrepay()
    }


    if (item.code) {
        // 这个时候肯定是有_setquery了
        getOpenId(item)
        return
    } else {
        state.form.payAmount = item.payAmount || ""
        state.form.personId = item.personId || ""
        state.tradeNo = item.tradeNo || ""
        state.activePayMethodId = item.payMethodId || ""
        state.studentId = item.studentId || ""
        await initPage()
        await rechargePrepay()

    }
    // #ifdef H5 || H5-WEIXIN

    // 回显支付方式
    nextTick(() => {
        // 支付回调后获取支付方式回显
        if (state.activePayMethodId) {
            state.form.configTypeId = state.payMethodList?.find((item) => item.payMethodId == state.activePayMethodId)?.configTypeId || ""
        }
        item.payMethodId && orderTipPopup.value.open()
    })
    // #endif
}

// 快捷支付
const quickRadioChange = (evt) => {
    const { id, payMethodId, configTypeId, relatedId } = JSON.parse(evt.detail.value)
    state.activePayMethodId = id || ""
    state.form.payMethodId = payMethodId || ""
    state.form.configTypeId = configTypeId || ""
    state.form.relatedId = relatedId

}

// 更多支付方式
const radioChange = (evt) => {
    const { payMethodId, configTypeId } = JSON.parse(evt.detail.value)
    state.activePayMethodId = payMethodId || ""
    state.form.payMethodId = payMethodId || ""
    state.form.configTypeId = configTypeId || ""
}

// 微信支付宝提交支付订单余额
const handleSaveWeixinPay = async () => {
    if (state.form.payMethodId == 1 && !state.form.openid) {
        await weixinPayType()
    }
    state.loadingSave = true
    http.post("/unicard/app/pay-center/pay-submit", state.form)
        .then(({ data }) => {
            // 如果是公众号嵌套前提前获取openid
            const params = {
                ...data.resultInfo.jsApiResult,
                package: data.resultInfo.jsApiResult?.packageStr || ''
            }
            if (isWechatBrowser()) {
                onBridgeReady(params)
            } else {
                // #ifdef MP-WEIXIN
                onBridgeReady(params)
                // #endif

                // #ifdef H5
                const roleArr = ["wx-miniprogram"]
                if (roleArr?.includes(checkPlatform())) {
                    isGuideMap.value = true
                    return
                } else {
                    let _redirect_url = ''
                    // payH5({ ...data, ...state.form })
                    if (["development", "uat"].includes(import.meta.env.VITE_APP_NAME)) {
                        _redirect_url = `https://local-h5.1yide.com/${window.location.hash}&tradeNo=${state.form.tradeNo}&payMethodId=${state.form.payMethodId}`
                    } else if (["uatrelease"].includes(import.meta.env.VITE_APP_NAME)) {
                        // http://wisdomappuat.1yide.com 预发布
                        _redirect_url = `https://wisdomappuat.1yide.com/${window.location.hash}&tradeNo=${state.form.tradeNo}&payMethodId=${state.form.payMethodId}`
                    } else {
                        // 正式环境
                        _redirect_url = `https://mclouds.1yide.com/${window.location.hash}&tradeNo=${state.form.tradeNo}&payMethodId=${state.form.payMethodId}`
                    }
                    window.location.href = `${data.resultInfo.h5Url}&redirect_url=${encodeURIComponent(_redirect_url)}`
                }
                // #endif
            }
        }).finally(() => {
            state.loadingSave = false
        })
}
onLoad((route) => {
    Object.keys(route).forEach((key) => {
        route[key] = decodeURIComponent(route[key])
    })
    init(route)
})

function clickLeft() {
    const _route = _returnRoute.value.pop() || '/apps/smartCard/recharge/index'
    navigateTo({ url: _route })
    // navigateTo({
    //     url: `/apps/smartCard/recharge/index`,
    //     query: {
    //         studentId: state.studentId || '',
    //         personId: state.form.personId
    //     }
    // })
}
</script>

<style lang="scss" scoped>
.order_payment {
    background: $uni-bg-color-grey;
    height: 100vh;

    .content {
        padding: 30rpx;

        .money-time {
            text-align: center;
            padding: 63rpx 0;

            .money {
                font-weight: 600;
                font-size: 72rpx;
                color: #333333;
            }

            .time-tips {
                margin: 10rpx 0;
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        .list-box {
            border-radius: 12rpx;
            overflow: hidden;
            padding: 20rpx;
            background: #FFFFFF;

            :deep(.uni-list-item__container) {
                align-items: center;
                padding: 20rpx 0;
            }

            .list-item {
                border-bottom: 1rpx solid #D9D9D9;

                .slot-image {
                    width: 40rpx;
                    height: 40rpx;
                    margin-right: 20rpx;
                }
            }
        }

        .label-box {
            font-weight: 400;
            font-size: 30rpx;
            color: #999999;
            margin: 20rpx 0;
        }
    }

    .footer {
        padding: 20px 32rpx;
        background: $uni-bg-color;
        display: flex;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;

        .mini-btn {
            background-color: var(--primary-color);
            flex: 1;
        }
    }

    .popup-content {
        width: 80vw;

        .header {
            position: relative;
            text-align: center;
            padding: 36rpx 0;

            .title {
                font-weight: 400;
                font-size: 34rpx;
                color: #333333;
            }

            .close {
                position: absolute;
                right: 10rpx;
                top: 10rpx;
            }
        }

        .body {
            text-align: center;
            border-top: 1rpx solid $uni-border-color;
            border-bottom: 1rpx solid $uni-border-color;
            padding: 30rpx 0;
            font-weight: 400;
            font-size: 34rpx;
            color: #ff0000;
        }

        .footer-btn {
            display: flex;
            justify-content: center;
            padding: 36rpx 0;
            font-weight: 400;
            font-size: 30rpx;
            color: #999999;
        }
    }

    .guide-map {
        width: 100vw;
        height: 100vh;
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 9999;
    }
}
</style>
