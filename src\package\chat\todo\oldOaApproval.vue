<template>
    <div class="recorde_page">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="详情" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <!-- 状态 -->
        <div class="recorde_info">
            <div class="recorde_info_status_box">
                <div class="recorde_info_status_text">
                    <text class="p_title">{{ state.title }}</text>
                    <text class="span_time">{{ state.createTime }}</text>
                </div>
                <div class="recorde_info_status" :style="['background: #FFE5E3', 'background: #FFF6DF', 'background: #DCF7ED', 'background: #E8E8E8'][state.status]">
                    {{ ["已拒绝", "待审批", "已通过", "已撤销"][state.status] }}
                </div>
            </div>
            <!-- 审批信息详情 -->
            <div class="recorde_info_details_box">
                <div class="details_item">
                    <text class="lebel">{{ identityType == "teacher" ? "所在部门" : "所在班级" }}</text>
                    <text class="value"> {{ state.dept }}</text>
                </div>
                <div class="details_item" v-if="identityType != 'teacher'">
                    <text class="lebel">请假学生</text>
                    <text class="value"> {{ state.name ? state.name : "-" }}</text>
                </div>
                <div class="details_item">
                    <text class="lebel">请假时间</text>
                    <text class="value" v-if="state.apprJson.startTime && state.apprJson.endTime">
                        {{ state.apprJson.startTime }}至
                        {{ state.apprJson.endTime }}
                    </text>
                    <text class="value" v-else>-</text>
                </div>
                <div class="details_item">
                    <text class="lebel">请假事由</text>
                    <text class="value">{{ state.apprJson.reason ? state.apprJson.reason : "-" }}</text>
                </div>
            </div>
            <!-- 审批流程 -->
            <div class="info_process_box">
                <div class="info_process_title">流程</div>
                <!-- 发起申请 && 审批人 -->
                <div v-for="(item, index) in state.hiApprNodeList" :key="item.id + 'hiApprNodeList ' + index">
                    <div class="info_myapply">
                        <div class="info_myapply_left">
                            <div class="left_img" :style="item.status === 2 || item.status === 5 ? 'border: 1px solid #11c685;' : ''">
                                <image class="image" :src="item.avatar && item.avatar !== '' ? item.avatar : '@nginx/chat/leaveImage.png'" alt="头像" />
                                <div class="info_status_mark" v-if="item.status === 2 || item.status === 0">
                                    <image class="image" :src="item.status === 2 ? '@nginx/chat/approvalYes.png' : item.status === 0 ? '@nginx/chat/approvalNo.png' : ''" alt="" />
                                </div>
                            </div>
                            <div class="left_info_text">
                                <text class="name">{{ item.nodeName }}</text>
                                <text class="status">
                                    {{ item.userName }}
                                    <text class="status">{{ ["已拒绝", "待审批", "同意", "撤销", "回退", "提交申请", "重新提交"][item.status] }}</text>
                                </text>
                            </div>
                        </div>
                        <div class="info_myapply_right">
                            {{ item.apprTime }}
                        </div>
                    </div>
                    <div v-if="item.status === 0" style="color: #141414; margin-left: 46rpx; font-size: 12px">
                        {{ state.deleteReason }}
                    </div>
                    <div :class="checkPlatform() === 'dingding' ? 'status_rate_dd' : 'status_rate'" v-if="state.ccList.length > 0 || index !== state.hiApprNodeList.length - 1" :style="item.status == 2 || item.status == 5 ? 'background: #11c685' : ''"></div>
                </div>
                <!-- 抄送人 -->
                <div class="info_cc" v-if="state.ccList.length > 0">
                    <div class="left_cc">
                        <div class="cc_logo">
                            <image class="image" src="@nginx/chat/ccLogo.png" alt="头像" />
                        </div>
                        <div class="cc_text">
                            <text class="label">抄送人</text>
                            <text class="value"> 共抄送{{ state.ccList.length }}人 </text>
                        </div>
                    </div>
                    <div class="right_cc" v-if="state.ccList.length > 6">
                        <van-icon @click="isShowCCFn" :name="state.isShowCC === true ? 'arrow-down' : 'arrow-up'" size="20px" color="#BABABA" />
                    </div>
                </div>
                <div class="cc_list_class" v-if="state.isShowCC === true">
                    <div class="cc_item" v-for="(item, index) in state.ccList" :key="item.id + 'ccList ' + index">
                        <image class="image" :src="item.avatar && item.avatar !== '' ? item.avatar : '@nginx/chat/leaveImage.png'" alt="头像" />
                        <span class="name">{{ item.name }}</span>
                    </div>
                </div>
            </div>
            <!-- 拒绝修改 -->
            <div class="submit_class" v-if="state.taskId && state.taskId !== ''">
                <button @click="operationFn(0)" class="btn_class">拒绝</button>
                <button @click="operationFn(2)" class="btn_class btn_primary">同意</button>
            </div>
        </div>
        <uni-popup ref="inputDialog" type="dialog">
            <uni-popup-dialog type="info" ref="inputClose" mode="textarea" title="拒绝理由" v-model="state.refuseReason" placeholder="请输入拒绝理由" @confirm="refuseReasonFn(0)"></uni-popup-dialog>
        </uni-popup>
    </div>
</template>

<script setup>
import { checkPlatform } from "@/utils/sendAppEvent.js"
import useStore from "@/store"
const { user } = useStore()
const routeQuery = ref({})

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})
const state = reactive({
    isWithdraw: false,
    refuseReasonDialog: false,
    refuseReason: "",
    isShowCC: true,
    title: "",
    dept: "",
    apprJson: {
        endTime: "",
        hours: 0,
        reason: "",
        startTime: ""
    },
    status: "",
    hiApprNodeList: [],
    ccList: [],
    deleteReason: "",
    procInstId: "", //	流程实例Id
    taskId: "", // 任务ID
    createTime: ""
})

/**
 * @description: 拒绝接口
 * @param {*} outcome  0为拒绝
 * @return {*}
 */
function refuseReasonFn(outcome) {
    if (state.refuseReason != "") {
        completeTaskFn(outcome)
    } else {
        uni.showToast({
            icon: "none",
            title: "请填写拒绝理由"
        })
    }
}

/**
 * @description: 拒绝同意接口
 * @param {*} outcome 2为同意 0为拒绝
 * @return {*}
 */
function completeTaskFn(outcome) {
    let params = {
        taskId: state.taskId,
        outcome,
        comment: state.refuseReason
    }
    http.post("/app/v2/proc/completeTask", params).then((res) => {
        uni.showToast({
            icon: "none",
            title: res.message
        })
        getRecordeInfoFn()
    })
}

function clickLeft() {
    uni.navigateBack()
}
const inputDialog = ref(null)
/**
 * @description: 点击拒绝同意
 * @param {*} outcome 2为同意 0为拒绝
 * @return {*}
 */
function operationFn(outcome) {
    switch (outcome) {
        case 2:
            completeTaskFn(outcome)
            break
        case 0:
            inputDialog.value.open()
            // state.refuseReasonDialog = true

            break

        default:
            break
    }
}

function getRecordeInfoFn() {
    uni.showLoading({
        title: "加载中..."
    })
    http.get("/app/userAppr/getInfo", { id: routeQuery.value.callId })
        .then((res) => {
            if (res.data) {
                let { apprJson, status, title, dept, hiApprNodeList, ccList, deleteReason, procInstId, name, taskId, isWithdraw, createTime } = res.data
                state.createTime = createTime || ""
                state.apprJson = apprJson || {
                    endTime: "",
                    hours: 0,
                    reason: "",
                    startTime: ""
                }
                state.taskId = taskId || ""
                state.title = title || ""
                state.name = apprJson?.student || ""
                state.dept = dept || ""
                state.status = status
                state.hiApprNodeList = hiApprNodeList || []
                state.ccList = ccList || []
                state.deleteReason = deleteReason
                state.isWithdraw = isWithdraw
                state.procInstId = procInstId // 	流程实例Id
            }
        })
        .finally(() => {
            state.showOverlay = false
            uni.hideLoading()
        })
}

function isShowCCFn() {
    state.isShowCC = !state.isShowCC
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    routeQuery.value = options
    getRecordeInfoFn()
})
</script>

<style scoped lang="scss">
.recorde_page {
    background: $uni-bg-color-grey;
}
.recorde_info {
    min-height: calc(100vh - 88rpx);
    background: $uni-bg-color-grey;

    .recorde_info_status_box {
        min-height: 10rpx;
        padding: 30rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #ffffff;
        // margin: 20rpx 0rpx;

        .recorde_info_status_text {
            display: flex;
            flex-direction: column;

            .p_title {
                font-size: 30rpx;
                font-weight: 500;
                color: #333333;
                line-height: 42rpx;
                margin: 0rpx 0rpx 10rpx 0rpx;
            }

            .span_time {
                font-size: 24rpx;
                font-weight: 400;
                color: #666666;
                line-height: 34rpx;
            }
        }

        .recorde_info_status {
            width: 132rpx;
            height: 46rpx;
            background: #fff6df;
            border-radius: 23rpx;
            font-size: 24rpx;
            font-weight: 400;
            color: #333333;
            line-height: 46rpx;
            text-align: center;
        }
    }

    .recorde_info_details_box {
        background: #ffffff;
        display: flex;
        flex-direction: column;
        font-weight: 400;
        margin-bottom: 20rpx;
        padding-bottom: 20rpx;

        .details_item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 30rpx 40rpx 0rpx 30rpx;

            .lebel {
                margin: 0;
                font-size: 24rpx;
                color: #666666;
                line-height: 34rpx;
            }

            .value {
                font-size: 28rpx;
                color: #333333;
                line-height: 40rpx;
            }
        }
    }

    .info_process_box {
        padding: 30rpx 30rpx 180rpx 30rpx;
        background: #ffffff;

        .info_process_title {
            font-size: 24rpx;
            font-weight: 400;
            color: #666666;
            line-height: 34rpx;
            margin-bottom: 8rpx;
        }

        .info_myapply {
            display: flex;
            justify-content: space-between;

            .info_myapply_left {
                display: flex;

                .left_img {
                    width: 76rpx;
                    height: 76rpx;
                    position: relative;

                    .info_status_mark {
                        position: absolute;
                        bottom: 25rpx;
                        right: -5rpx;
                        width: 28rpx;
                        height: 28rpx;

                        .image {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .image {
                        width: 100%;
                        height: 100%;
                    }
                }

                .left_info_text {
                    font-family:
                        PingFangSC-Regular,
                        PingFang SC;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    margin-left: 6rpx;

                    .name {
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        margin: 0;
                    }

                    .status {
                        font-size: 24rpx;
                        font-weight: 400;
                        color: #666666;
                        padding: 4px 0rpx;
                    }
                }
            }

            .info_myapply_right {
                font-size: 24rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 34rpx;
            }
        }

        .status_rate {
            width: 1rpx;
            height: 60rpx;
            background: #b3b3b3;
            margin-left: 38rpx;
        }

        .status_rate_dd {
            width: 3rpx;
            height: 60rpx;
            background: #b3b3b3;
            margin-left: 38rpx;
        }

        .info_cc {
            display: flex;
            justify-content: space-between;

            .left_cc {
                display: flex;

                .cc_logo {
                    width: 76rpx;
                    height: 76rpx;
                    background: #11c685;
                    border-radius: 8rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .image {
                        width: 56rpx;
                        height: 56rpx;
                    }
                }

                .cc_text {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    margin-left: 6rpx;

                    .label {
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        margin: 0;
                    }

                    .value {
                        font-size: 24rpx;
                        font-weight: 400;
                        color: #666666;
                    }
                }
            }

            .right_cc {
                display: flex;
            }
        }

        .cc_list_class {
            display: flex;
            margin: 20rpx 0rpx 0rpx 80rpx;
            flex-wrap: wrap;

            .cc_item {
                display: flex;
                flex-direction: column;
                margin-right: 30rpx;
                justify-content: center;
                align-items: center;

                .image {
                    width: 60rpx;
                    height: 60rpx;
                }

                .name {
                    font-size: 20rpx;
                    font-weight: 400;
                    color: #666666;
                    line-height: 28rpx;
                    max-width: 100rpx;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                }
            }
        }
    }

    .submit_class {
        position: fixed;
        bottom: 0rpx;
        left: 0;
        padding: 30rpx;
        width: 92%;
        background-color: #fff;
        display: flex;
        .btn_class {
            margin: 0 10rpx;
            flex: 1;
            border: 1rpx solid #eee;
            font-size: 28rpx;
            height: 90rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #3d3c3c;
        }
        .btn_primary {
            background-color: var(--primary-color);
            color: #fff;
        }
    }
}
</style>
