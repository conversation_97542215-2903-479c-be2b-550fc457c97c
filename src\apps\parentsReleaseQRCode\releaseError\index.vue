<template>
    <uni-nav-bar :showMenuButtonWidth="true" :border="false" @clickLeft="routerBack" left-icon="left" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
        <view class="title_box">二维码失效</view>
    </uni-nav-bar>
    <view class="content">
        <view class="qr-code">
            <img
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALgAAAC4CAIAAADfUbGQAAADqUlEQVR42u3bUXLDIAwFQN//0u0VkqAnBN732UljDOuOLOjzJ/JBHlMgoAgoAoqAIqAIKCKgCCgCioAioAgoAooIKAKKgCKHQHkCWbnuJ5+PTN+X91V1v+l5BgUUUEABBZQboFRNdGLi0tdduccErNx6gQIKKKCAAsrZUKqKu8kLU3VfVT/vAQcKKKCAAgoooPyyYImx7RoPKKCAAgoooIByNpRpsGo34UABBRRQQAEFlImbghOaZumGW+fBK1BAAQUUUEB5I5TEoV8/75lnUEABBRRQgLgNSjqJIq5z0tMNxg0rAgoooIACCihHQEkUXwk0u6670gBMzycooIACCiig3AylasGqNt6mHZBOLOTeAhkUUEABBRRQzoBSdfg5MYkTGmsrY0tfCxRQQAEFFFBuhrKrmF0Bt4K7s1BNNNBAAQUUUEAB5Y1QVm4scQA7XYCnG2XpIh0UUEABBRRQboZStbFXVdAlkFUV1LsOTI0oZkEBBRRQQAFlyD+pVzXBdh10OuWQVGLMoIACCiiggHIblM7PJwq99KZmbvFaZwAUUEABBRRQjoCSKNASzbGqAjPd7KpqGOYeMFBAAQUUUEA5A0rVYaWq6yY2KVfGkFiwznsEBRRQQAEFlFOhVBW2kw8wT2jiVb0cjHvrAQUUUEABBZQGKM9Cdn1/+tB1/0LmXiBAAQUUUEAB5SQouw4idR607lykFVg99wIKKKCAAgoo50HZdbAosUGYKFRri8pdjU1QQAEFFFBAuQdK+jPp300sUmLMoIACCiiggAJKTVGWbkAlIFaNfwI4UEABBRRQQLkZSmcjq3OzMI27s+lX2+gDBRRQQAEFlDOgrEx0ZzGbxpQohBMvEAV/AkABBRRQQAHlaCjpDa1Eqor3RLGfbjaCAgoooIACylug5Ab62/encaSL3875BAUUUEABBZSboewqTqvGtgIx8VDtKtJBAQUUUEAB5VQo8dKpsaGXaGqlDxb1AwIFFFBAAQWUM6BMKL52LWTngab0AwMKKKCAAgooN0OZVrR2Ak1gmrkhCgoooIACCihnQ+lsQFVdKzGeaQ8DKKCAAgoooICyp8E1YWyJBUtsIoICCiiggAIKKH2bYblG02+Tni6cq5pyoIACCiiggPIWKJ2Tnt7Yq9rwSzQME+MBBRRQQAEFlNugJBpQVZOSnsTE9ycKeVBAAQUUUEB5CxR5W0ARUAQUAUVAEVAEFBFQBBQBRUARUAQUAUUEFAFFQJH2/AO0bOWZuhj43QAAAABJRU5ErkJggg=="
            />
        </view>
        <button class="update-code" style="background: var(--primary-color)" type="primary" @click="Scan">重新扫码</button>
    </view>
</template>
<script setup>
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
const Scan = () => {
    if (checkPlatform() == "wx-miniprogram") {
        window.parent.postMessage({ scanQRCode: true }, "*")
    } else {
        sendAppEvent("getQrResult", { code: "parentsReleaseQRCode" })
    }
}
const routerBack = () => {
    const roleArr = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"]
    if (roleArr.includes(checkPlatform())) {
        sendAppEvent("backApp", {})
    } else {
        window.parent.postMessage({ back: true }, "*")
    }
}
</script>
<style lang="scss" scoped>
.title_box {
    width: 100%;
    text-align: center;
    line-height: 88rpx;
    font-size: 34rpx;
    font-weight: 500;
    color: #333333;
}

.content {
    // 居中
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    .qr-code {
        width: 400rpx;
        height: 400rpx;
        position: relative;
        border-radius: 30rpx;
        overflow: hidden;

        &::after {
            content: "二维码已失效";
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: #161212ad;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 32rpx;
        }

        img {
            width: 100%;
            height: 100%;
        }
    }

    .update-code {
        margin-top: 30px;
    }
}
</style>
