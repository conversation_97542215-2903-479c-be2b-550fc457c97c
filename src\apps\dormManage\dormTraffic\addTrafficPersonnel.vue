<template>
    <div class="add_traffic_personnel">
        <z-paging>
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="添加放行" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
            </template>
            <!-- #ifdef  MP-WEIXIN -->
            <view class="right_class" @click="releaseRecord"> 放行记录 </view>
            <!-- #endif -->

            <uni-list :border="false">
                <uni-list-item class="form_item" clickable @click="selectPerson">
                    <template v-slot:header>
                        <view class="left">
                            <span class="must">*</span>
                            选择人员
                        </view>
                    </template>
                    <template v-slot:footer>
                        <view class="right">
                            <span class="right_text"> {{ form.businessName || "请选择" }}</span>
                            <uni-icons type="forward" size="18" color="#999999" />
                        </view>
                    </template>
                </uni-list-item>
                <uni-list-item class="form_item">
                    <template v-slot:header>
                        <view class="left">
                            <span class="must">*</span>
                            放行开始时间
                        </view>
                    </template>
                    <template v-slot:footer>
                        <picker mode="time" @change="startTimeChange">
                            <view class="right">
                                <span class="right_text" v-if="!form.startTime">请选择</span>
                                <view v-else class="uni-input right_text">{{ form.startTime }}</view>
                                <uni-icons type="forward" size="18" color="#999999" />
                            </view>
                        </picker>
                    </template>
                </uni-list-item>
                <uni-list-item class="form_item">
                    <template v-slot:header>
                        <view class="left">
                            <span class="must">*</span>
                            放行结束时间
                        </view>
                    </template>
                    <template v-slot:footer>
                        <picker mode="time" @change="endTimeChange">
                            <view class="right">
                                <span v-if="!form.endTime" class="right_text">请选择</span>
                                <view v-else class="uni-input right_text">{{ form.endTime }}</view>
                                <uni-icons type="forward" size="18" color="#999999" />
                            </view>
                        </picker>
                    </template>
                </uni-list-item>
            </uni-list>
            <div class="reason">
                <span class="title must">* </span>
                <span class="title">放行原因</span>
                <textarea class="textarea_class" auto-height :maxlength="200" v-model="form.reason" placeholder-style="color:#999999" placeholder="请输入" />
                <div class="number_words">{{ form.reason.length }} / 200</div>
            </div>
            <template #bottom>
                <div class="buttom_class">
                    <button @click="confirm">确认放行</button>
                </div>
            </template>
        </z-paging>
    </div>
</template>

<script setup>
const form = ref({
    businessId: [],
    startTime: "",
    endTime: "",
    type: 1, // 固定为1：学生
    reason: "",
    businessName: ""
})
function linechange(event) {
    console.log(event.detail)
}
function routerBack() {
    uni.navigateBack()
}
function releaseRecord() {
    navigateTo({
        url: "/apps/dormManage/dormTraffic/releaseRecord"
    })
}
function endTimeChange(e) {
    form.value.endTime = e.detail?.value
}
function startTimeChange(e) {
    form.value.startTime = e.detail?.value
}

const selectPerson = () => {
    navigateTo({ url: "/apps/dormManage/selectPersonal/index", query: { multiple: true } })
}

function confirm() {
    if (form.value.businessId && form.value.businessId.length > 0 && form.value.startTime !== "" && form.value.endTime !== "" && form.value.reason !== "") {
        http.post("/app/dorm/pass/addDormRelease", form.value).then((res) => {
            uni.showToast({
                title: res.message
            })
            releaseRecord()
        })
    } else {
        uni.showToast({
            title: "请填写完整放行信息！",
            duration: 2000,
            icon: "none"
        })
    }
}

uni.$on("dormTraffic", function (data) {
    if (data && data.businessId.length) {
        form.value.businessId = data.businessId || []
        form.value.businessName = data.businessName ? data.businessName?.join("、") : ""
    }
})
</script>

<style lang="scss" scoped>
// 头部
.head {
    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: #333333;
    }
    .right_class {
        display: flex;
        justify-content: flex-end;
        height: 100%;
        align-items: center;
        font-size: 28rpx;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #4566d5;
        line-height: 40rpx;
    }
}
.add_traffic_personnel {
    min-height: calc(100vh - 100rpx);
    background: $uni-bg-color-grey;
    .form_item {
        .left {
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
            .must {
                color: #f5222d;
            }
        }
        .right {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            .right_text {
                max-width: 400rpx;
                font-size: 28rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
            }
        }
    }
    .reason {
        min-height: 162rpx;
        background: #ffffff;
        margin-top: 20rpx;
        padding: 28rpx;
        .title {
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
        }
        .must {
            color: #f5222d;
        }
        .textarea_class {
            padding-top: 20rpx;
            width: 100%;
        }
        .number_words {
            font-size: 28rpx;
            font-weight: 400;
            color: #999999;
            line-height: 52rpx;
            text-align: right;
        }
    }
    .buttom_class {
        height: 106rpx;
        background: #ffffff;
        padding: 30rpx;
        button {
            background-color: #4566d5;
            color: #ffffff;
            border-radius: 10rpx;
            font-size: 32rpx;
            height: 92rpx;
            font-weight: 400;
            line-height: 92rpx;
        }
    }
}
</style>
