<template>
    <div class="page">
        <z-paging ref="paging" class="container_box" v-model="state.dataList" :auto="false" @query="queryList">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" v-if="checkPlatform() !== 'dingding'" left-icon="left" title="点餐记录" :border="false" fixed statusBar @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" />
                <div class="filter_box">
                    <div class="filter left_filter" @click="packageType">
                        {{ state.selectType?.name || "全部套餐类型" }}
                    </div>
                    <div class="filter right_filter" @click="calendars.open()">
                        <text v-if="state.startTime && state.endTime"> {{ state.startTime + "至" + state.endTime }}</text>
                        <text v-else>请选择日期</text>
                    </div>
                </div>
            </template>
            <view class="record_list">
                <view class="record_item" v-for="(item, index) in state.dataList" :key="item.orderDate + index">
                    <view class="date_title">
                        {{ item.orderDate }}
                    </view>
                    <view class="combo_meal_box">
                        <view
                            class="meal_item"
                            v-for="(meal, mIdx) in item.children"
                            :key="meal.id + mIdx"
                            :style="{
                                borderBottom: mIdx === item.children.length - 1 ? 'none' : '1rpx solid #d8d8d8'
                            }"
                            @click="goDetail(meal)"
                        >
                            <view class="left_box">
                                <text
                                    class="meal_title"
                                    :style="{
                                        marginBottom: mIdx === item.children.length - 1 ? '0' : '30rpx'
                                    }"
                                >
                                    {{ meal.mealSetTypeName }}：
                                    <text class="meal_value">{{ meal.mealSetName || "-" }}</text>
                                </text>
                                <text class="meal_title">
                                    菜品：
                                    <text class="meal_value">{{ meal.dishName || "-" }}</text>
                                </text>
                            </view>
                            <view class="right_box"><uni-icons type="right" size="20" style="color: #999"></uni-icons> </view>
                        </view>
                    </view>
                </view>
            </view>
            <template #empty>
                <view class="not_set">
                    <img class="image" src="https://alicdn.1d1j.cn/announcement/20230706/b38b5f559f77415aa3bedebe883e09dd.png" alt="" />
                    <text class="text">暂无数据</text>
                </view>
            </template>
        </z-paging>
        <view class="calendar_box">
            <uv-calendars ref="calendars" color="#00D190" :clearDate="false" confirmColor="#00D199" :allowSameDay="true" mode="range" @confirm="confirm" />
        </view>
        <yd-select-popup ref="selectPopupRef" :list="state.canteenMealSetType" title="请选择" @closePopup="closePopup" :fieldNames="{ value: 'id', label: 'name' }" />
    </div>
</template>

<script setup>
import { checkPlatform } from "@/utils/sendAppEvent.js"
import { onLoad } from "@dcloudio/uni-app"
import { nextTick } from "vue"

const selectPopupRef = ref(null)
const calendars = ref(null)
const paging = ref(null)
const state = reactive({
    dataList: [],
    canteenMealSetType: [],
    startTime: "",
    endTime: "",
    selectType: {},
    studentId: null
})

const goDetail = (item) => {
    navigateTo({
        url: "/apps/canteenMachine/detail/index",
        query: {
            id: item.id,
            studentId: state.studentId
        }
    })
}

const back = () => {
    uni.navigateBack()
}
const closePopup = (val) => {
    if (!val) return
    state.selectType = val
    paging.value.reload()
}

const packageType = () => {
    selectPopupRef.value.open()
}

const queryList = (pageNo, pageSize) => {
    const params = {
        startTime: state.startTime || "",
        endTime: state.endTime || "",
        mealSetTypeId: state.selectType.id,
        pageNo,
        pageSize,
        userId: state.studentId || null
    }
    http.post("/app/canteen/canteenStudentOrder/selectDayRecordPage", params)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

function getCanteenMealSetType() {
    http.get("/app/canteenMealSetType/get").then((res) => {
        state.canteenMealSetType = [
            {
                name: "全部套餐类型",
                id: null
            }
        ].concat(res.data)
    })
}

const confirm = (e) => {
    state.startTime = e.range.before || ""
    state.endTime = e.range.after || ""
    paging.value.reload()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    nextTick(() => {
        state.studentId = options.studentId
        getCanteenMealSetType()
        paging.value.reload()
    })
})
</script>

<style lang="scss">
.calendar_box {
    .uv-toolbar__wrapper__confirm {
        color: var(--primary-color) !important;
    }

    .uv-calendar-item__weeks-box-item {
        .uv-calendar-item--isDay-text {
            color: var(--primary-color) !important;
        }
    }
}
</style>
<style lang="scss" scoped>
.page {
    background-color: #f9faf9;
    min-height: 100vh;
}
.calendar_box {
    .uv-calendar-item__weeks-box-item {
        .uv-calendar-item--isDay-text {
            color: var(--primary-color) !important;
        }
    }
}

.filter_box {
    height: 100rpx;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30rpx;

    .filter {
        display: flex;
        align-items: center;

        &::after {
            content: "";
            display: block;
            border: 10rpx solid transparent;
            border-top: 10rpx solid var(--primary-color);
            border-bottom-width: 1px;
            margin-left: 10rpx;
        }
    }

    .left_filter {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
    }

    .right_filter {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
    }
}

.not_set {
    height: calc(100vh - 140rpx);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .image {
        width: 360rpx;
        height: 210rpx;
    }

    .text {
        font-size: 26rpx;
        font-weight: 400;
        color: #8c8c8c;
        line-height: 36rpx;
        padding-top: 30rpx;
    }
}

.record_list {
    .record_item {
        background: #ffffff;
        padding: 30rpx;
        margin-top: 20rpx;

        .date_title {
            font-weight: 500;
            font-size: 30rpx;
            color: #333333;
            line-height: 42rpx;
            padding-bottom: 30rpx;
            border-bottom: 1rpx solid $uni-border-color;
        }

        .combo_meal_box {
            .meal_item {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .left_box {
                    display: flex;
                    flex-direction: column;
                    padding: 30rpx 0;

                    .meal_title {
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #999999;
                        line-height: 40rpx;

                        .meal_value {
                            font-weight: 400;
                            font-size: 28rpx;
                            color: #333333;
                            line-height: 40rpx;
                            text-align: left;
                        }
                    }
                }
            }
        }
    }
}
</style>
