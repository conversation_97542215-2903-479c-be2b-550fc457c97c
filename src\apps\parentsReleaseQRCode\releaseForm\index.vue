<template>
    <uni-nav-bar :showMenuButtonWidth="true" :border="false" left-icon="left" @clickLeft="routerBack" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
        <view class="title_box" @click="routerBack">确定放行</view>
    </uni-nav-bar>
    <view class="releaseForm">
        <uni-forms class="reset-uni-forms" ref="customFormRef" label-position="top" label-width="100vw" :rules="state.customRules" :modelValue="state.valiFormData">
            <uni-forms-item class="form-item" label="请选择你要放行出校的孩子" required name="userIds">
                <checkbox-group @change="radioChange">
                    <label v-for="item in state.childrenList" :key="item.studentId">
                        <view
                            class="uni-list-cell"
                            :class="{
                                active: isChecked(item.studentId)
                            }"
                        >
                            <view class="cell-info">
                                <text>{{ item.studentName }}</text>
                            </view>
                            <view class="radio">
                                <checkbox :value="item.studentId" :checked="isChecked(item.studentId)" />
                            </view>
                        </view>
                    </label>
                </checkbox-group>
            </uni-forms-item>
            <uni-forms-item class="form-item" label="请选择你本次放行孩子出校的场地" required name="siteName">
                <view @click="inputDialogShow">
                    <uni-list-item v-if="state.valiFormData.siteName" class="reset-input text" showArrow :title="state.valiFormData.siteName" />
                    <view class="reset-input placeholder" v-else>请选择</view>
                </view>
            </uni-forms-item>
        </uni-forms>
        <view class="footer">
            <view class="text">家长您好，将孩子放行后，请在校门口有秩序地等候孩子步出校门，确保把孩子安全接回家。</view>
            <!-- 阅读 -->
            <checkbox class="reset-checkbox" :checked="isRead" @click="onChangeChecbox" @change="onChangeChecbox"> 我已认真阅读以上提醒</checkbox>
            <button class="releaseSave" :class="{ active: !isRead }" type="primary" :disabled="!isRead" :loading="state.releaseLoadin" @click="submitForm">放 行</button>
        </view>

        <uni-popup ref="inputDialog" type="bottom">
            <view class="res-popup">
                <view class="handler">
                    <view class="title">选择出校场地</view>
                    <uni-icons class="icons" type="closeempty" @click="siteCancel"></uni-icons>
                </view>
                <view class="construction-site">
                    <checkbox-group class="site" @change="inputDialogChangeLabel">
                        <label v-for="item in state.siteOptions" :key="item.siteId">
                            <view class="uni-list">
                                <text>{{ item.siteName }}</text>
                                <view class="radio">
                                    <checkbox :value="item" :checked="state.valiFormData.siteIds.includes(item.siteId)" />
                                </view>
                            </view>
                        </label>
                    </checkbox-group>
                </view>
                <view class="construction-footer">
                    <button class="siteCancel" @click="siteCancel">取 消</button>
                    <button class="siteSave" type="primary" @click="siteSave">确 定</button>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="popupRrrorRef" type="dialog" style="z-index: 9999">
            <view class="popup-error">
                <img src="/static/<EMAIL>" alt="" />
                <view class="success-txt"> 放行失败！ </view>
                <view class="tips">
                    <view class="error-txt" v-for="(item, idx) in errMessage" :key="idx">{{ item }} </view>
                </view>
                <button class="releaseSave" type="primary" @click="popupRrrorRef.close()">确 定</button>
            </view>
        </uni-popup>
    </view>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue"
// import { onReady, onLoad } from "@dcloudio/uni-app";
import { getFaceList, getSiteList, getQuickPassEltern } from "../api"
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
// import navigate from "@/utils/navigate";

const customFormRef = shallowRef()
const inputDialog = shallowRef()
const popupRrrorRef = shallowRef()
const errMessage = shallowRef("")

const isRead = shallowRef(true)

const state = reactive({
    childrenList: [],
    siteOptions: [],
    selectSite: [],
    valiFormData: {
        userIds: null,
        siteName: "",
        siteId: "",
        siteIds: []
    },
    // 自定义表单校验规则
    customRules: {
        userIds: {
            rules: [
                {
                    required: true,
                    errorMessage: "请选择出校的孩子"
                }
            ]
        },
        siteName: {
            rules: [
                {
                    required: true,
                    errorMessage: "请选择出校场地"
                }
            ]
        }
    },
    releaseLoadin: false
})
const isChecked = computed(() => {
    return (id) => {
        return state.valiFormData.userIds.includes(id)
    }
})
const onChangeChecbox = () => {
    isRead.value = !isRead.value
}
const radioChange = (event) => {
    state.valiFormData.userIds = event.detail.value
}
// 过滤
const filterSite = (data) => {
    let ids = []
    let names = []
    data?.forEach((item) => {
        ids.push(item.siteId)
        names.push(item.siteName)
    })
    return {
        ids,
        names
    }
}
// 选择出校场地
const inputDialogChangeLabel = (event) => {
    state.selectSite = event.detail.value
}

// 选择出校场地
const inputDialogShow = () => {
    inputDialog.value.open("bottom")
}
// 关闭选择出校场地
const siteCancel = () => {
    inputDialog.value.close()
}
// 确定选择出校场地
const siteSave = () => {
    const { ids, names } = filterSite(state.selectSite)
    state.valiFormData.siteIds = ids
    state.valiFormData.siteName = names.join("、")
    siteCancel()
}
// 提交表单
const submitForm = () => {
    customFormRef.value.validate().then(() => {
        state.releaseLoadin = true
        getQuickPassEltern(state.valiFormData)
            .then((res) => {
                navigateTo({
                    url: "/apps/parentsReleaseQRCode/releaseSuccess/index"
                })
            })
            .catch((err) => {
                errMessage.value = err.message.split("\n")
                popupRrrorRef.value.open("center")
            })
            .finally(() => {
                state.releaseLoadin = false
            })
    })
}

onReady(() => {
    // 必须在节点渲染完毕后执行
    customFormRef.value.setRules(state.customRules)
})
// 获取孩子信息
const handlerStudentInfo = async () => {
    await getFaceList().then(({ data }) => {
        state.childrenList = data.childrenList || []
        // 默认勾选第一个学生
        if (data.childrenList.length) {
            state.valiFormData.userIds = [data.childrenList[0].studentId]
        }
    })
}

// 获取场地
const getNoSignSite = async () => {
    await getSiteList({ type: 2 }).then(({ data }) => {
        state.siteOptions = data.siteList
        state.valiFormData.siteIds = []
        state.valiFormData.siteName = ""
        // 默认选择所有场地
        if (data.siteList.length) {
            const { ids, names } = filterSite(data.siteList)
            state.valiFormData.siteIds = ids
            state.valiFormData.siteName = names.join("、")
        }
    })
}
const routerBack = () => {
    const roleArr = ["yide-ios-app", "Ios", "yide-android-app"]
    if (roleArr.includes(checkPlatform())) {
        sendAppEvent("backApp", {})
    } else {
        window.parent.postMessage({ back: true }, "*")
    }
}
onMounted(async () => {
    await handlerStudentInfo()
    await getNoSignSite()
})
</script>
<style lang="scss" scoped>
:deep(.uni-radio-input) {
    border-color: var(--primary-color);
}

.reset-checkbox {
    :deep(.uni-radio-input) {
        border-color: var(--primary-color) !important;
    }
}

.title_box {
    width: 100%;
    text-align: center;
    line-height: 88rpx;
    font-size: 34rpx;
    font-weight: 500;
    color: #333333;
}

:deep(.uni-checkbox-input),
:deep(.uni-radio-input) {
    border-radius: 50%;
    overflow: hidden;

    &:hover {
        border-color: var(--primary-color);
    }

    svg {
        color: $uni-bg-color;
        background: var(--primary-color);
        transform: translate(-50%, -50%) scale(1);

        &:focus {
            outline-color: var(--primary-color);
            outline-style: auto;
            outline-width: 5px;
        }

        path {
            fill: $uni-bg-color;
        }
    }
}

.releaseForm {
    padding: 30rpx;
    overflow: hidden;
    .reset-uni-forms {
        overflow: hidden auto;
        height: calc(100vh - 444rpx);
    }
    .form-item {
        background: $uni-bg-color;
        border-radius: 20rpx;
        padding: 30rpx;

        .reset-input {
            border: none;
            height: 88rpx;
            background-color: #f6f6f6 !important;
            border-radius: 46rpx;

            &.placeholder {
                text-indent: 40rpx;
                align-items: center;
                color: #999;
                line-height: 88rpx;
            }
            &.text {
                :deep(.uni-list-item__content-title) {
                    // 字数超出一行隐藏
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
            :deep(.uni-list--border) {
                display: none;
            }
        }
    }

    .uni-list-cell {
        display: flex;
        justify-content: space-between;
        margin: 20rpx 0;
        padding: 22rpx 48rpx;
        background-color: #f6f6f6;
        border-radius: 46rpx;
        border: 2rpx solid transparent;

        &.active {
            border-color: var(--primary-color);
            background-color: #e8fff8;

            :deep(.uni-radio-input) {
                background-color: var(--primary-color) !important;
                border-color: var(--primary-color) !important;
            }
        }
    }

    .res-popup {
        background-color: $uni-bg-color !important;
        border-radius: 20rpx 20rpx 0 0;

        .handler {
            display: flex;
            justify-content: space-between;
            padding: 20rpx 30rpx;
            align-items: center;

            .title {
                flex: 1;
                text-align: center;
            }
        }

        .uni-list {
            display: flex;
            justify-content: space-between;
            margin: 20rpx 0;
            padding: 22rpx 48rpx;
            border-bottom: 1rpx solid #d8d8d8;

            :deep(.uni-radio-input) {
                border: none;
                background-color: $uni-bg-color !important;

                svg {
                    transform: translate(-50%, -50%) scale(1);
                    background-color: $uni-bg-color;

                    path {
                        fill: var(--primary-color);
                    }
                }
            }
        }

        .construction-site {
            display: flex;
            .construction {
                .construction-item {
                    width: 200rpx;
                    height: 88rpx;
                    line-height: 88rpx;

                    &.active {
                        color: var(--primary-color);

                        .text {
                            border-left: 2px solid var(--primary-color);
                            padding-left: 20px;
                        }
                    }

                    .text {
                        border-left: 2px solid transparent;
                        padding-left: 20px;
                    }
                }
            }

            .site {
                flex: 1;
                overflow: auto;
                height: 100%;
                max-height: 600rpx;
                :deep(.uni-list) {
                    margin: 0;
                }
            }
        }
        .construction-footer {
            display: flex;
            justify-content: space-between;
            padding: 30rpx;
            .siteCancel,
            .siteSave {
                flex: 1;
                border: none;
                font-size: 30rpx;
                margin: 0 10rpx;
                border-radius: 46rpx;
                &:after {
                    display: none;
                }
            }
            .siteSave {
                background: var(--primary-color);
            }
        }
    }

    .footer {
        // 页脚定位
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 30rpx;
        background-color: $uni-bg-color;

        .text {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            line-height: 34rpx;
            text-align: left;
            font-style: normal;
        }

        .reset-checkbox {
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            text-align: left;
            font-style: normal;
            margin: 24rpx 0;
        }

        .releaseSave {
            background: linear-gradient(90deg, #3cdc6b 0%, var(--primary-color) 100%);
            border-radius: 46rpx;

            &.active {
                background: #bbbbbb;
                color: $uni-bg-color;
            }
        }
    }
}

.popup-error {
    width: 70vw;
    height: 520rpx;
    background: #ffffff;
    border-radius: 20rpx;
    text-align: center;
    margin: 60rpx;
    padding: 60rpx;

    img {
        width: 116rpx;
        height: 148rpx;
    }

    .success-txt {
        font-weight: 500;
        font-size: 34rpx;
        color: #333333;
        font-style: normal;
        text-align: center;
        padding: 24rpx 0;
    }

    .tips {
        text-align: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        font-style: normal;
        overflow: hidden auto;
        height: 100rpx;
        // .error-txt {
        //   text-align: left;
        // }
    }

    .releaseSave {
        background: linear-gradient(90deg, #3cdc6b 0%, var(--primary-color) 100%);
        border-radius: 46rpx;
        margin-top: 52rpx;
    }
}
</style>
