<template>
    <view class="notification-scope">
        <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="确定人员" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" />
        <uv-tabs
            :list="state.tabsConfirmViewList"
            :current="state.activeTab"
            @click="clickTabs"
            :activeStyle="{ color: 'var(--primary-color)' }"
            :inactiveStyle="{ color: '#606266' }"
            lineWidth="20"
            :customStyle="{ background: '#fff' }"
            :itemStyle="{
                height: '50rpx',
                flex: 1,
                paddingBottom: '20rpx'
            }"
            lineColor="var(--primary-color)"
        ></uv-tabs>
        <uni-goods-nav :fill="true" :options="options" :button-group="state.customButtonGroup" @buttonClick="buttonClick" style="margin-top: 20px" />

        <uni-list class="reset-list">
            <uni-list-item v-for="item in state.scopeList" :key="item.id" :title="item.name" showArrow link @click="deptPeople(item.id)" />
        </uni-list>
    </view>
</template>

<script setup>
import { reactive } from "vue"
const tabsViewList = [
    {
        name: "未浏览 （0）",
        isView: false
    },
    {
        name: "已浏览 （0）",
        isView: true
    }
]
const tabsConfirmList = [
    {
        name: "未确定 （0）",
        isConfirm: false
    },
    {
        name: "已确定（0）",
        isConfirm: true
    }
]
const options = []

const state = reactive({
    scopeList: [],
    activeTab: 0,
    confirmView: "isView",
    tabsConfirmViewList: tabsViewList,
    propsForm: {
        id: "",
        identity: 1
    },
    customButtonGroup: [
        {
            text: "老师",
            backgroundColor: "#11C685",
            color: "#fff",
            identity: 1
        },
        {
            text: "家长",
            backgroundColor: "#fff",
            color: "#333",
            identity: 2
        }
    ]
})

const clickLeft = () => {
    uni.navigateBack()
}

const deptPeople = (deptId) => {
    const { id, identity } = state.propsForm
    const query = {
        id,
        [state.confirmView]: state.propsForm[state.confirmView]
    }
    // 家长
    if (identity == 2) {
        query.classesId = deptId
    } else {
        query.deptId = deptId
    }
    navigateTo({
        url: "/apps/notice/components/notificationScopePeople",
        query
    })
}
// 老师 家长切换
const buttonClick = (e) => {
    state.propsForm.identity = e.content.identity
    state.customButtonGroup.forEach((item) => {
        if (item.identity == e.content.identity) {
            item.backgroundColor = "#11C685"
            item.color = "#fff"
        } else {
            item.backgroundColor = "#fff"
            item.color = "#333"
        }
    })
    getNotifyInfo()
}
// 未浏览 已浏览切换
const clickTabs = (item) => {
    state.propsForm[state.confirmView] = item[state.confirmView]
    getNotifyInfo()
}

const getNotifyInfo = () => {
    http.post("/cloud/mobile/mess/publish/notifyInfo", state.propsForm).then(({ data }) => {
        state.scopeList = data
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.propsForm.id = options.id
    state.activeTab = 0
    // 未确定 已确定
    // if (options.confirmView == "isConfirm") {
    state.tabsConfirmViewList = tabsConfirmList
    state.propsForm.isConfirm = false
    state.confirmView = "isConfirm"
    state.tabsConfirmViewList[0].name = `未确定（${options.receiveUsers}）`
    state.tabsConfirmViewList[1].name = `已确定（${options.confirmUsers}）`
    // } else {
    //     // 未浏览 已浏览
    //     state.tabsConfirmViewList = tabsViewList
    //     state.propsForm.isView = false
    //     state.confirmView = "isView"
    // }
    getNotifyInfo()
})
</script>

<style lang="scss" scoped>
.notification-scope {
    height: 100vh;
    background: $uni-bg-color-grey;

    .reset-list {
        margin: 10rpx 0;
        overflow: hidden auto;
        height: calc(100vh - 164rpx);
    }

    :deep(.uni-tab__cart-box) {
        background: $uni-bg-color-grey;
    }
}
</style>
