<template>
    <div class="activity">
        <z-paging ref="paging" v-model="state.activityList" @query="queryList" class="activity">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="评价活动" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <uv-tabs
                    :list="tabsList"
                    @click="changeTabs"
                    lineColor="var(--primary-color)"
                    :activeStyle="{ color: 'var(--primary-color)' }"
                    :inactiveStyle="{ color: '#999999' }"
                    :itemStyle="{
                        height: '50rpx',
                        flex: 1,
                        paddingBottom: '20rpx',
                        background: '#fff'
                    }"
                >
                </uv-tabs>
                <!-- 搜索 -->
                <view class="search_input">
                    <input type="text" class="query_input_text" v-model="state.title" placeholder="搜索活动名称" @input="onRefresh" />
                </view>
                <!-- 筛选 -->
                <div class="sift_box">
                    <div class="status">
                        <uni-data-select class="status_select" v-model="state.status" :localdata="statusList" :clear="false" @change="onRefresh" placeholder="所有"></uni-data-select>
                    </div>
                    <div class="date">
                        <uni-datetime-picker :border="false" :clearIcon="false" type="daterange" placeholder="选择时间" v-model="state.startEndDate" @change="onRefresh" />
                    </div>
                </div>
            </template>

            <!-- 列表 -->
            <div v-if="state.activityList?.length" class="activity_list">
                <view class="activity_item" v-for="item in state.activityList" :key="item.id">
                    <div class="title_box" @click="handlerDetails('evaluationInfo', item)">
                        <span class="label" :style="{ background: activityStatus[item.status]?.color }">{{ activityStatus[item.status]?.name }}</span>
                        <div class="title ellipsis">
                            {{ item.title }}
                        </div>
                        <uni-icons color="#8C8C8C" class="icon_class" type="right" size="18"></uni-icons>
                    </div>
                    <span class="date">
                        <span>{{ item.startDate + "-" + item.endDate }}</span>
                        <span>今日参与：{{ item.partakePersonNum }}人</span>
                    </span>
                    <span class="classes ellipsis" style="padding: 0">
                        {{ item.names?.join("、") || "" }}
                    </span>
                    <div v-if="item.status == 1 && state.pageType != 1" class="click_text" @click="handlerDetails('index', item)">立即评价</div>
                </view>
            </div>
            <view v-if="!state.activityList?.length && pageLoading" class="loading">
                <uv-loading-icon :show="pageLoading" text="加载中..." :vertical="true"></uv-loading-icon>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const activityStatus = {
    0: { name: "未开始", color: "#FFFFBB37" },
    1: { name: "进行中", color: "var(--primary-color)" },
    2: { name: "已结束", color: "#595959" }
}
const paging = ref(null)
const pageLoading = ref(false)
const state = reactive({
    activityList: [],
    activityParams: {
        personId: "",
        evalTypeId: ""
    },
    status: null,
    title: "",
    pageType: 1,
    startEndDate: []
})
const statusList = ref([
    { value: null, text: "所有" },
    { value: 0, text: "未开始" },
    { value: 1, text: "进行中" },
    { value: 2, text: "已结束" }
])
const tabsList = [
    {
        key: 1,
        name: "我参与的"
    },
    {
        key: 2,
        name: "我评价的"
    }
]

function clickLeft() {
    uni.navigateBack()
}
const onRefresh = () => {
    paging.value?.reload()
}
// 调用List数据
function queryList(pageNo, pageSize) {
    const { title, status, startEndDate = [], activityParams = {}, pageType } = state
    const { evalTypeId, personId } = activityParams
    const [startDate, endDate] = startEndDate
    const params = {
        pageNo,
        pageSize,
        studentId: personId,
        startDate,
        pageType,
        endDate,
        status,
        title,
        evalTypeId
    }
    pageLoading.value = true
    http.post("/app/evalActivity/pageByEltern", params)
        .then(({ data }) => {
            paging.value?.complete(data.list || false)
        })
        .finally(() => {
            pageLoading.value = false
        })
}

// 评价详情
const handlerDetails = (type, item) => {
    let params = item
    let url = "/apps/evalActivity/immediateEvaluation/index"
    if (type == "evaluationInfo") {
        url = "/apps/evalActivity/immediateEvaluation/evaluationInfo"
        params = {
            id: item.id
        }
    }
    navigateTo({
        url,
        query: params
    })
}

function changeTabs(tabs) {
    state.pageType = tabs.key
    onRefresh()
}

onLoad((item) => {
    state.activityParams = item || {}
})
</script>

<style lang="scss" scoped>
.search_input {
    background: $uni-bg-color;
    padding: 20rpx 30rpx;
    .query_input_text {
        padding: 10rpx 20rpx;
        border-radius: 30rpx;
        font-size: 30rpx;
        background: $uni-bg-color-grey;
    }
}

.sift_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: $uni-bg-color;
    padding: 0 30rpx 20rpx;

    .status {
        max-width: 46%;
        flex: 1;

        .status_select {
            :deep(.uni-select) {
                border: none;

                .uni-select__input-text {
                    width: auto;
                    font-size: 28rpx;
                    color: #666666;
                }

                .uni-icons:before {
                    content: "";
                    display: block;
                    border: 10rpx solid transparent;
                    margin-left: 6rpx;
                }

                .uniui-bottom:before {
                    border-top: 10rpx solid var(--primary-color);
                    border-bottom-width: 1px;
                    margin-top: 6rpx;
                }

                .uniui-top:before {
                    border-bottom-color: var(--primary-color);
                    border-top-width: 1px;
                    margin-bottom: 6rpx;
                }
            }

            :deep(.uni-select__selector) {
                left: -15px;
                width: 100vw;
                top: calc(100% + 6px) !important;
                border: none;
                border-radius: 0rpx 0rpx 12rpx 12rpx;
                box-shadow: none;
                padding: 0px 15px;
            }

            :deep(.uni-select__input-box) {
                max-width: 100%;
            }

            :deep(.uni-popper__arrow_bottom) {
                display: none;
            }

            :deep(.uni-select__selector-item:last-child) {
                border: none !important;
            }

            :deep(.uni-select__selector-item) {
                border-bottom: 1rpx solid #d8d8d8;
                line-height: 80rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
            }
        }
    }

    .date {
        display: flex;
        align-items: center;
        width: 54%;

        &::after {
            content: "";
            display: block;
            border: 10rpx solid transparent;
            border-top: 10rpx solid var(--primary-color);
            border-bottom-width: 1px;
            margin-left: 10rpx;
        }

        :deep(.icon-calendar) {
            display: none;
        }
    }
}
.activity {
    background: $uni-bg-color-grey;
}
.loading {
    margin-top: 300rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity_list {
    padding: 20rpx 30rpx;
    background: $uni-bg-color-grey;

    .activity_item {
        min-height: 100rpx;
        padding: 32rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        margin-bottom: 20rpx;

        .title_box {
            display: flex;
            align-items: center;
            margin-bottom: 18rpx;
            width: 100%;

            .label {
                width: 80rpx;
                padding: 0rpx 4rpx;
                height: 36rpx;
                background: var(--primary-color);
                border-radius: 6rpx;
                font-weight: 500;
                font-size: 20rpx;
                color: $uni-bg-color;
                line-height: 36rpx;
                text-align: center;
                margin-right: 10rpx;
            }

            .title {
                font-weight: 500;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
                flex: 1;
            }
        }

        .date {
            font-weight: 400;
            font-size: 26rpx;
            color: #666666;
            line-height: 36rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 10rpx 0;
        }

        .classes {
            font-weight: 400;
            font-size: 26rpx;
            color: #666666;
            line-height: 36rpx;
        }

        .click_text {
            margin-top: 30rpx;
            padding-top: 30rpx;
            border-top: 1rpx solid $uni-border-color;
            font-weight: 400;
            font-size: 28rpx;
            color: var(--primary-color);
            line-height: 40rpx;
            text-align: center;
        }
    }
}
</style>
<style lang="scss">
/* #ifdef MP-WEIXIN */
.activity {
    :deep(.uni-date-x) {
        justify-content: flex-end !important;
    }
}
/* #endif */
</style>
