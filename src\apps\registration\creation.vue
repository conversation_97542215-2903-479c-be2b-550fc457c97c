<template>
    <view>
        <view class="creationPage" v-if="!state.showcropping">
            <!-- 头部 -->
            <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="新建活动报名" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>

            <!-- 填写的表单页 -->
            <view class="creationForm">
                <view class="creationFormBox">
                    <uv-form labelPosition="left" :model="creationFormObj" ref="creationFormRef" labelWidth="auto" :borderBottom="false" :rules="rules">
                        <view class="setItemBox">
                            <uv-form-item label="活动名称" required prop="activityName">
                                <uv-textarea autoHeight border="none" style="text-align: right; padding: unset" v-model="creationFormObj.activityName" maxlength="50" placeholder="请输入（不超过50字）"></uv-textarea>
                            </uv-form-item>
                            <uv-form-item label="子项设置" required prop="subTypeName" @click="showTypeSelect">
                                <uv-input v-model="creationFormObj.subTypeName" border="none" inputAlign="right" placeholder="请选择子项设置"> </uv-input>
                                <template v-slot:right>
                                    <uv-icon name="arrow-right"></uv-icon>
                                </template>
                            </uv-form-item>

                            <view class="subBox" v-if="creationFormObj.subType === 1">
                                <uv-form-item label="子项目名称" prop="subName" :key="index" v-for="(item, index) in creationFormObj.subList">
                                    <uv-input v-model="item.subName" inputAlign="right" maxlength="20" border="none" placeholder="请输入（20字内）"> </uv-input>
                                    <template v-slot:right>
                                        <image @click="delsubListItem(index)" class="addImg" src="@nginx/workbench/registration/plusIcon.png"></image>
                                    </template>
                                </uv-form-item>
                                <view class="addSub">
                                    <view style="width: 1rpx; height: 64rpx"></view>
                                    <uv-button @click="addSubBtn" style="width: 244rpx; height: 64rpx" type="primary" iconSize="14" icon="plus" iconColor="var(--primary-color)" color="var(--primary-color)" :plain="true" text="新增子项目"></uv-button>
                                </view>
                            </view>

                            <view class="subBox2" v-if="creationFormObj.subType === 2">
                                <view :key="item.subName + index" class="subBox2_item" v-for="(item, index) in creationFormObj.subList">
                                    <uv-form-item label="子项目名称" prop="subName">
                                        <uv-input v-model="item.subName" inputAlign="right" maxlength="20" border="none" placeholder="请输入（20字内）"> </uv-input>
                                        <template v-slot:right>
                                            <image @click="delsubListItem(index)" class="addImg" src="@nginx/workbench/registration/plusIcon.png"></image>
                                        </template>
                                    </uv-form-item>
                                    <uv-form-item class="numQuantityForm" label="报名数量限制">
                                        <uv-input type="number" v-model="item.numQuantity" inputAlign="right" border="none" placeholder="请输入（不填则为不限制报名数）"> </uv-input>
                                    </uv-form-item>
                                </view>
                                <view class="addSub">
                                    <view style="width: 1rpx; height: 64rpx"></view>
                                    <uv-button @click="addSubBtn" style="width: 244rpx; height: 64rpx" type="primary" iconSize="14" icon="plus" iconColor="var(--primary-color)" color="var(--primary-color)" :plain="true" text="新增子项目"></uv-button>
                                </view>
                            </view>

                            <uv-form-item label="报名对象范围" required prop="treeSubmitListName" @click="showSelectMember">
                                <uv-input v-model="creationFormObj.treeSubmitListName" border="none" inputAlign="right" placeholder="请选择"> </uv-input>
                                <template v-slot:right>
                                    <uv-icon name="arrow-right"></uv-icon>
                                </template>
                            </uv-form-item>

                            <uv-form-item label="报名数量限制" prop="total" v-if="creationFormObj.subType === 1">
                                <uv-input type="number" v-model="creationFormObj.total" border="none" placeholder="请输入（不填则为不限制报名数）" inputAlign="right"> </uv-input>
                            </uv-form-item>

                            <!-- <uv-form-item @click="openactivityStartTime" label="活动时间" prop="activityStartTimeStr" required>
                                <uv-input v-model="creationFormObj.activityStartTimeStr" border="none" placeholder="请选择时间" inputAlign="center"> </uv-input>
                                <view>至</view>
                                <uv-input v-model="creationFormObj.activityEndTimeStr" border="none" placeholder="请选择时间" inputAlign="right"> </uv-input>
                            </uv-form-item> -->

                            <!-- <uv-form-item label="报名时间" prop="registerStartTimeStr" required @click="openregisterStartTime">
                                <uv-input v-model="creationFormObj.registerStartTimeStr" border="none" placeholder="请选择时间" inputAlign="center"> </uv-input>
                                <view>至</view>
                                <uv-input v-model="creationFormObj.registerEndTimeStr" border="none" placeholder="请选择时间" inputAlign="right"> </uv-input>
                            </uv-form-item> -->

                            <uv-form-item label="活动时间" required prop="activityStartTimeStr">
                                <uni-datetime-picker class="reset-datetime-picker" v-model="creationFormObj.activityStartTimeStr" type="datetimerange" :border="false" :hide-second="true" :clear-icon="false" rangeSeparator="至" @change="maskActivityClick" />
                            </uv-form-item>

                            <uv-form-item label="报名时间" required prop="registerStartTimeStr">
                                <uni-datetime-picker class="reset-datetime-picker" v-model="creationFormObj.registerStartTimeStr" type="datetimerange" :border="false" :hide-second="true" :clear-icon="false" rangeSeparator="至" @change="maskRegisterClick" />
                            </uv-form-item>
                        </view>

                        <view class="setItemInfo">
                            <uv-form-item label="活动介绍" labelPosition="top" prop="activityIntroduction" required>
                                <view style="width: 1rpx; height: 1rpx"></view>
                                <uv-textarea
                                    :customStyle="{
                                        backgroundColor: '#F2F2F2',
                                        marginTop: '24rpx'
                                    }"
                                    v-model="creationFormObj.activityIntroduction"
                                    :maxlength="200"
                                    height="150"
                                    count
                                ></uv-textarea>
                            </uv-form-item>
                            <uv-form-item label="备注" labelPosition="top" prop="activityRemarks" required>
                                <view style="width: 1rpx; height: 1rpx"></view>
                                <uv-textarea
                                    autoHeight
                                    :customStyle="{
                                        backgroundColor: '#F2F2F2',
                                        marginTop: '24rpx'
                                    }"
                                    v-model="creationFormObj.activityRemarks"
                                    :maxlength="20"
                                    count
                                ></uv-textarea>
                            </uv-form-item>
                        </view>
                    </uv-form>

                    <!-- 处理图片的地方 -->
                    <view class="coverImgBox">
                        <view class="coverImgBox_name"> 封面图片</view>
                        <!-- {{ creationFormObj.fileList }} -->
                        <view class="upBox">
                            <view v-if="creationFormObj.coverImg" class="upBox_img">
                                <image @click="delcoverImg" class="chaIcon" src="@nginx/workbench/registration/chaImg.png"></image>
                                <image :src="creationFormObj.coverImg" mode="aspectFill" style="width: 80px; height: 80px" />
                            </view>

                            <!-- 自定义上传区域 -->
                            <view class="custom-upload-area" @click="chooseImage">
                                <view class="upload-icon">
                                    <text class="upload-plus">+</text>
                                </view>
                                <text class="upload-text">上传图片</text>
                            </view>
                        </view>
                        <view class="tipcoverImgBox"> 图片格式jpg/png/bmp,大小5M内。建议图片比例为9：16 </view>
                    </view>
                </view>
            </view>
            <view class="footBoxOut">
                <view class="footBox">
                    <view class="publish" @click="publishBtn">
                        <text class="publishName">发布报名</text>
                    </view>
                </view>
            </view>
            <!-- 这是子项弹出的选择区域 -->
            <uv-action-sheet round="20" ref="typeSelect" :actions="actions" title="子项设置" @select="subTypeSelect"> </uv-action-sheet>
            <!-- 弄的一个日历组件 -->
            <uv-calendars :date="state.defaultTime" :allowSameDay="true" ref="calendars" @confirm="calendarsConfirm" mode="range" confirmColor="var(--primary-color)" color="var(--primary-color)" />
        </view>
        <view v-if="state.showcropping">
            <cropping-img ref="croppingimgRef" :url="state.rawUrl" @returnImg="returnImg"></cropping-img>
        </view>
    </view>
</template>
<script setup>
import { selectMember } from "./treeSubmitList.js"
import CroppingImg from "./components/croppingImg.vue"
import dayjs from "dayjs"

const calendars = ref(null)
const creationFormRef = ref(null)
const croppingimgRef = ref(null)
const creationFormObj = ref({
    coverImg: "@nginx/workbench/registration/coverImg.png",
    fileList: [],
    activityName: "",
    activityTimeStr: [],
    subList: [
        {
            subName: ""
        }
    ]
})

const state = ref({
    defaultTime: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    rawUrl: "",
    showcropping: false,
    saveparameter: {}
})

const rules = ref({
    activityName: [
        {
            required: true,
            message: "活动名称不能为空",
            trigger: ["blur", "change"]
        }
    ],
    subTypeName: [
        {
            required: true,
            message: "子项设置不能为空",
            trigger: ["blur", "change"]
        }
    ],
    activityIntroduction: [
        {
            required: true,
            message: "活动介绍不能为空",
            trigger: ["blur", "change"]
        }
    ],
    activityRemarks: [
        {
            required: true,
            message: "备注不能为空",
            trigger: ["blur", "change"]
        }
    ],
    activityStartTimeStr: [
        {
            required: true,
            message: "活动时间不能为空",
            trigger: ["blur", "change"]
        }
    ],
    registerStartTimeStr: [
        {
            required: true,
            message: "报名时间不能为空",
            trigger: ["blur", "change"]
        }
    ]
})

const hideKeyboard = () => {
    uni.hideKeyboard()
}

const typeSelect = ref(null)

const actions = ref([
    {
        name: "各子项不单独报名",
        subType: 1,
        color: creationFormObj.value.subType === 1 ? "var(--primary-color)" : "#666666"
    },
    {
        name: "各子项单独报名",
        subType: 2,
        color: creationFormObj.value.subType === 2 ? "var(--primary-color)" : "#666666"
    }
])

watch(
    () => creationFormObj.value.subType,
    (value) => {
        actions.value = [
            {
                name: "各子项不单独报名",
                subType: 1,
                color: creationFormObj.value.subType === 1 ? "var(--primary-color)" : "#666666"
            },
            {
                name: "各子项单独报名",
                subType: 2,
                color: creationFormObj.value.subType === 2 ? "var(--primary-color)" : "#666666"
            }
        ]
    }
)

const clickLeft = () => {
    uni.navigateBack()
}

// 自定义选择图片方法
const chooseImage = () => {
    uni.chooseImage({
        count: 1, // 只能选择一张图片
        sourceType: ["album"], // 只能从相册选择
        success: (res) => {
            const tempFilePath = res.tempFilePaths[0]
            const tempFile = res.tempFiles[0]
            const fileSize = tempFile.size

            // 校验图片大小（5MB = 5242880字节）
            if (fileSize > 5242880) {
                uni.showToast({
                    icon: "none",
                    title: "图片大小超出最大限制（5MB）"
                })
                return
            }

            // 校验图片格式 - 使用 tempFile.type 或 tempFile.name 进行校验
            let isValidFormat = false

            // 方法1：通过 type 字段校验（推荐）
            if (tempFile.type && tempFile.type.startsWith("image/")) {
                const imageType = tempFile.type.split("/")[1]
                isValidFormat = ["jpeg", "jpg", "png", "bmp"].includes(imageType.toLowerCase())
            }
            // 方法2：如果没有 type 字段，通过文件名校验
            else if (tempFile.name) {
                isValidFormat = isImageFileName(tempFile.name)
            }

            if (!isValidFormat) {
                uni.showToast({
                    icon: "none",
                    title: "图片格式不支持，请选择jpg/png/bmp格式"
                })
                return
            }

            // 上传图片
            uni.showLoading({
                title: "图片加载中"
            })

            http.uploadFile("/file/common/upload", tempFilePath, {
                folderType: "visitorFace"
            })
                .then((res) => {
                    state.value.rawUrl = res
                    state.value.showcropping = true
                })
                .catch(() => {
                    uni.showToast({
                        icon: "none",
                        title: "图片上传失败"
                    })
                })
                .finally(() => {
                    uni.hideLoading()
                })
        },
        fail: () => {
            uni.showToast({
                icon: "none",
                title: "选择图片失败"
            })
        }
    })
}

// 校验图片格式的方法
function isImageFileName(fileName) {
    if (!fileName) return false
    return /\.(jpg|jpeg|png|bmp)$/i.test(fileName)
}

const returnImg = (url) => {
    if (url) {
        creationFormObj.value.coverImg = url
        state.value.showcropping = false
    } else {
        creationFormObj.value.coverImg = ""
        state.value.showcropping = false
    }
}

const delcoverImg = () => {
    creationFormObj.value.coverImg = ""
}

const showTypeSelect = () => {
    typeSelect.value.open()
    hideKeyboard()
}

const subTypeSelect = (e) => {
    creationFormObj.value.subTypeName = e.name
    creationFormObj.value.subType = e.subType

    // 清空数据
    creationFormObj.value.subList = [
        {
            subName: ""
        }
    ]

    creationFormRef.value.validateField(["subTypeName"])
}

const addSubBtn = () => {
    creationFormObj.value.subList = [
        ...creationFormObj.value.subList,
        {
            subName: ""
        }
    ]
}

const delsubListItem = (index) => {
    creationFormObj.value.subList.splice(index, 1)
}

// 时间
const openactivityStartTime = () => {
    hideKeyboard()
    state.value.timeType = "activity"
    calendars.value.open()
}

const openregisterStartTime = () => {
    hideKeyboard()
    state.value.timeType = "register"
    calendars.value.open()
}
const calendarsConfirm = (val) => {
    console.log("val", val)

    if (state.value.timeType === "activity") {
        creationFormObj.value.activityStartTimeStr = val.range.before
        creationFormObj.value.activityEndTimeStr = val.range.after
        creationFormRef.value.validateField(["activityStartTimeStr"])
    }
    if (state.value.timeType === "register") {
        creationFormObj.value.registerStartTimeStr = val.range.before
        creationFormObj.value.registerEndTimeStr = val.range.after
        creationFormRef.value.validateField(["registerStartTimeStr"])
    }
}

const showSelectMember = () => {
    // 跳进选人的地方
    navigateTo({
        url: "/apps/registration/selectMember"
    })
}

// 发布
const publishBtn = () => {
    // 表单校验
    console.log(creationFormRef.value)

    creationFormRef.value.validate().then(() => {
        if (state.value.saveparameter.belong === "2") {
            const params = {
                ...creationFormObj.value,
                treeSubmitList: selectMember.value.treeSubmitList
            }
            http.post("/app/activity/enroll/update", params).then((res) => {
                uni.showToast({
                    title: "操作成功",
                    icon: "none"
                })
                uni.reLaunch({
                    url: "/apps/registration/index"
                })
            })
        } else {
            const params = {
                ...creationFormObj.value,
                treeSubmitList: selectMember.value.treeSubmitList
            }
            http.post("/app/activity/enroll/create", params).then((res) => {
                uni.showToast({
                    title: "操作成功",
                    icon: "none"
                })
                uni.reLaunch({
                    url: "/apps/registration/index"
                })
            })
        }
    })
}

// 可以多次触发
onShow(() => {
    creationFormObj.value.treeSubmitListName = JSON.parse(JSON.stringify(selectMember.value.treeSubmitListName))
})

const handlerDetailsInfo = (data) => {
    const params = {
        activityId: data.activityId
    }
    http.post("/app/activity/enroll/info", params).then(({ data }) => {
        const { id, activityName, total, activityIntroduction, activityRemarks, coverImg, registerStartTime, registerEndTime, activityStartTime, activityEndTime, subType, enrollSubList, selectedNodesInfo } = data
        // 查到详情回显一下
        creationFormObj.value.id = id
        creationFormObj.value.activityName = activityName
        creationFormObj.value.total = total
        creationFormObj.value.activityIntroduction = activityIntroduction
        creationFormObj.value.activityRemarks = activityRemarks
        creationFormObj.value.coverImg = coverImg
        creationFormObj.value.registerStartTimeStr = registerStartTime
        creationFormObj.value.registerEndTimeStr = registerEndTime
        creationFormObj.value.activityStartTimeStr = activityStartTime
        creationFormObj.value.activityEndTimeStr = activityEndTime
        creationFormObj.value.subType = subType
        creationFormObj.value.subTypeName = subType === 1 ? "各子项不单独报名" : "各子项单独报名"
        if (selectedNodesInfo) {
            // 转对象
            selectMember.value.treeSubmitList = JSON.parse(selectedNodesInfo)
            selectMember.value.treeSubmitListName = selectMember.value.treeSubmitList.map((item) => item.name).join("、")
            creationFormObj.value.treeSubmitListName = JSON.parse(JSON.stringify(selectMember.value.treeSubmitListName))
        }

        if (subType === 1) {
            creationFormObj.value.subList = enrollSubList.map((item) => {
                return {
                    subName: item.subName
                }
            })
        }
        if (subType === 2) {
            creationFormObj.value.subList = enrollSubList.map((item) => {
                return {
                    subName: item.subName,
                    numQuantity: item.numQuantity
                }
            })
        }
    })
}
// 选活动时间
const maskActivityClick = (e) => {
    creationFormObj.value.activityStartTimeStr = e[0]
    creationFormObj.value.activityEndTimeStr = e[0]
}
// 报名时间
const maskRegisterClick = (e) => {
    creationFormObj.value.registerStartTimeStr = e[0]
    creationFormObj.value.registerEndTimeStr = e[0]
}
onLoad((options) => {
    console.log("options", options)
    state.value.saveparameter = options
    // 可能加载进来需要查询详情一下
    // belong 是2 说明 是创建人进来编辑的
    if (options.belong === "2") {
        // 查详情
        handlerDetailsInfo(options)
    }
})
</script>
<style lang="scss" scoped>
.creationPage {
    font-size: 28rpx;
    // height: 100vh;
    // overflow: hidden;
    display: flex;
    flex-direction: column;
}

.title_box {
    width: 100%;
    text-align: center;
    line-height: 88rpx;
    font-size: 34rpx;
    font-weight: 500;
    color: $uni-text-color;
}

.creationForm {
    // height: calc(100vh - 88rpx);
    // overflow: auto;
    background-color: $uni-bg-color-grey;

    .creationFormBox {
        margin: 24rpx 32rpx;
        // background: $uni-bg-color;
        // border-radius: 12rpx;
    }
}

.setItemBox,
.setItemInfo {
    background: $uni-bg-color;
    border-radius: 12rpx;
}

.setItemBox {
    margin-bottom: 24rpx;

    :deep(.uv-form-item) {
        border-bottom: 1px solid $uni-border-color;
        padding-left: 44rpx;
        padding-right: 22rpx;
    }
}

.setItemInfo {
    margin-bottom: 24rpx;

    :deep(.uv-form-item) {
        padding-left: 44rpx;
        padding-right: 22rpx;
    }
}

.subname {
    padding-bottom: 24rpx;
}

.addImg {
    width: 36rpx;
    height: 36rpx;
}

.coverImgBox {
    background: $uni-bg-color;
    border-radius: 12rpx;
    padding: 22rpx;

    .coverImgBox_name {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 28rpx;
        text-align: left;
        font-style: normal;
        padding-bottom: 24rpx;
    }
}

.tipcoverImgBox {
    padding-top: 24rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #adadad;
    line-height: 28rpx;
    font-style: normal;
}

.footBoxOut {
    background-color: $uni-bg-color-grey;
    height: 176rpx;
}

.footBox {
    position: fixed;
    // padding: 22rpx 30rpx;
    width: 100%;
    height: 176rpx;
    background: $uni-bg-color;
    bottom: 0px;
    z-index: 2;

    .publish {
        margin: 22rpx 30rpx;
        text-align: center;
        height: 92rpx;
        line-height: 92rpx;
        background: var(--primary-color);
        border-radius: 10rpx;

        .publishName {
            font-weight: 400;
            font-size: 32rpx;
            color: $uni-text-color-inverse;
            line-height: 44rpx;
            text-align: center;
            font-style: normal;
        }
    }
}

.subBox {
    :deep(.uv-form-item) {
        border-bottom: unset;
    }
}

.addSub {
    display: flex;
    padding-left: 44rpx;
    padding-bottom: 36rpx;
    border-bottom: 1px solid $uni-border-color;
}

.subBox2 > .subBox2_item:nth-last-child(2) {
    .numQuantityForm {
        border-bottom: unset !important;
    }
}

.setItemInfo {
    :deep(.uv-textarea__count) {
        background-color: unset !important;
    }

    :deep(.uni-textarea-textarea) {
        color: rgba(0, 0, 0, 0.85) !important;
    }
}

.upBox {
    display: flex;
    align-items: center;

    .upBox_img {
        margin: 0 16rpx 16rpx 0;
        position: relative;

        .chaIcon {
            width: 36rpx;
            top: 0;
            right: 0;
            position: absolute;
            height: 36rpx;
            z-index: 1;
        }
    }
}

.reset-datetime-picker {
    :deep(.uni-icons) {
        display: none;
    }
}

/* 自定义上传区域样式 */
.custom-upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 160rpx;
    height: 160rpx;
    border: 2rpx dashed #d9d9d9;
    border-radius: 8rpx;
    background-color: #fafafa;
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
        background-color: #f0f0f0;
        border-color: #bfbfbf;
    }
}

.upload-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 8rpx;
}

.upload-plus {
    font-size: 36rpx;
    color: #999999;
    font-weight: 300;
    line-height: 1;
}

.upload-text {
    font-size: 24rpx;
    color: #666666;
    text-align: center;
}
</style>
