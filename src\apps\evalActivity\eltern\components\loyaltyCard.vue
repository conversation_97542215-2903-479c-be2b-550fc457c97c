<template>
    <div class="loyalty_card_com">
        <z-paging ref="paging" v-model="state.loyaltyCardlist" @query="queryList" :auto="false" :fixed="false">
            <template #top>
                <div class="statistics">
                    <div class="statistics_item">
                        <span class="number">{{ state.countWriteOffCard }}</span>
                        <span class="text">获得积分卡</span>
                    </div>
                    <div class="split_line"></div>
                    <div class="statistics_item">
                        <span class="number">{{ state.countWriteOffScore }}</span>
                        <span class="text">核销积分</span>
                    </div>
                </div>
                <div class="bg_line"></div>
                <div class="loyalty_card_content">
                    <div class="date_box">
                        <span>核销时间</span>
                        <span class="reset-datetime-picker">
                            <uni-datetime-picker :border="false" :clearIcon="false" type="daterange" placeholder="选择时间" v-model="state.scoreStartEndTime" @change="calendarsConfirm" />
                            <uni-icons type="right" size="14" color="#8C8C8C"></uni-icons>
                        </span>
                    </div>
                </div>
            </template>
            <div class="card_list">
                <div class="card_item" @click="cardInfo(item)" v-for="item in state.loyaltyCardlist" :key="item.evalTypeId">
                    <image mode="aspectFill" class="image" :src="item.medalIconUrl || item.scoreCardIconUrl" alt="" />
                    <span class="ellipsis score_card_name">{{ item.scoreCardName }}</span>
                </div>
            </div>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <details-popup ref="detailsPopupRef" />
    </div>
</template>

<script setup>
import { nextTick } from "vue"
import DetailsPopup from "./detailsPopup.vue"

const props = defineProps({
    parame: {
        type: Object,
        default: () => {}
    }
})
const paramsIntegral = ref({})
const detailsPopupRef = ref(null)
const paging = ref(null)
const state = reactive({
    scoreStartEndTime: [],
    loyaltyCardlist: [],
    countWriteOffCard: 0,
    countWriteOffScore: 0
})

// 调用List数据
function queryList(pageNo, pageSize) {
    const [scoreStartTime, scoreEndTime] = state.scoreStartEndTime
    const params = {
        writeOffStartTime: scoreStartTime,
        writeOffEndTime: scoreEndTime,
        ...paramsIntegral.value,
        identity: 0,
        pageNo,
        pageSize
    }

    http.post("/app/evalScoreCard/pageUserScoreCard", params).then(({ data }) => {
        paging.value?.complete(data.list)
    })
}

// 得分时间筛选
const calendarsConfirm = () => {
    paging.value.reload()
}

function cardInfo(data) {
    // data.medalIconUrl = ''
    detailsPopupRef.value.open("loyaltyCard", data)
}

async function getUserScoreCardFn() {
    const params = {
        ...paramsIntegral.value,
        identity: 0
    }
    await http.post("/app/evalScoreCard/getUserScoreCardInfo", params).then(({ data }) => {
        state.countWriteOffCard = data.countWriteOffCard
        state.countWriteOffScore = data.countWriteOffScore
    })
}

watch(
    () => props.parame,
    (value) => {
        if (value.personId) {
            paramsIntegral.value = value
            nextTick(async () => {
                await getUserScoreCardFn()
                paging.value?.reload()
            })
        } else {
            paging.value?.complete(false)
        }
    },
    {
        immediate: true,
        deep: true
    }
)
</script>

<style lang="scss" scoped>
.loyalty_card_com {
    height: 100%;

    .statistics {
        display: flex;
        height: 146rpx;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20rpx;

        .split_line {
            width: 2rpx;
            height: 60rpx;
            background: $uni-border-color;
        }

        .statistics_item {
            width: 46%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-evenly;

            .number {
                font-size: 36rpx;
                color: #262626;
                line-height: 50rpx;
            }

            .text {
                font-weight: 400;
                font-size: 26rpx;
                color: rgba(0, 0, 0, 0.45);
                line-height: 36rpx;
            }
        }
    }

    .bg_line {
        width: 100%;
        height: 20rpx;
        background: $uni-bg-color-grey;
    }

    .loyalty_card_content {
        padding: 30rpx;

        .date_box {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            padding-bottom: 30rpx;
            border-bottom: 1rpx solid $uni-border-color;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .reset-datetime-picker {
                display: flex;
                align-items: center;

                :deep(.uniui-calendar) {
                    display: none;
                }
            }
        }
    }
    .card_list {
        padding-top: 30rpx;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;

        .card_item {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            height: 240rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;

            .image {
                width: 320rpx;
                height: 180rpx;
                background: $uni-bg-color-grey;
                border-radius: 20rpx;
            }

            .score_card_name {
                padding-top: 20rpx;
                width: 100%;
                text-align: center;
            }
        }
    }
}
</style>
