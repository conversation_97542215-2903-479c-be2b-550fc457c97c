<template>
    <view class="release_record">
        <z-paging ref="paging" v-model="dataList" @query="queryList">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="放行记录" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <view class="date_time">
                    <view class="title">
                        <text>放行时间</text>
                    </view>
                    <view class="select_time">
                        <picker mode="date" :value="query.startDate" @change="changeStartTime">
                            <view
                                :style="{
                                    color: query.startDate ? '#333333' : '#999999'
                                }"
                                class="time_input"
                                >{{ query.startDate || "开始时间" }}</view
                            >
                        </picker>
                        <view class="connect">至</view>
                        <picker mode="date" :value="query.endDate" @change="changeEndTime">
                            <view
                                class="time_input"
                                :style="{
                                    color: query.endDate ? '#333333' : '#999999'
                                }"
                                >{{ query.endDate || "结束时间" }}</view
                            >
                        </picker>
                    </view>
                </view>
            </template>
            <view class="record_list">
                <view class="record_item" v-for="item in dataList" :key="item.id">
                    <view v-for="it in labelList" :key="it.value">
                        <view class="record_item_box" v-if="it.value == 'name'">
                            <view class="title_box">
                                <text class="title">{{ it.label }}</text>
                                <view @click="showClick(item)">
                                    <uni-icons :type="item.isShow ? 'up' : 'down'" size="16"></uni-icons>
                                </view>
                            </view>
                            <text class="value title" :class="item.isShow ? 'show_text' : 'title_value'">
                                {{ item[it.value] }}
                            </text>
                        </view>
                        <view v-else class="record_item_box">
                            <text class="title">{{ it.label }}</text>
                            <text class="value" v-if="it.value == 'time'"> {{ item.startDate }} - {{ item.endDate }} </text>
                            <text class="value" v-else>
                                {{ item[it.value] }}
                            </text>
                        </view>
                    </view>
                </view>
            </view>
            <template #empty>
                <slot name="empty">
                    <yd-empty text="暂无数据" />
                </slot>
            </template>
        </z-paging>
    </view>
</template>

<script setup>
const query = ref({})
const paging = ref(null)
const dataList = ref([])
const labelList = ref([
    {
        label: "通行人员",
        value: "name"
    },
    {
        label: "放行人",
        value: "createBy"
    },
    {
        label: "放行日期",
        value: "dates"
    },
    {
        label: "放行时间",
        value: "time"
    },
    {
        label: "放行原因",
        value: "reason"
    }
])

// 调用List数据
async function queryList(pageNo, pageSize) {
    http.post("/cloud/quick/pass/page", { ...query.value, pageNo, pageSize })
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((res) => {
            paging.value.complete(false)
        })
}

function showClick(item) {
    item.isShow = !item.isShow
}

function changeStartTime(time) {
    query.value.startDate = time.detail.value
    if (query.value.startDate && query.value.endDate) {
        paging.value.reload()
    }
}
function changeEndTime(time) {
    query.value.endDate = time.detail.value
    if (query.value.startDate && query.value.endDate) {
        paging.value.reload()
    }
}
</script>

<style lang="scss" scoped>
.release_record {
    min-height: 100vh;
    background: $uni-bg-color-grey;
    .date_time {
        background: $uni-bg-color;
        padding: 30rpx;
        .title {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            line-height: 34rpx;
            margin-bottom: 10rpx;
        }
        .select_time {
            display: flex;
            align-items: center;
            .connect {
                padding: 0 10rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #666666;
                line-height: 40rpx;
            }
            .time_input {
                background: $uni-bg-color-grey;
                height: 80rpx;
                width: 320rpx;
                flex: 1;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 80rpx;
                text-align: center;
            }
        }
    }
    .record_list {
        margin: 20rpx 30rpx;
        .record_item {
            padding: 30rpx 30rpx 0 30rpx;
            background: $uni-bg-color;
            margin-bottom: 20rpx;
            box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
            border-radius: 20rpx;
            .record_item_box {
                display: flex;
                flex-direction: column;
                padding-bottom: 40rpx;
                .title_box {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .title {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #666666;
                    line-height: 34rpx;
                    margin-bottom: 10rpx;
                }
                .value {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #333333;
                    line-height: 40rpx;
                }
                .title_value {
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                }
                .show_text {
                    white-space: pre-wrap;
                }
            }
        }
    }
}
</style>
