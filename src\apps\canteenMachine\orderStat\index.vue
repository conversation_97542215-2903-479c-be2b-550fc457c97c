<template>
    <div>
        <z-paging ref="paging" class="container_box" v-model="state.dataList" :auto="false" @query="queryList">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" v-if="checkPlatform() !== 'dingding'" left-icon="left" title="费用统计" :border="false" fixed statusBar @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" />
                <view class="header">
                    <view class="left">
                        <view class="left_time">
                            <view class="arrow_btn" v-if="timeType == 'week'">
                                <uni-icons type="left" size="20" color="#333" @click="handleWeek('prev')"></uni-icons>
                            </view>
                            <div class="datetime">
                                <text v-if="isWithinThisWeek">本周</text>
                                <text v-else>
                                    {{ splitTime(state.startTime) + " - " + splitTime(state.endTime) }}
                                </text>
                            </div>
                            <view class="arrow_btn" v-if="timeType == 'week'">
                                <uni-icons type="right" size="20" color="#333" @click="handleWeek('next')"></uni-icons>
                            </view>
                            <view class="delete_icon" @click="backWeek" v-if="timeType != 'week'">
                                <uni-icons type="close" size="20" style="color: #999"></uni-icons>
                            </view>
                        </view>
                        <!-- <view class="arrow_btn" @click="handleMonth('prev')">
                        <uni-icons type="left" size="20" color="#333"></uni-icons>
                        上一月
                    </view>
                    <text class="current_month">{{ dateCover(state.startTime, "YYYY年MM月") }}</text>
                    <view class="arrow_btn" @click="handleMonth('next')">
                        下一月
                        <uni-icons type="right" size="20" color="#333"></uni-icons>
                    </view> -->
                    </view>
                    <view class="right">
                        <view class="order_box calendars" @click="calendars.open()">筛选</view>
                        <view class="order_box" @click="handleOrder">订餐统计</view>
                    </view>
                </view>

                <view class="table_header">
                    <view>预定就餐日期</view>
                    <view>类型</view>
                    <view>就餐状态</view>
                    <view>金额(元)</view>
                </view>
            </template>

            <view class="main">
                <view class="table_item" v-for="(item, index) in state.dataList" :key="index">
                    <view>{{ dateCover(item.orderDate) }}</view>
                    <view style="color: var(--primary-color)" @click="handleClick(item)">{{ item.mealSetTypeName }}</view>
                    <view :style="{ color: item.isServing ? '#BDBDBD' : '#333333' }">{{ item.isServing ? "已就餐" : "未就餐" }}</view>
                    <view>{{ toFixed(item.totalPrice) }}</view>
                </view>
            </view>
            <template #empty>
                <view class="not_set">
                    <img class="image" src="https://alicdn.1d1j.cn/announcement/20230706/b38b5f559f77415aa3bedebe883e09dd.png" alt="" />
                    <text class="text">暂无数据</text>
                </view>
            </template>

            <template #bottom>
                <view class="footer">
                    <view class="selected_text">合计：</view>
                    <view class="total_price"
                        >¥<text style="font-size: 42rpx">
                            {{ toFixed(state.payInfo.totalPrice) }}
                        </text>
                    </view>
                    <view class="nav_buttons">
                        <button class="nav_btn" v-if="state.payInfo.needPay" @click="handlePayment">确定缴纳</button>
                    </view>
                </view>
            </template>
        </z-paging>
        <view class="calendar_box">
            <uv-calendars ref="calendars" color="#00D190" :clearDate="false" confirmColor="#00D199" :allowSameDay="true" mode="range" @confirm="confirm" />
        </view>
    </div>
</template>
<script setup>
import { checkPlatform } from "@/utils/sendAppEvent.js"
import dayjs from "dayjs"
import isoWeek from "dayjs/plugin/isoWeek"
import useStore from "@/store"

const { user } = useStore()
const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

dayjs.extend(isoWeek)
const paging = ref(null)
const timeType = ref("week") // week: 以周切换， optional：任选

const calendars = ref(null)
const state = reactive({
    dataList: [],
    payInfo: {},
    startTime: "",
    endTime: "",
    studentId: null
})

const handleClick = (item) => {
    navigateTo({
        url: "/apps/canteenMachine/detail/index",
        query: {
            id: item.id,
            studentId: state.studentId
        }
    })
}

const splitTime = computed(() => {
    return (dateStr) => {
        if (dateStr) {
            const parts = dateStr.split("-")
            return `${parts[1]}.${parts[2]}`
        } else {
            return dateStr || ""
        }
    }
})

const queryList = () => {
    const params = {
        userId: identityType.value == "eltern" ? state.studentId : null,
        userType: identityType.value == "eltern" ? 0 : 1,
        startTime: state.startTime || "",
        endTime: state.endTime || ""
    }
    http.post("/app/canteen/canteenStudentOrder/canteenStudentOrderCount", params)
        .then((res) => {
            paging.value.setLocalPaging(res.data)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

// 获取订单支付信息
const getStudentPayment = () => {
    const params = {
        userId: identityType.value == "eltern" ? state.studentId : null,
        userType: identityType.value == "eltern" ? 0 : 1,
        startTime: state.startTime || "",
        endTime: state.endTime || ""
    }
    http.post("/app/canteen/canteenStudentOrder/studentCostPayment", params).then((res) => {
        state.payInfo = res.data
    })
}

// 月切换
const handleMonth = (val) => {
    state.startTime = dayjs(state.startTime)
        .add(val === "prev" ? -1 : 1, "month")
        .format("YYYY-MM-DD")
    paging.value.reload()
    getStudentPayment()
}

function toFixed(num) {
    if (num == null || num == "") return "0.00"
    return parseFloat(num).toFixed(2)
}

function dateCover(date, type = "MM月DD日") {
    if (!date) return "-"
    return dayjs(date).format(type)
}

const handleOrder = () => {
    navigateTo({
        url: "/apps/canteenMachine/orderCanteen/index",
        query: {
            studentId: state.studentId
        }
    })
}

const isWithinThisWeek = computed(() => {
    const startDayStr = state.startTime
    const endDayStr = state.endTime
    const { startDay, endDay } = getStartAndEndOfWeek()
    return startDayStr == startDay && endDayStr == endDay
})

function getLastWeekStartAndEnd(startDayStr) {
    // 解析输入的开始日期
    const startDay = dayjs(startDayStr)

    // 确定本周周一（即本周的开始）
    let currentStartDay = startDay.startOf("isoWeek") // ISO week starts from Monday

    // 计算上周的开始和结束时间
    let lastStartDay = currentStartDay.subtract(1, "week")
    let lastEndDay = lastStartDay.endOf("isoWeek")

    return {
        startDay: lastStartDay.format("YYYY-MM-DD"),
        endDay: lastEndDay.format("YYYY-MM-DD")
    }
}

function getNextWeekStartAndEnd(endDayStr) {
    // 解析输入的结束日期
    const endDay = dayjs(endDayStr)

    // 确定本周周一（即本周的开始）
    let currentStartDay = endDay.startOf("isoWeek") // ISO week starts from Monday

    // 计算下周的开始和结束时间
    let nextStartDay = currentStartDay.add(1, "week")
    let nextEndDay = nextStartDay.endOf("isoWeek")

    return {
        startDay: nextStartDay.format("YYYY-MM-DD"),
        endDay: nextEndDay.format("YYYY-MM-DD")
    }
}

const handleWeek = (type) => {
    if (type == "next") {
        const { startDay, endDay } = getNextWeekStartAndEnd(state.endTime)
        state.startTime = startDay || ""
        state.endTime = endDay || ""
    } else {
        const { startDay, endDay } = getLastWeekStartAndEnd(state.startTime)
        state.startTime = startDay || ""
        state.endTime = endDay || ""
    }
    paging.value.reload()
    getStudentPayment()
}

const handlePayment = () => {
    if (state.payInfo.payeeWay == null) {
        return uni.showToast({
            title: "请先配置支付方式",
            icon: "none"
        })
    }
    // 二维码支付
    if (state.payInfo.payeeWay == 1) {
        navigateTo({
            url: "/apps/canteenMachine/codePayment/index",
            query: {
                ...state.payInfo,
                studentId: state.studentId
            }
        })
    } else {
        // 校易付支付
        navigateTo({
            url: "/apps/canteenMachine/orderPayment/index",
            query: {
                ...state.payInfo,
                payType: state.payeeType,
                title: `${dateCover(state.startTime, "YYYY.MM")}月订餐缴费`,
                studentId: state.studentId
            }
        })
    }
}

const back = () => {
    uni.navigateBack()
}

// 获取本周的开始和结束时间
function getStartAndEndOfWeek() {
    let now = dayjs() // 获取当前时间
    let startDay = now.startOf("week") // 默认 week 开始于星期天
    // 调整到以周一为一周的开始
    if (startDay.day() !== 1) {
        // 如果当前不是周一
        startDay = startDay.add(1, "day").startOf("day") // 调整至周一的开始
    }
    let endDay = startDay.clone().add(6, "day").endOf("day") // 结束时间为下周的前一天结束时
    return {
        startDay: startDay.format("YYYY-MM-DD"),
        endDay: endDay.format("YYYY-MM-DD")
    }
}

const confirm = (e) => {
    state.startTime = e.range.before || ""
    state.endTime = e.range.after || ""
    timeType.value = "optional"
    paging.value.reload()
    getStudentPayment()
}

const backWeek = () => {
    const { startDay, endDay } = getStartAndEndOfWeek()
    state.startTime = startDay || ""
    state.endTime = endDay || ""
    timeType.value = "week"
    paging.value.reload()
    getStudentPayment()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.studentId = options.studentId
    nextTick(() => {
        const { startDay, endDay } = getStartAndEndOfWeek()
        state.startTime = startDay || ""
        state.endTime = endDay || ""
        paging.value.reload()
        getStudentPayment()
    })
})
</script>
<style lang="scss">
.calendar_box {
    .uv-toolbar__wrapper__confirm {
        color: var(--primary-color) !important;
    }

    .uv-calendar-item__weeks-box-item {
        .uv-calendar-item--isDay-text {
            color: var(--primary-color) !important;
        }
    }
}
</style>
<style lang="scss" scoped>
.calendar_box {
    .uv-calendar-item__weeks-box-item {
        .uv-calendar-item--isDay-text {
            color: var(--primary-color) !important;
        }
    }
}

.container_box {
    .header {
        display: flex;
        padding: 30rpx;
        background: #fff;

        .left {
            display: flex;
            flex: 1;
            justify-content: space-between;
            align-items: center;

            .left_time {
                justify-content: space-between;
                display: flex;
                align-items: center;
            }

            .arrow_btn {
            }

            .current_month {
            }

            .datetime {
                font-weight: 600;
                font-size: 14px;
                color: #333333;
                line-height: 27px;
                margin: 0px 4px;
            }
        }

        .right {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .order_box {
                min-width: 64px;
                border-radius: 20rpx;
                background: #8aeacc;
                color: var(--primary-color);
                font-size: 24rpx;
                line-height: 40rpx;
                text-align: center;
            }

            .calendars {
                min-width: 50px;
                background: #faf0e6;
                color: #ff9e00;
                margin-right: 12px;
            }
        }
    }

    .table_header {
        margin-top: 20rpx;
        padding: 30rpx;
        background: #fff;
        font-weight: 600;
        display: grid;
        grid-template-columns: 240rpx 1fr 1fr 1fr;
    }

    .main {
        padding: 0 30rpx;
        background: #fff;

        .table_item {
            display: grid;
            grid-template-columns: 240rpx 1fr 1fr 1fr;
            font-size: 26rpx;
            border-bottom: 2rpx solid #d9d9d9;
            font-size: #333;
            padding: 24rpx 0;
        }
    }

    .not_set {
        height: calc(100vh - 502rpx);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .image {
            width: 360rpx;
            height: 210rpx;
        }

        .text {
            font-size: 26rpx;
            font-weight: 400;
            color: #8c8c8c;
            line-height: 36rpx;
            padding-top: 30rpx;
        }
    }

    .footer {
        flex-shrink: 0;
        padding: 20rpx 30rpx;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #f5f5f5;

        .selected_text {
            font-size: 14px;
            color: #999999;
        }

        .total_price {
            font-size: 28rpx;
            color: #ff1c1c;
            font-weight: 600;
            flex: 1;
        }

        .nav_buttons {
            display: flex;
            gap: 20rpx;

            .nav_btn {
                font-size: 14px;
                padding: 0 40rpx;
                height: 70rpx;
                border-radius: 40rpx;
                background-color: #00c389;
                color: #ffffff;
                line-height: 72rpx;
            }
        }
    }
}
</style>
