import { defineStore } from "pinia"

const useCollectTable = defineStore("officialDocs", {
    state: () => {
        return {
            docs: {
                formListData: {},
                submitFormData: {},
                params: {
                    title: ""
                }
            }
        }
    },
    getters: {
        getFormListData(state) {
            return state.docs.formListData
        },
        // 提交表单
        submitFormData(state) {
            return state.docs.submitFormData
        },
        getDocsParams(state) {
            return state.docs.params
        }
    },
    actions: {
        setFormListData(item) {
            this.docs.formListData = item
        },
        // 修改提交表单
        setSubmitFormListKey(key, item) {
            this.docs.submitFormData[key] = item
        },
        // 清掉所有
        setSubmitFormClearKey() {
            this.docs.formListData = {}
            this.docs.submitFormData = {}
        },
        setSubmitFormClear() {
            this.docs.submitFormData = {}
        },
        setDocsParams(item) {
            this.docs.params = item
        }
    },

    persist: {
        key: "yd-mobile-officialDocs",
        paths: ["docs"],
        debug: import.meta.env.VITE_USER_NODE_ENV === "production",
        beforeRestore: (ctx) => {
            console.log(`beforeRestore '${ctx.store.$id}'`)
        },
        afterRestore: (ctx) => {
            console.log(`afterRestore '${ctx.store.$id}'`)
        }
    }
})

export default useCollectTable
